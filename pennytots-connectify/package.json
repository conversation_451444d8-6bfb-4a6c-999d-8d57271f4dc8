{"name": "connectify-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.80.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "date-fns": "^4.1.0", "emoji-mart": "^5.6.0", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-international-phone": "^4.5.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}
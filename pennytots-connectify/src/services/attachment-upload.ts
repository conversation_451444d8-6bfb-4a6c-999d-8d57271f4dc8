import { Axios } from '@/api/axios';

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface AttachmentUploadResult {
  success: boolean;
  data?: any;
  error?: string;
  messageId?: string;
}

export type AttachmentType = 'image' | 'video' | 'audio' | 'document' | 'contact';

export interface AttachmentUploadOptions {
  chatId: string;
  file?: File;
  message?: string;
  type: AttachmentType;
  chatType?: 'chat' | 'group';
  contactData?: any; // For contact attachments
  onProgress?: (progress: UploadProgress) => void;
  onSuccess?: (result: AttachmentUploadResult) => void;
  onError?: (error: string) => void;
}

class AttachmentUploadService {
  private activeUploads: Map<string, XMLHttpRequest> = new Map();

  /**
   * Upload an image attachment
   */
  async uploadImage(options: AttachmentUploadOptions): Promise<AttachmentUploadResult> {
    if (!options.file) {
      throw new Error('File is required for image upload');
    }

    return this.uploadFile({
      ...options,
      type: 'image',
      endpoint: `/chats/upload-image-attachment/${options.chatId}`
    });
  }

  /**
   * Upload a video attachment
   */
  async uploadVideo(options: AttachmentUploadOptions): Promise<AttachmentUploadResult> {
    if (!options.file) {
      throw new Error('File is required for video upload');
    }

    return this.uploadFile({
      ...options,
      type: 'video',
      endpoint: `/chats/upload-video-attachment/${options.chatId}`
    });
  }

  /**
   * Upload an audio attachment
   */
  async uploadAudio(options: AttachmentUploadOptions): Promise<AttachmentUploadResult> {
    if (!options.file) {
      throw new Error('File is required for audio upload');
    }

    return this.uploadFile({
      ...options,
      type: 'audio',
      endpoint: `/chats/upload-audio-attachment/${options.chatId}`
    });
  }

  /**
   * Upload a document attachment
   */
  async uploadDocument(options: AttachmentUploadOptions): Promise<AttachmentUploadResult> {
    if (!options.file) {
      throw new Error('File is required for document upload');
    }

    return this.uploadFile({
      ...options,
      type: 'document',
      endpoint: `/chats/upload-document-attachment/${options.chatId}`
    });
  }

  /**
   * Upload a contact attachment
   */
  async uploadContact(options: AttachmentUploadOptions): Promise<AttachmentUploadResult> {
    if (!options.contactData) {
      throw new Error('Contact data is required for contact upload');
    }

    try {
      const formData = new FormData();
      formData.append('message', options.message || '');
      formData.append('type', options.chatType || 'chat');
      formData.append('contact', JSON.stringify(options.contactData));

      const response = await Axios.post(
        `/chats/upload-contact-attachment/${options.chatId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      const result: AttachmentUploadResult = {
        success: true,
        data: response.data,
      };

      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Upload failed';
      
      if (options.onError) {
        options.onError(errorMessage);
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Generic file upload method
   */
  private async uploadFile(options: AttachmentUploadOptions & { endpoint: string }): Promise<AttachmentUploadResult> {
    return new Promise((resolve) => {
      const formData = new FormData();
      formData.append('file', options.file!);
      formData.append('message', options.message || '');
      formData.append('type', options.chatType || 'chat');

      const xhr = new XMLHttpRequest();
      const uploadId = `${options.chatId}-${Date.now()}`;
      
      // Store the request for potential cancellation
      this.activeUploads.set(uploadId, xhr);

      // Handle upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && options.onProgress) {
          const progress: UploadProgress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100),
          };
          options.onProgress(progress);
        }
      });

      // Handle completion
      xhr.addEventListener('load', () => {
        this.activeUploads.delete(uploadId);

        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            const result: AttachmentUploadResult = {
              success: true,
              data: response,
            };

            if (options.onSuccess) {
              options.onSuccess(result);
            }

            resolve(result);
          } catch (error) {
            const errorResult: AttachmentUploadResult = {
              success: false,
              error: 'Failed to parse response',
            };

            if (options.onError) {
              options.onError(errorResult.error!);
            }

            resolve(errorResult);
          }
        } else {
          let errorMessage = 'Upload failed';
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            errorMessage = errorResponse.message || errorMessage;
          } catch (e) {
            // Use default error message
          }

          const errorResult: AttachmentUploadResult = {
            success: false,
            error: errorMessage,
          };

          if (options.onError) {
            options.onError(errorMessage);
          }

          resolve(errorResult);
        }
      });

      // Handle errors
      xhr.addEventListener('error', () => {
        this.activeUploads.delete(uploadId);
        
        const errorResult: AttachmentUploadResult = {
          success: false,
          error: 'Network error occurred',
        };

        if (options.onError) {
          options.onError(errorResult.error!);
        }

        resolve(errorResult);
      });

      // Handle abort
      xhr.addEventListener('abort', () => {
        this.activeUploads.delete(uploadId);
        
        const errorResult: AttachmentUploadResult = {
          success: false,
          error: 'Upload cancelled',
        };

        resolve(errorResult);
      });

      // Get the token for authorization
      const token = localStorage.getItem('token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      // Start the upload
      xhr.open('POST', `${process.env.NEXT_PUBLIC_API_URL}${options.endpoint}`);
      xhr.send(formData);
    });
  }

  /**
   * Cancel an active upload
   */
  cancelUpload(chatId: string): void {
    for (const [uploadId, xhr] of this.activeUploads.entries()) {
      if (uploadId.startsWith(chatId)) {
        xhr.abort();
        this.activeUploads.delete(uploadId);
      }
    }
  }

  /**
   * Cancel all active uploads
   */
  cancelAllUploads(): void {
    for (const xhr of this.activeUploads.values()) {
      xhr.abort();
    }
    this.activeUploads.clear();
  }

  /**
   * Get file type from file extension
   */
  getFileType(file: File): AttachmentType {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    if (!extension) return 'document';

    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a'];

    if (imageExtensions.includes(extension)) return 'image';
    if (videoExtensions.includes(extension)) return 'video';
    if (audioExtensions.includes(extension)) return 'audio';
    
    return 'document';
  }

  /**
   * Validate file size and type
   */
  validateFile(file: File, maxSize: number = 10 * 1024 * 1024): { valid: boolean; error?: string } {
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds ${Math.round(maxSize / (1024 * 1024))}MB limit`,
      };
    }

    return { valid: true };
  }
}

// Export a singleton instance
export const attachmentUploadService = new AttachmentUploadService();
export default attachmentUploadService;

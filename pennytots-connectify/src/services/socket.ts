'use client';

import { io, Socket } from 'socket.io-client';

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(token: string, serverUrl: string = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001') {
    if (this.socket?.connected) {
      return this.socket;
    }

    const connectionOptions = {
      forceNew: true,
      reconnectionAttempts: Infinity,
      timeout: 100000,
      transports: ['websocket'],
      auth: {
        token: token,
      },
    };

    this.socket = io(serverUrl, connectionOptions);

    this.socket.on('connect', () => {
      console.log('Socket connected successfully');
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect(token, serverUrl);
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.handleReconnect(token, serverUrl);
    });

    return this.socket;
  }

  private handleReconnect(token: string, serverUrl: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(token, serverUrl);
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.reconnectAttempts = 0;
    }
  }

  getSocket() {
    return this.socket;
  }

  isSocketConnected() {
    return this.isConnected && this.socket?.connected;
  }

  // Chat-specific methods
  joinChat(chatId: string, accountId: string, callback?: (response: any) => void) {
    if (this.socket) {
      this.socket.emit('join-chat', { chatId, accountId }, callback);
    }
  }

  joinGroup(groupId: string, callback?: (response: any) => void) {
    if (this.socket) {
      this.socket.emit('join-group', groupId, callback);
    }
  }

  sendPrivateMessage(messageData: any, callback?: (response: any) => void) {
    if (this.socket) {
      this.socket.emit('Private Chat', messageData, callback);
    }
  }

  sendGroupMessage(messageData: any, callback?: (response: any) => void) {
    if (this.socket) {
      this.socket.emit('Group Chat', messageData, callback);
    }
  }

  // Event listeners
  onPrivateMessage(callback: (message: any) => void) {
    if (this.socket) {
      this.socket.on('Private Chat', callback);
    }
  }

  onGroupMessage(callback: (message: any) => void) {
    if (this.socket) {
      this.socket.on('Group Chat', callback);
    }
  }

  onMessageUpdate(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('message-update', callback);
    }
  }

  onUserTyping(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('user-typing', callback);
    }
  }

  onUserStoppedTyping(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('user-stopped-typing', callback);
    }
  }

  // Remove event listeners
  offPrivateMessage() {
    if (this.socket) {
      this.socket.off('Private Chat');
    }
  }

  offGroupMessage() {
    if (this.socket) {
      this.socket.off('Group Chat');
    }
  }

  offMessageUpdate() {
    if (this.socket) {
      this.socket.off('message-update');
    }
  }

  offUserTyping() {
    if (this.socket) {
      this.socket.off('user-typing');
    }
  }

  offUserStoppedTyping() {
    if (this.socket) {
      this.socket.off('user-stopped-typing');
    }
  }

  // Typing indicators
  emitTyping(chatId: string, isGroup: boolean = false) {
    if (this.socket) {
      const event = isGroup ? 'group-typing' : 'private-typing';
      this.socket.emit(event, { chatId });
    }
  }

  emitStoppedTyping(chatId: string, isGroup: boolean = false) {
    if (this.socket) {
      const event = isGroup ? 'group-stopped-typing' : 'private-stopped-typing';
      this.socket.emit(event, { chatId });
    }
  }
}

// Create a singleton instance
const socketService = new SocketService();

export default socketService;

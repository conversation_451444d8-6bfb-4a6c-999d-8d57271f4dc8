import { Notification, NotificationType } from '@/components/profile/ProfileNotifications';

export interface ToastOptions {
  duration?: number;
  persistent?: boolean;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center';
}

export interface ChatToastMessages {
  chatCreated: (userName: string) => string;
  chatCreationFailed: (userName: string, error?: string) => string;
  messageSent: () => string;
  messageSendFailed: (error?: string) => string;
  attachmentUploading: (fileName: string) => string;
  attachmentUploaded: (fileName: string) => string;
  attachmentUploadFailed: (fileName: string, error?: string) => string;
  userTyping: (userName: string) => string;
  connectionLost: () => string;
  connectionRestored: () => string;
  chatBlocked: (userName: string) => string;
  chatUnblocked: (userName: string) => string;
  userOffline: (userName: string) => string;
  userOnline: (userName: string) => string;
}

class ToastService {
  private toastHandler: ((notification: Omit<Notification, 'id'>) => void) | null = null;

  // Set the toast handler (usually from useProfileNotifications)
  setToastHandler(handler: (notification: Omit<Notification, 'id'>) => void) {
    this.toastHandler = handler;
  }

  // Generic toast method
  private showToast(
    type: NotificationType,
    title: string,
    message: string,
    options: ToastOptions = {}
  ) {
    if (!this.toastHandler) {
      console.warn('Toast handler not set. Call setToastHandler first.');
      return;
    }

    this.toastHandler({
      type,
      title,
      message,
      duration: options.duration || (type === 'error' ? 5000 : 3000),
      persistent: options.persistent || false,
    });
  }

  // Convenience methods
  success(title: string, message: string, options?: ToastOptions) {
    this.showToast('success', title, message, options);
  }

  error(title: string, message: string, options?: ToastOptions) {
    this.showToast('error', title, message, { duration: 5000, ...options });
  }

  warning(title: string, message: string, options?: ToastOptions) {
    this.showToast('warning', title, message, options);
  }

  info(title: string, message: string, options?: ToastOptions) {
    this.showToast('info', title, message, options);
  }

  // Chat-specific toast messages
  chatMessages: ChatToastMessages = {
    chatCreated: (userName: string) => `Chat started with ${userName}`,
    chatCreationFailed: (userName: string, error?: string) => 
      `Failed to start chat with ${userName}${error ? `: ${error}` : ''}`,
    messageSent: () => 'Message sent',
    messageSendFailed: (error?: string) => 
      `Failed to send message${error ? `: ${error}` : ''}`,
    attachmentUploading: (fileName: string) => `Uploading ${fileName}...`,
    attachmentUploaded: (fileName: string) => `${fileName} uploaded successfully`,
    attachmentUploadFailed: (fileName: string, error?: string) => 
      `Failed to upload ${fileName}${error ? `: ${error}` : ''}`,
    userTyping: (userName: string) => `${userName} is typing...`,
    connectionLost: () => 'Connection lost. Trying to reconnect...',
    connectionRestored: () => 'Connection restored',
    chatBlocked: (userName: string) => `Blocked ${userName}`,
    chatUnblocked: (userName: string) => `Unblocked ${userName}`,
    userOffline: (userName: string) => `${userName} is offline`,
    userOnline: (userName: string) => `${userName} is online`,
  };

  // Chat-specific methods
  chatCreated(userName: string, options?: ToastOptions) {
    this.success('Chat Started', this.chatMessages.chatCreated(userName), options);
  }

  chatCreationFailed(userName: string, error?: string, options?: ToastOptions) {
    this.error('Chat Failed', this.chatMessages.chatCreationFailed(userName, error), options);
  }

  messageSent(options?: ToastOptions) {
    this.success('Message Sent', this.chatMessages.messageSent(), { duration: 2000, ...options });
  }

  messageSendFailed(error?: string, options?: ToastOptions) {
    this.error('Message Failed', this.chatMessages.messageSendFailed(error), options);
  }

  attachmentUploading(fileName: string, options?: ToastOptions) {
    this.info('Uploading', this.chatMessages.attachmentUploading(fileName), { 
      persistent: true, 
      ...options 
    });
  }

  attachmentUploaded(fileName: string, options?: ToastOptions) {
    this.success('Upload Complete', this.chatMessages.attachmentUploaded(fileName), options);
  }

  attachmentUploadFailed(fileName: string, error?: string, options?: ToastOptions) {
    this.error('Upload Failed', this.chatMessages.attachmentUploadFailed(fileName, error), options);
  }

  userTyping(userName: string, options?: ToastOptions) {
    this.info('Typing', this.chatMessages.userTyping(userName), { 
      duration: 1000, 
      ...options 
    });
  }

  connectionLost(options?: ToastOptions) {
    this.warning('Connection Lost', this.chatMessages.connectionLost(), { 
      persistent: true, 
      ...options 
    });
  }

  connectionRestored(options?: ToastOptions) {
    this.success('Connected', this.chatMessages.connectionRestored(), options);
  }

  chatBlocked(userName: string, options?: ToastOptions) {
    this.warning('User Blocked', this.chatMessages.chatBlocked(userName), options);
  }

  chatUnblocked(userName: string, options?: ToastOptions) {
    this.success('User Unblocked', this.chatMessages.chatUnblocked(userName), options);
  }

  userOffline(userName: string, options?: ToastOptions) {
    this.info('User Status', this.chatMessages.userOffline(userName), options);
  }

  userOnline(userName: string, options?: ToastOptions) {
    this.success('User Status', this.chatMessages.userOnline(userName), options);
  }

  // Batch operations
  showMultiple(notifications: Array<{
    type: NotificationType;
    title: string;
    message: string;
    options?: ToastOptions;
  }>) {
    notifications.forEach(({ type, title, message, options }) => {
      this.showToast(type, title, message, options);
    });
  }

  // Clear all toasts (if the handler supports it)
  clearAll() {
    // This would need to be implemented in the toast handler
    console.log('Clear all toasts requested');
  }
}

// Export singleton instance
export const toastService = new ToastService();
export default toastService;

// Hook for easy integration with components
export const useChatToasts = () => {
  return {
    chatCreated: toastService.chatCreated.bind(toastService),
    chatCreationFailed: toastService.chatCreationFailed.bind(toastService),
    messageSent: toastService.messageSent.bind(toastService),
    messageSendFailed: toastService.messageSendFailed.bind(toastService),
    attachmentUploading: toastService.attachmentUploading.bind(toastService),
    attachmentUploaded: toastService.attachmentUploaded.bind(toastService),
    attachmentUploadFailed: toastService.attachmentUploadFailed.bind(toastService),
    userTyping: toastService.userTyping.bind(toastService),
    connectionLost: toastService.connectionLost.bind(toastService),
    connectionRestored: toastService.connectionRestored.bind(toastService),
    chatBlocked: toastService.chatBlocked.bind(toastService),
    chatUnblocked: toastService.chatUnblocked.bind(toastService),
    userOffline: toastService.userOffline.bind(toastService),
    userOnline: toastService.userOnline.bind(toastService),
  };
};

// Helper to format file names for toasts
export const formatFileName = (fileName: string, maxLength: number = 20): string => {
  if (fileName.length <= maxLength) return fileName;
  
  const extension = fileName.split('.').pop();
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension!.length - 4);
  
  return `${truncatedName}...${extension}`;
};

// Helper to get user display name
export const getUserDisplayName = (user: { first_name?: string; last_name?: string; firstName?: string; lastName?: string }): string => {
  const firstName = user.firstName || user.first_name || '';
  const lastName = user.lastName || user.last_name || '';
  return `${firstName} ${lastName}`.trim() || 'Unknown User';
};

import localFont from 'next/font/local'

export const poppins = localFont({
    src: "/assets/fonts/Poppins-Regular.ttf",
    display: "swap",
    variable: "--font-poppins-regular",
  });

  export const poppinsItalic = localFont({
    src: "/assets/fonts/Poppins-Italic.ttf",
    display: "swap",
    variable: "--font-poppins-italic",
  });

  export const poppinsSemibold = localFont({
    src: "/assets/fonts/Poppins-SemiBold.ttf",
    display: "swap",
    variable: "--font-poppins-regular",
  });

    export const poppinsMedium = localFont({
    src: "/assets/fonts/Poppins-Medium.ttf",
    display: "swap",
    variable: "--font-poppins-regular",
  });

  
    export const poppinsRegular = localFont({
    src: "/assets/fonts/Poppins-Regular.ttf",
    display: "swap",
    variable: "--font-poppins-regular",
  });

    export const montserratMedium = localFont({
    src: "/assets/fonts/Montserrat-Medium.ttf",
    display: "swap",
    variable: "--font-montserrat-regular",
  });

import "./globals.css";
import { Providers } from "./providers";

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';



export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={``}
      >
        <Providers>
          {children}
        </Providers>
        <ToastContainer />
      </body>
    </html>
  );
}

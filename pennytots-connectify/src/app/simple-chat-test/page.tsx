// 'use client';

// import React, { useState } from 'react';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { ArrowLeft } from 'lucide-react';
// import { useRouter } from 'next/navigation';

// const SimpleChatTest: React.FC = () => {
//   const [messages, setMessages] = useState<string[]>([]);
//   const [inputValue, setInputValue] = useState('');
//   const router = useRouter();

//   const handleSendMessage = () => {
//     if (inputValue.trim()) {
//       setMessages(prev => [...prev, inputValue.trim()]);
//       setInputValue('');
//     }
//   };

//   const handleKeyPress = (e: React.KeyboardEvent) => {
//     if (e.key === 'Enter') {
//       handleSendMessage();
//     }
//   };

//   return (
//     <div className="flex flex-col h-screen bg-gray-50">
//       {/* Header */}
//       <div className="flex items-center gap-3 p-4 bg-white border-b">
//         <Button variant="ghost" size="sm" onClick={() => router.back()}>
//           <ArrowLeft className="h-4 w-4" />
//         </Button>
//         <h1 className="text-lg font-semibold">Simple Chat Test</h1>
//       </div>

//       {/* Messages Area */}
//       <div className="flex-1 overflow-y-auto p-4">
//         {messages.length === 0 ? (
//           <div className="text-center text-gray-500 py-8">
//             <p>No messages yet. Type something below to test!</p>
//           </div>
//         ) : (
//           <div className="space-y-2">
//             {messages.map((message, index) => (
//               <div key={index} className="bg-blue-500 text-white p-3 rounded-lg max-w-xs ml-auto">
//                 {message}
//               </div>
//             ))}
//           </div>
//         )}
//       </div>

//       {/* Input Area */}
//       <div className="bg-white border-t p-4">
//         <div className="flex items-center gap-2">
//           <input
//             type="text"
//             value={inputValue}
//             onChange={(e) => setInputValue(e.target.value)}
//             onKeyPress={handleKeyPress}
//             placeholder="Type a message..."
//             className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
//           />
//           <Button onClick={handleSendMessage} disabled={!inputValue.trim()}>
//             Send
//           </Button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default SimpleChatTest;

'use client';

import React from 'react';
import { useParams } from 'next/navigation';
import MainLayout from '@/components/layout/main-layout';
import CommentItem from '@/components/screens/commentItem';

export default function TopicViewPage() {
  const params = useParams();
  const topicId = params.id as string;

  return (
    <MainLayout activeScreen="home">
      <CommentItem topicId={topicId} />
    </MainLayout>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useGetQuestions, useChangeUserPennytots } from '@/redux/quiz/hooks';
import { useGetCredit } from '@/redux/credit/hooks';
import { Loader2} from 'lucide-react';
import { poppinsMedium, poppins } from '@/fonts';

interface OptionButtonProps {
  option: string;
  onPress: () => void;
  isSelected: boolean;
  isCorrect: boolean | null;
  correctAnswer: string;
  gameOver: boolean;
  canClick: boolean;
  shouldBlink: boolean;
}

const OptionButton: React.FC<OptionButtonProps> = ({
  option,
  onPress,
  isSelected,
  isCorrect,
  correctAnswer,
  gameOver,
  canClick,
  shouldBlink,
}) => {
  const getButtonStyle = () => {
    if (!gameOver) {
      return isSelected
        ? 'bg-blue-500 text-white border-blue-500'
        : 'bg-white border-[#D0BB68] border ';
    }

    if (option === correctAnswer) {
      const blinkClass = shouldBlink ? 'animate-pulse bg-green-400' : '';
      return `bg-green-500 text-white border-green-500 ${blinkClass}`;
    }

    if (isSelected && option !== correctAnswer) {
      return 'bg-red-500 text-white border-red-500';
    }

    return 'bg-gray-100 text-gray-500 border-gray-300';
  };

  return (
    <Button
      onClick={onPress}
      disabled={!canClick}
      className={`border-[#D0BB68] cursor-pointer hover:bg-[#FFC085] border py-[8px] px-[2rem] w-full rounded-[12px] transition-all duration-300 ${getButtonStyle()}`}
      variant="outline"
    >
      {option}
    </Button>
  );
};

const QuizPage = () => {
  const router = useRouter();
  const {
    data: quizData,
    isLoading,
    refetch: getNextQuestion,
    isFetching,
  } = useGetQuestions();

  const { data: credits, refetch: refetchCredits } = useGetCredit();
  const { mutate: changeUserPennytots } = useChangeUserPennytots();

  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [score, setScore] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [canClick, setCanClick] = useState(true);
  const [showResult, setShowResult] = useState(false);
  const [shouldBlink, setShouldBlink] = useState(false);
  const [autoProgressMessage, setAutoProgressMessage] = useState('');
  const [countdown, setCountdown] = useState(0);

  const { question, options, answer: correctAnswer } = quizData || {};

  const handleOptionSelect = (option: string) => {
    if (!canClick || gameOver) return;

    setSelectedOption(option);
    setCanClick(false);

    const correct = option === correctAnswer;
    setIsCorrect(correct);
    setGameOver(true);
    setShowResult(true);

    if (correct) {
      setScore(score + 1);
      // Increase PennyTots for correct answer
      changeUserPennytots('increase');
      setAutoProgressMessage('🎉 Correct! Moving to next question...');

      // Start countdown for correct answer (1.5 seconds)
      setCountdown(2);
      const correctCountdownInterval = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(correctCountdownInterval);
            handleNextQuestion();
            return 0;
          }
          return prev - 1;
        });
      }, 750); // Update every 0.75 seconds for 2 counts

    } else {
      // Reduce PennyTots for wrong answer
      changeUserPennytots('reduce');
      setAutoProgressMessage('❌ Wrong answer! The correct answer is highlighted. Moving to next question...');

      // Start blinking animation for correct answer
      setShouldBlink(true);

      // Start countdown for wrong answer (3 seconds)
      setCountdown(3);
      const wrongCountdownInterval = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(wrongCountdownInterval);
            handleNextQuestion();
            return 0;
          }
          return prev - 1;
        });
      }, 1000); // Update every second
    }

    // Refresh credits after answer
    setTimeout(() => {
      refetchCredits();
    }, 1000);
  };

  const handleNextQuestion = () => {
    setSelectedOption(null);
    setIsCorrect(null);
    setGameOver(false);
    setCanClick(true);
    setShowResult(false);
    setShouldBlink(false); // Reset blinking state
    setAutoProgressMessage(''); // Clear progress message
    setCountdown(0); // Reset countdown
    getNextQuestion();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  if (isLoading || isFetching) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin  mb-4 text-blue-500" />
          <p className="text-gray-600">Loading question...</p>
        </div>
      </div>
    );
  }



  return (
    <div className="w-full flex justify-center mt-[12px]">
      <div className="w-full">
        {/* Header */}
      

        {/* Question Card */}
        <Card className="mb-6">
          {/* <CardHeader>
            <CardTitle className={`text-[#797C7B] ${poppins.className} text-[1rem]`}>Trivia</CardTitle>
          </CardHeader> */}
          <CardContent>
            <p className={`text-[#4F4F4F] text-[14px] ${poppinsMedium.className}`}>
              {question}
            </p>

            {/* Options */}
            <div className="flex flex-col gap-[12px] mt-[4px]">
              {options?.map((option: string, index: number) => (
                <OptionButton
                  key={index}
                  option={option}
                  onPress={() => handleOptionSelect(option)}
                  isSelected={selectedOption === option}
                  isCorrect={isCorrect}
                  correctAnswer={correctAnswer}
                  gameOver={gameOver}
                  canClick={canClick}
                  shouldBlink={shouldBlink && option === correctAnswer && !isCorrect}
                />
              ))}
            </div>

            {/* Auto Progress Message */}
            {autoProgressMessage && (
              <div className="mt-4 p-3 rounded-lg bg-blue-50 border border-blue-200">
                <p className="text-sm text-blue-700 text-center font-medium">
                  {autoProgressMessage}
                  {countdown > 0 && (
                    <span className="ml-2 inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white rounded-full text-xs font-bold">
                      {countdown}
                    </span>
                  )}
                </p>
              </div>
            )}

          </CardContent>
        </Card>

      </div>
    </div>
  );
};

export default QuizPage;

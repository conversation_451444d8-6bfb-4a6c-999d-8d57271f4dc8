'use client';

import { useEffect, useState } from "react";
import { ArrowRight, LucideUnlink2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import Sidebar from "@/components/sidebar/onboarding-sidebar";
import { montserratMedium, poppinsSemibold } from "@/fonts";
import { setLoading } from "@/redux/main/reducer";

import Link from 'next/link';
import { useUpdateProfile } from "@/redux/profile/hooks";
import {useDispatch} from 'react-redux'

interface IInterest {
    _id: string;
    name: string;
}

const Company = () => {
   const [status, setStatus] = useState("");
  const [graduationYear, setGraduationYear] = useState("");
  const [isStatusDropdownVisible, setStatusDropdownVisible] = useState(false);
  const [isGraduationYearDropdown, setGraduationYearDropdown] = useState(false);
    const [error, setError] = useState('');
    const [disableButton, setDisableButton] = useState(true);
    const { updateProfile, loading: isUpdatingProfile } = useUpdateProfile();
  const dispatch = useDispatch()


    const router = useRouter();

  
//  const years = Array.from({ length: 81 }, (_, i) => ({
//     id: (1950 + i).toString(),
//     name: (1950 + i).toString(),
//   }));

//   const statusOptions = [
//     { id: "Student", name: "Student", icon: <StudentSVG /> },
//     { id: "Alumnus", name: "Alumnus", icon: <AlumniSVG /> },
//     { id: "Staff", name: "Staff", icon: <StaffSVG /> },
//   ];

  const saveFormData = async () => {
    try {
      await localStorage.setItem(
        "formData",
        JSON.stringify({ status, graduationYear })
      );
    } catch (error) {
      console.error("Saving error", error);
    }
  };

  const loadFormData = async () => {
    try {
      const stored = await localStorage.getItem("formData");
      if (stored) {
        const parsed = JSON.parse(stored);
        setStatus(parsed.status || "");
        setGraduationYear(parsed.graduationYear || "");
      }
    } catch (error) {
      console.error("Loading error", error);
    }
  };

  useEffect(() => {
    loadFormData();
  }, []);

  useEffect(() => {
    saveFormData();
  }, [status, graduationYear]);

  // const handleStatusSelect = (selected: Status) => {
  //   setStatus(selected);
  //   setStatusDropdownVisible(false);
  //   if (selected === "Staff") setGraduationYear("N/A");
  //   else setGraduationYear("");
  // };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
   dispatch(setLoading(true))
    setError(''); // Clear any previous errors

    if (!status || !graduationYear) {
      setError('Please select a field before proceeding.');
      return;
    }

    try {
      const formData = {
        graduation_status: status,
        graduation_year: graduationYear,
      };

      await updateProfile(formData, "/onboarding/interests");
    } catch (error) {
      console.error('Profile update failed:', error);
      setError('Profile update failed. Please try again.');
    }
  };




    const validateForm = () => {
        const isValidStatus = status.trim().length > 0;
        const isValidGraduationYear = graduationYear.trim().length > 0;
       

        return isValidStatus &&
            isValidGraduationYear
         

    };

    // Update button state whenever form fields change
    useEffect(() => {
        setDisableButton(!validateForm());
    }, [status, graduationYear]);

    useEffect(()=> {
      router.prefetch("onboarding/interests")
    }, [])

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, nextFieldId: string) => {
      if(e.key === 'Enter') {
        e.preventDefault()

        const nextInput = document.getElementById(nextFieldId)
        if(nextInput){
          nextInput.focus()
        }
      }
    }

    return (
        <div className="flex justify-center">


            <div className="flex  w-full max-w-[1250px] justify-between items-center">
                <Sidebar />
                <div className="w-[1px] bg-[#D9D9D9] h-[751px] flex justify-center"></div>
                <div className="w-full max-w-[454px] flex  ">
                    <div className="w-full">
                        <div className="mb-8">
                            <h1 className={`${poppinsSemibold.className} text-[#4F4F4F] text-[1.25rem]`}>Status</h1>
                            <p className={`text-[#645D5D] text-[14px] ${montserratMedium.className}`}>What`s your current status</p>
                        </div>

                        {error && (
                            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
                                {error}
                            </div>
                        )}

                        <form onSubmit={handleUpdateProfile} className="mt-[2rem] flex flex-col gap-[1rem]">

                            

                            <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="gender">Status</Label>
                                    <Input
                                    id="work"
                                    type="text"
                                    value={status}
                                    placeholder="Are you  student, alumnus or staff"
                                    onChange={(e) => setStatus(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'job')}
                                    required
                                />
                                   

                            </div>

                              <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="gender">Year of graduation</Label>
                                    <Input
                                    id="job"
                                    type="text"
                                    value={graduationYear}
                                    placeholder="What did you graduate"
                                    onChange={(e) => setGraduationYear(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'button')}
                                    disabled={isUpdatingProfile}
                                    required
                                />
                                   

                            </div>
                         
                         
                            <Button
                            id="button"
                                variant="formButton"
                                type="submit"
                                className={`w-full mt-[2rem] flex items-center justify-between ${disableButton
                                        ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                                        : 'bg-[#163E23] text-white cursor-pointer'
                                    }`}
                                disabled={disableButton}
                            >
                                  {isUpdatingProfile ? (
                                <span className="flex justify-center items-center gap-2">
                                    <svg
                                        className="animate-spin h-4 w-4 text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        ></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                                        ></path>
                                    </svg>
                                    Updating Profile...
                                </span>
                            ) : (
                                'Next'
                            )}
                           <ArrowRight width={26} height={21} className="" />

                            </Button>
                            <Button
                                variant="backButton"
                                type="submit"

                                
                            >

                                        <ArrowRight width={26} height={21} />
                                    Back
                              
                            </Button>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    );
}

export default Company;

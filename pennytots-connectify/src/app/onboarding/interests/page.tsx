'use client';

import { useEffect, useState } from "react";
import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import Sidebar from "@/components/sidebar/onboarding-sidebar";
import { montserratMedium, poppinsSemibold } from "@/fonts";
import { SelectInterests } from "@/components/elements/SelectInterests"
import { useTags } from "@/redux/tags/useTags";
import { setLoading } from "@/redux/main/reducer";
import {useDispatch} from 'react-redux'


interface IInterest {
  _id: string;
  name: string;
}

const InterestsPage = () => {
  const [interests, setInterests] = useState<string[]>([]);
  const [error, setError] = useState('');
  const [disableButton, setDisableButton] = useState(true);
 const dispatch = useDispatch()
  // Use the tags hook for updating interests
  const { loading, updateAreaOfInterests } = useTags({
    onSuccess: () => {
      console.log('Interests updated successfully!');
    },
    onError: (error) => {
      setError(error);
    },
  });

  const handleUpdateInterest = async (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(setLoading(true))

    if (interests.length === 0) {
      setError('Please select at least one interest to continue.');
      return;
    }

    setError(''); // Clear any previous errors

    // Create a welcome message for the user
    const welcomeMessage = "Hello everyone! I just joined our amazing network. Nice to be here!";

    // Update interests and create welcome topic
    await updateAreaOfInterests(interests, welcomeMessage);
    console.log(updateAreaOfInterests, 'interests')
    

  };

  // Update button state whenever interests change
  useEffect(() => {
    setDisableButton(interests.length === 0);
  }, [interests]);

  useEffect(() => {
  console.log('🔥 useTags loading:', loading);
}, [loading]);


  return (
    <div className="flex justify-center">


      <div className="flex  w-full max-w-[1250px] justify-between items-center">
        <Sidebar />
        <div className="w-[1px] bg-[#D9D9D9] h-[751px] flex justify-center"></div>
        <div className="w-full max-w-[454px] flex  ">
          <div className="w-full">
            <div className="mb-8">
              <h1 className={`${poppinsSemibold.className} text-[#4F4F4F] text-[1.25rem]`}>Interests</h1>
              <p className={`text-[#645D5D] text-[14px] ${montserratMedium.className}`}>Tailor your posts to match your interests</p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
                {error}
              </div>
            )}

            <form onSubmit={handleUpdateInterest} className="mt-[2rem] flex flex-col gap-[1rem]">



              <div className="flex flex-col gap-[4px]">
                <Label htmlFor="gender">I am interested in:</Label>
                <SelectInterests
                  interests={interests}
                  setInterests={setInterests}
                  multiple={true}
                />
              </div>
              <Button
                variant="formButton"
                type="submit"
                className={`w-full mt-[2rem] flex items-center justify-center ${disableButton
                  ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                  : 'bg-[#163E23] text-white cursor-pointer'
                  }`}
                disabled={disableButton}
              >  {loading ? (
                <span className="flex justify-center items-center gap-2">
                  <svg
                    className="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                  </svg>
                  Creating Account...
                </span>
              ) : (
                'Next'
              )}

                <ArrowRight width={26} height={21} className="" />
              </Button>
              <Button
                variant="backButton"
                type="submit"
              >
                <ArrowRight width={26} height={21} />
                Back
              </Button>
            </form>
          </div>
        </div>

      </div>
    </div>
  );
}

export default InterestsPage;

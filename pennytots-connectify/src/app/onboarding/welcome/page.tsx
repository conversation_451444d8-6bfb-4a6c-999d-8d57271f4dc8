'use client';

import { useEffect, useState } from "react";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import Sidebar from "@/components/sidebar/onboarding-sidebar";
import { montserratMedium, poppinsRegular, poppinsSemibold } from "@/fonts";

import Link from 'next/link';
import { userConstants } from '@/constants/userConstants';
import { useUpdateProfile } from "@/redux/profile/hooks";


interface IInterest {
    _id: string;
    name: string;
}

export default function PersonalDetailsForm() {
    const [city, setCity] = useState('');
    const [state, setState] = useState('');
    const [gender, setGender] = useState('');
    const [age, setAge] = useState('');
    const [error, setError] = useState('');
    const [disableButton, setDisableButton] = useState(true);
    const { updateProfile, loading: isUpdatingProfile } = useUpdateProfile();



    const router = useRouter();

    const saveFormData = () => {
        try {
            const formData = {
                city,
                state,
                gender,
                age,

            };
            localStorage.setItem('personalformData', JSON.stringify(formData));
        } catch (error) {
            console.error('Error saving form data', error);
        }
    };

    const loadFormData = () => {
        try {
            const storedData = localStorage.getItem('personalformData');
            if (storedData) {
                const parsedData = JSON.parse(storedData);
                setCity(parsedData.city || '');
                setState(parsedData.state || '');
                setGender(parsedData.gender || '');
                setAge(parsedData.age || '');

            }
        } catch (error) {
            console.error('Error loading form data', error);
        }
    };

    useEffect(() => {
        loadFormData();
    }, []);

    useEffect(() => {
        saveFormData();
    }, [city, state, gender, age]);



    const handleUpdateProfile = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(''); // Clear any previous errors

        // Validation
        if (!city) {
            setError('Please enter your city');
            return;
        }

        if (!state) {
            setError('Please enter a state');
            return;
        }
        if (!age) {
            setError('Age is required');
            return;
        }

        if (!gender) {
            setError('Gender is required');
            return;
        }

        try {
            const formData = {
                city,
                state,
                age,
                gender,
            };
            await updateProfile(formData, "/onboarding/personal");
            console.log(formData, 'welcome');
        } catch (error) {
            console.error('Profile update failed:', error);
            setError('Profile update failed. Please try again.');
        }
    };

    const validateForm = () => {
        const isCityValid = city.trim().length > 0;
        const isStateValid = state.trim().length > 0;
        const isAgeValid = age.trim().length >= 4; // Assuming PIN should be at least 4 characters
        const isGenderValid = gender.trim().length > 0;

        return isCityValid &&
            isStateValid &&
            isAgeValid &&
            isGenderValid

    };

    // Update button state whenever form fields change
    useEffect(() => {
        setDisableButton(!validateForm());
    }, [city, state, age, gender]);

    useEffect(() => {
        router.prefetch("onboarding/personal")
    }, [])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, nextFieldId: string) => {
      if(e.key === 'Enter') {
        e.preventDefault()

        const nextInput = document.getElementById(nextFieldId)
        if(nextInput){
          nextInput.focus()
        }
      }
    }

    return (
        <div className="flex justify-center">


            <div className="flex  w-full max-w-[1250px] justify-between items-center">
                <Sidebar />
                <div className="w-[1px] bg-[#D9D9D9] h-[751px] flex justify-center"></div>
                <div className="w-full max-w-[454px] flex  ">
                    <div className="w-full">
                        <div className="mb-8">
                            <h1 className={`${poppinsSemibold.className} text-[#4F4F4F] text-[1.25rem]`}>Tell us more about you</h1>
                            <p className={`text-[#645D5D] text-[14px] ${montserratMedium.className}`}>Provide more details about you</p>
                        </div>

                        {error && (
                            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
                                {error}
                            </div>
                        )}

                        <form onSubmit={handleUpdateProfile} className="mt-[2rem] flex flex-col gap-[1rem]">

                            <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="age">Age Range</Label>
                                <div className="relative w-full">
                                    <select
                                        id="gender"
                                        value={age}
                                        onChange={e => setAge(e.target.value)}
                                        className="appearance-none border border-[#D0D5DD] rounded p-[1rem] pr-[3rem] w-full focus:outline-none focus:ring-1 focus:ring-[#F2875D]"
                                        required
                                    >
                                        <option value="">Select Gender</option>
                                        {userConstants.ageRange.map(g => (
                                            <option key={g.value} value={g.value}>{g.label}</option>
                                        ))}
                                    </select>

                                    <div className="pointer-events-none absolute right-4 top-1/2 -translate-y-1/2 text-[#667085]">
                                        ▼
                                    </div>
                                </div>

                            </div>

                            <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="gender">Gender</Label>
                                <div className="relative w-full">
                                    <select
                                        id="gender"
                                        value={gender}
                                        onChange={e => setGender(e.target.value)}
                                        className="appearance-none border border-[#D0D5DD] rounded p-[1rem] pr-[3rem] w-full focus:outline-none focus:ring-1 focus:ring-[#F2875D]"
                                        required
                                    >
                                        <option value="">Select Gender</option>
                                        {userConstants.genders.map(g => (
                                            <option key={g.value} value={g.value}>{g.label}</option>
                                        ))}
                                    </select>

                                    <div className="pointer-events-none absolute right-4 top-1/2 -translate-y-1/2 text-[#667085]">
                                        ▼
                                    </div>
                                </div>

                            </div>

                            <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="email">Town/City</Label>
                                <Input
                                    id="city"
                                    type="text"
                                    value={city}
                                    onChange={(e) => setCity(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'state')}
                                    disabled={isUpdatingProfile}

                                    required
                                />
                            </div>



                            <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="pin">State/Province</Label>
                                <div className="relative">
                                    <Input
                                        id="state"
                                        value={state}
                                        onChange={(e) => setState(e.target.value)}
                                        onKeyDown={(e) => handleKeyDown(e, 'button')}
                                        disabled={isUpdatingProfile}
                                        required
                                    />

                                </div>
                            </div>



                            <Button
                            id="button"
                                variant="formButton"
                                type="submit"
                                className={`w-full mt-[2rem] flex items-center justify-between ${disableButton
                                    ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                                    : 'bg-[#163E23] text-white cursor-pointer'
                                    }`}
                                disabled={disableButton}
                            >
                                {isUpdatingProfile ? (
                                    <span className="flex justify-center items-center gap-2">
                                        <svg
                                            className="animate-spin h-4 w-4 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                                            ></path>
                                        </svg>
                                        Updating Profile...
                                    </span>
                                ) : (
                                    'Next'
                                )}
                                <ArrowRight width={26} height={21} className="" />

                            </Button>
                            <p className={`mt-[26px] text-center text-[#4F4F4F] text-[14px] ${poppinsRegular.className}`}>
                                Forgotten Password?
                                <Link href="/forgot-password" className="text-[#F2875D]">
                                    Recover
                                </Link>
                            </p>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    );
}

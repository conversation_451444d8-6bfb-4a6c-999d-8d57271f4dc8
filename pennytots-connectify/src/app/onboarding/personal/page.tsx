'use client';

import { useEffect, useState } from "react";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import Sidebar from "@/components/sidebar/onboarding-sidebar";
import { montserratMedium, poppinsRegular, poppinsSemibold } from "@/fonts";

import Link from 'next/link';
import { useUpdateProfile } from "@/redux/profile/hooks";
import { setLoading } from "@/redux/main/reducer";
import { useDispatch } from "react-redux";


interface IInterest {
    _id: string;
    name: string;
}

const PersonalOnboarding = () => {
    const [bio, setBio] = useState("");
    const [link1, setLink1] = useState("");
    const [link2, setLink2] = useState("");
    const [link3, setLink3] = useState("");
    const [error, setError] = useState('');
    const [disableButton, setDisableButton] = useState(true);
    const { updateProfile, loading: isUpdatingProfile } = useUpdateProfile();
    const dispatch = useDispatch()

    const validateURL = (link: string) => {
        const regex = new RegExp(
            "(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?"
        );
        return regex.test(link);
    };

    const router = useRouter();

    const saveFormData = () => {
        try {
            const formData = {
                bio,
                link1,
                link3,
                link2,

            };
            localStorage.setItem('personalformData', JSON.stringify(formData));
        } catch (error) {
            console.error('Error saving form data', error);
        }
    };

    const loadFormData = () => {
        try {
            const storedData = localStorage.getItem('personalformData');
            if (storedData) {
                const parsedData = JSON.parse(storedData);
                setBio(parsedData.bio || '');
                setLink1(parsedData.link1 || '');
                setLink2(parsedData.link2 || '');
                setLink3(parsedData.link3 || '');

            }
        } catch (error) {
            console.error('Error loading form data', error);
        }
    };

    useEffect(() => {
        loadFormData();
    }, []);

    useEffect(() => {
        saveFormData();
    }, [link1, link2, link3, bio]);



    const handleUpdateProfile = async (e: React.FormEvent) => {
        e.preventDefault();
        dispatch(setLoading(true))
        setError(''); // Clear any previous errors

        // Validation
        if (!bio) {
            setError('Bio field must be filled before proceeding.');
            return;
        }
        if (link1 && !validateURL(link1)) {
            setError('Invalid Link 1');
            return;
        }

        if (link2 && !validateURL(link2)) {
            setError('Invalid Link 2');
            return;
        }

        if (link3 && !validateURL(link3)) {
            setError('Invalid Link 3');
            return;
        }

        try {
            const formData = {
                bio,
                website: link3,
                facebook: link2,
                linkedin: link1,
            };

            await updateProfile(formData, "/onboarding/company");
            console.log(formData, 'personal');
        } catch (error) {
            console.error('Profile update failed:', error);
            setError('Profile update failed. Please try again.');
        }
    };

    const validateForm = () => {
        const isValidBio = bio.trim().length > 0;
        const isValidLink1 = link1.trim().length > 0;
        const isValidLink2 = link2.trim().length >= 4; // Assuming PIN should be at least 4 characters
        const isValidLink3 = link3.trim().length > 0;

        return isValidBio &&
            isValidLink1 &&
            isValidLink2 &&
            isValidLink3

    };

    // Update button state whenever form fields change
    useEffect(() => {
        setDisableButton(!validateForm());
    }, [bio, link1, link2, link3]);

    useEffect(() => {
        router.prefetch("/onboarding/company")
    }, [])

     const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, nextFieldId: string) => {
      if(e.key === 'Enter') {
        e.preventDefault()

        const nextInput = document.getElementById(nextFieldId)
        if(nextInput){
          nextInput.focus()
        }
      }
    }

    return (
        <div className="flex justify-center">


            <div className="flex  w-full max-w-[1250px] justify-between items-center">
                <Sidebar />
                <div className="w-[1px] bg-[#D9D9D9] h-[751px] flex justify-center"></div>
                <div className="w-full max-w-[454px] flex  ">
                    <div className="w-full">
                        <div className="mb-8">
                            <h1 className={`${poppinsSemibold.className} text-[#4F4F4F] text-[1.25rem]`}>Tell us more about you</h1>
                            <p className={`text-[#645D5D] text-[14px] ${montserratMedium.className}`}>Provide more details about you</p>
                        </div>

                        {error && (
                            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
                                {error}
                            </div>
                        )}

                        <form onSubmit={handleUpdateProfile} className="mt-[2rem] flex flex-col gap-[1rem]">

                            <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="About">About me</Label>
                                <Input
                                    id="about"
                                    type="text"
                                    value={bio}
                                    onChange={(e) => setBio(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'link1')}
                                    disabled={isUpdatingProfile}
                                    required
                                />

                            </div>


                            <div className="flex flex-col gap-[4px]">
                                <Label htmlFor="gender">Public links</Label>
                                <Input
                                    id="link1"
                                    type="text"
                                    value={link1}
                                    placeholder="e.g Linkedin profile link"
                                    onChange={(e) => setLink1(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'link2')}
                                    disabled={isUpdatingProfile}
                                    required
                                />


                            </div>
                            <div className="flex flex-col gap-[1rem]">
                                <Input
                                    id="link2"
                                    type="text"
                                    value={link2}
                                    placeholder="e.g Facebook profile link"
                                    onChange={(e) => setLink2(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'link3')}
                                    disabled={isUpdatingProfile}
                                    required
                                />
                                <Input
                                    id="link3"
                                    type="text"
                                    value={link3}
                                    placeholder="e.g personal blog or website"
                                    onChange={(e) => setLink3(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'button')}
                                    disabled={isUpdatingProfile}
                                    required
                                />
                            </div>
                            <Button
                                id="button"
                                variant="formButton"
                                type="submit"
                                className={`w-full mt-[2rem] flex items-center justify-between ${disableButton
                                    ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                                    : 'bg-[#163E23] text-white cursor-pointer'
                                    }`}
                                disabled={disableButton}
                            >
                                {isUpdatingProfile ? (
                                    <span className="flex justify-center items-center gap-2">
                                        <svg
                                            className="animate-spin h-4 w-4 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                                            ></path>
                                        </svg>
                                        Updating Profile...
                                    </span>
                                ) : (
                                    'Next'
                                )}

                                <ArrowRight width={26} height={21} className="" />
                            </Button>
                            <p className={`mt-[26px] text-center text-[#4F4F4F] text-[14px] ${poppinsRegular.className}`}>
                                Forgotten Password?
                                <Link href="/forgot-password" className="text-[#F2875D]">
                                    Recover
                                </Link>
                            </p>
                        </form>
                    </div>
                </div>

            </div>
        </div>
    );
}

export default PersonalOnboarding;

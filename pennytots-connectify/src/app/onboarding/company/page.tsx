'use client';

import { useEffect, useState } from "react";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import Sidebar from "@/components/sidebar/onboarding-sidebar";
import { montserratMedium, poppinsSemibold } from "@/fonts";
import { useUpdateProfile } from "@/redux/profile/hooks";
import {useDispatch} from 'react-redux'
import { setLoading } from "@/redux/main/reducer";


interface IInterest {
  _id: string;
  name: string;
}

const Company = () => {
  const [company, setCompany] = useState("");
  const [company_position, setCompanyPosition]=useState("");
  const [error, setError] = useState('');
  const [disableButton, setDisableButton] = useState(true);
  const { updateProfile, loading: isUpdatingProfile } = useUpdateProfile();
 const dispatch = useDispatch()


  const router = useRouter();


  const handleUpdateProfile = async (event: React.FormEvent) => {
    event.preventDefault();
    dispatch(setLoading(true))
    setError(''); // Clear any previous errors

    if (!company || !company_position) {
      setError('Please fill in all fields before proceeding.');
      return;
    }

    try {
      const formData = { company, company_position };
      await updateProfile(formData, '/onboarding/status-and-graduation');
    } catch (error) {
      console.error('Profile update failed:', error);
      setError('Profile update failed. Please try again.');
    }
  };

  const saveFormData = async () => {
    try {
      await localStorage.setItem(
        "personalformData",
        JSON.stringify({ company, company_position })
      );
    } catch (error) {
      console.error("Error saving form data", error);
    }
  };

  const loadFormData = async () => {
    try {
      const storedData = await localStorage.getItem("personalformData");
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setCompany(parsedData.company || "");
        setCompanyPosition(parsedData.company_position || "");
      }
    } catch (error) {
      console.error("Error loading form data", error);
    }
  };

  useEffect(() => {
    loadFormData();
  }, []);

  useEffect(() => {
    saveFormData();
  }, [company, company_position]);




  const validateForm = () => {
    const isValidCompany = company.trim().length > 0;
    const isValidCompanyPosition = company_position.trim().length > 0;


    return isValidCompany &&
      isValidCompanyPosition


  };

  // Update button state whenever form fields change
  useEffect(() => {
    setDisableButton(!validateForm());
  }, [company, company_position]);


  useEffect(() => {
    router.prefetch('/onboarding/status-and-graduation')
  }, [])

   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, nextFieldId: string) => {
      if(e.key === 'Enter') {
        e.preventDefault()

          if (nextFieldId === 'button') {
      const button = document.getElementById('button');
      if (button) {
        button.click(); // 👈 Triggers button click
      }
    } 
    // Otherwise, focus the next input
    else {
      const nextInput = document.getElementById(nextFieldId);
      if (nextInput) nextInput.focus();
    }
      }
    }

  return (
    <div className="flex justify-center">


      <div className="flex  w-full max-w-[1250px] justify-between items-center">
        <Sidebar />
        <div className="w-[1px] bg-[#D9D9D9] h-[751px] flex justify-center"></div>
        <div className="w-full max-w-[454px] flex  ">
          <div className="w-full">
            <div className="mb-8">
              <h1 className={`${poppinsSemibold.className} text-[#4F4F4F] text-[1.25rem]`}>Be professional</h1>
              <p className={`text-[#645D5D] text-[14px] ${montserratMedium.className}`}>Provide more details about you</p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
                {error}
              </div>
            )}

            <form onSubmit={handleUpdateProfile} className="mt-[2rem] flex flex-col gap-[1rem]">



              <div className="flex flex-col gap-[4px]">
                <Label htmlFor="gender">Place of work</Label>
                <Input
                  id="work"
                  type="text"
                  value={company}
                  placeholder="Where do you work?"
                  onChange={(e) => setCompany(e.target.value)}
                  onKeyDown={(e) => handleKeyDown(e, 'job')}
                  disabled={isUpdatingProfile}
                  required
                />


              </div>

              <div className="flex flex-col gap-[4px]">
                <Label htmlFor="gender">Job title</Label>
                <Input
                  id="job"
                  type="text"
                  value={company_position}
                  placeholder="What`s your profession?"
                  onChange={(e) => setCompanyPosition(e.target.value)}
                  onKeyDown={(e) => handleKeyDown(e, 'button')}
                  disabled={isUpdatingProfile}
                  required
                />


              </div>


              <Button
              id="button"
                variant="formButton"
                tabIndex={0}
                type="submit"
                className={`w-full mt-[2rem] flex items-center justify-between ${disableButton
                  ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                  : 'bg-[#163E23] text-white cursor-pointer'
                  }`}
                disabled={disableButton}
              >
                {isUpdatingProfile ? (
                  <span className="flex justify-center items-center gap-2">
                    <svg
                      className="animate-spin h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                      ></path>
                    </svg>
                    Updating Profile...
                  </span>
                ) : (
                  'Next'
                )}

                <ArrowRight width={26} height={21} className="" />
              </Button>

            </form>
            <Button
              variant="backButton"
              onClick={() => router.push('/onboarding/personal')}
              className="mt-[26px]"



            >

              <ArrowRight width={26} height={21} />
              Back

            </Button>
          </div>
        </div>

      </div>
    </div>
  );
}

export default Company;

"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Eye, EyeOff, AlertCircle, ArrowRight } from "lucide-react"
import Link from "next/link"
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import { useRouter } from "next/navigation"
import { accountLogin } from "@/redux/user/hooks"
import Sidebar from "@/components/sidebar/onboarding-sidebar"
import Image from 'next/image'
import { poppinsRegular, poppinsSemibold } from "@/fonts"
import '@/components/ui/phone-input.css'
import { useSelector, useDispatch } from "react-redux"
import { appLoading, setLoading } from "@/redux/main/reducer"

// Mock user login function - replace with your actual API call
// const accountLogin = async (credentials: {
//   phone_number: { code: string; number: string };
//   password: string;
// }) => {
//   // Replace this with your actual login API call
//   console.log("Login attempt:", credentials);
//   // Simulate API call
//   return new Promise((resolve, reject) => {
//     setTimeout(() => {
//       if (credentials.password === "1234") {
//         resolve({ success: true, user: { id: 1, name: "User" } });
//       } else {
//         reject(new Error("Invalid credentials"));
//       }
//     }, 1000);
//   });
// };

export default function Component() {
  const [showPassword, setShowPassword] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState("")
  const [password, setPassword] = useState("")
  const [countryCode, setCountryCode] = useState("NG")
  const [error, setError] = useState("")
  // const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const [disableButton, setDisableButton] = useState(true);
  const dispatch = useDispatch()
  
  const isLoading  = useSelector(appLoading)


// useEffect(()=> {
//   dispatch(setLoading(false))
// }, [])
  const handleSubmitPress = async (e: React.FormEvent, ) => {
    e.preventDefault()
    setError("")

    if (!phoneNumber) {
      setError("Please fill phone number")
      return
    }
    if (!password) {
      setError("Please fill your PIN")
      return
    }

   dispatch(setLoading(true))
    try {
       const formattedPhone = phoneNumber.replace(/\s+/g, '');
        const code = formattedPhone.match(/^\+\d{1,3}/)?.[0] || "";
        const number = formattedPhone.slice(code.length);

      await accountLogin({
        phone_number: {
          code: code,
          number: number,
        },
        password: password,
      }, router)

      // Redirect to dashboard or home page on successful login
   
    } catch (err: any) {
      // Extract error message from axios error structure
      let errorMessage = "Login failed";

      if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err?.message) {
        errorMessage = err.message;
      } else if (err?.response?.status === 403) {
        errorMessage = "Invalid phone number or password";
      } else if (err?.response?.status === 401) {
        errorMessage = "Unauthorized access";
      }

      setError(errorMessage)
    } finally {
      dispatch(setLoading(false))
       }
  }

  const validateForm = () => {
    const isValidBio = phoneNumber.trim().length > 0;
    const isValidLink1 = password.trim().length > 0;


    return isValidBio &&
      isValidLink1


  }

  // Update button state whenever form fields change
  useEffect(() => {
    setDisableButton(!validateForm());
  }, [phoneNumber, password]);

  return (
    <div className="w-full flex justify-center h-[100vh]">
      <div className="flex  w-full max-w-[1250px] justify-between items-center">
        <Sidebar />
        <div className="w-full max-w-[454px] ">
          {<Image
            src="/connectify-logo.svg"
            width={210}
            height={56}
            alt='connectify-logo'
          />/* Logo */}
          <div className="flex items-center justify-center mb-8">

          </div>

          {/* Welcome Text */}
          <div className=" mb-8">
            <h1 className={`${poppinsSemibold.className} text-[2rem] text-[#4F4F4F] `}>Welcome back!</h1>
            <p className={`${poppinsRegular.className} text-[#645D5D] text-[14px]`}>
              {"Don't have an account? "}
              <Link href="/signup" className={`${poppinsSemibold.className} text-[#F2875D] text-[14px]`}>
                Sign up
              </Link>
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmitPress} className="space-y-6">
            {/* Phone Number */}
            <div className="flex flex-col gap-[4px]">
              <Label htmlFor="phone" className="text-sm font-medium text-gray-700 ">
                Phone number
              </Label>
              <PhoneInput
                defaultCountry="ng"
                value={phoneNumber}
                onChange={(value) => setPhoneNumber(value)}
                className="w-full gap-[10px] flex "
               disabled={isLoading}
              />
            </div>

            {/* Password */}
            <div className="gap-[4px] flex flex-col">
              <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pr-10"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* Login Button */}
            <Button
              variant="formButton"
              type="submit"
              className={`w-full mt-[2rem] flex items-center justify-between ${disableButton
                ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                : 'bg-[#163E23] text-white cursor-pointer'
                }`}
              disabled={disableButton}
            >
             {isLoading ? (
                     <span className="flex justify-center items-center gap-2">
                       <svg
                         className="animate-spin h-4 w-4 text-white"
                         xmlns="http://www.w3.org/2000/svg"
                         fill="none"
                         viewBox="0 0 24 24"
                       >
                         <circle
                           className="opacity-25"
                           cx="12"
                           cy="12"
                           r="10"
                           stroke="currentColor"
                           strokeWidth="4"
                         ></circle>
                         <path
                           className="opacity-75"
                           fill="currentColor"
                           d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                         ></path>
                       </svg>
                       Updating Profile...
                     </span>
                   ) : (
                     'Next'
                   )}
                 <ArrowRight width={26} height={21} className="" />
                   
   
            </Button>

            {/* Forgot Password */}
            <div className="text-center mt-[26px]">
              <p className={`mt-[26px] text-center text-[#4F4F4F] text-[14px] ${poppinsRegular.className}`}>
                Forgotten Password?
                <Link href="/forgot-password" className="text-[#F2875D]">
                  Recover
                </Link>
              </p>

            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

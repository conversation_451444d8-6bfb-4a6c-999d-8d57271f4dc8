'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowRight, Users, MessageCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import ProfileClickHandler from '@/components/profile/ProfileClickHandler';
import { poppinsRegular, poppinsMedium } from '@/fonts';

// Test user data
const testUsers = [
  {
    _id: '686f48e54622ba4de88913cb',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    profile_picture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  },
  {
    _id: '686f48e54622ba4de88913cc',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    profile_picture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
  }
];

const TestNewLayout: React.FC = () => {
  const router = useRouter();

  const handleChatStart = (chatData: any) => {
    console.log('Chat started:', chatData);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className={poppinsMedium.className}>
            🎯 New Messages Layout Test
          </CardTitle>
          <p className={`${poppinsRegular.className} text-gray-600`}>
            Test the new side-by-side layout: Group Chats → Private Chats → Chat Interface
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Layout Explanation */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 border rounded-lg bg-blue-50">
              <div className="flex items-center gap-2 mb-2">
                <Users className="w-5 h-5 text-blue-600" />
                <h3 className={`${poppinsMedium.className} text-sm text-blue-800`}>
                  1. Group Chats Sidebar
                </h3>
              </div>
              <p className={`${poppinsRegular.className} text-xs text-blue-700`}>
                Shows all group chats. Click a group to see its private chats.
              </p>
            </div>
            
            <div className="p-4 border rounded-lg bg-green-50">
              <div className="flex items-center gap-2 mb-2">
                <MessageCircle className="w-5 h-5 text-green-600" />
                <h3 className={`${poppinsMedium.className} text-sm text-green-800`}>
                  2. Private Chats Panel
                </h3>
              </div>
              <p className={`${poppinsRegular.className} text-xs text-green-700`}>
                Shows private chats for selected group. Click to open chat.
              </p>
            </div>
            
            <div className="p-4 border rounded-lg bg-purple-50">
              <div className="flex items-center gap-2 mb-2">
                <ArrowRight className="w-5 h-5 text-purple-600" />
                <h3 className={`${poppinsMedium.className} text-sm text-purple-800`}>
                  3. Chat Interface
                </h3>
              </div>
              <p className={`${poppinsRegular.className} text-xs text-purple-700`}>
                Full chat interface with messages and input field.
              </p>
            </div>
          </div>

          {/* Navigation Tests */}
          <div className="space-y-4">
            <h3 className={`${poppinsMedium.className} text-lg`}>Test Navigation</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button 
                onClick={() => router.push('/messages-new')}
                className="h-20 flex flex-col items-center justify-center gap-2"
              >
                <Users className="w-6 h-6" />
                <span>Go to New Messages Layout</span>
              </Button>
              
              <Button 
                onClick={() => router.push('/messages')}
                variant="outline"
                className="h-20 flex flex-col items-center justify-center gap-2"
              >
                <MessageCircle className="w-6 h-6" />
                <span>Go to Old Messages (for comparison)</span>
              </Button>
            </div>
          </div>

          {/* Profile Click Tests */}
          <div className="space-y-4">
            <h3 className={`${poppinsMedium.className} text-lg`}>Test Profile Clicks</h3>
            <p className={`${poppinsRegular.className} text-sm text-gray-600`}>
              These should now navigate to the new layout instead of individual chat pages:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {testUsers.map((user) => (
                <div key={user._id} className="p-4 border rounded-lg">
                  <ProfileClickHandler
                    user={user}
                    showAvatar={true}
                    avatarSize={60}
                    defaultAction="chat"
                    onChatStart={handleChatStart}
                    className="w-full p-3 border rounded hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg font-bold">
                        {user.first_name[0]}
                      </div>
                      <div>
                        <p className={`${poppinsMedium.className} text-sm`}>
                          {user.first_name} {user.last_name}
                        </p>
                        <p className={`${poppinsRegular.className} text-xs text-gray-500`}>
                          Click to start chat
                        </p>
                      </div>
                    </div>
                  </ProfileClickHandler>
                </div>
              ))}
            </div>
          </div>

          {/* Expected Behavior */}
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h4 className={`${poppinsMedium.className} text-sm mb-2`}>Expected Behavior:</h4>
            <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
              <li>• Sidebar now shows "Group Chats" instead of "Messages"</li>
              <li>• Clicking sidebar "Group Chats" → Opens new layout with 3 panels</li>
              <li>• Clicking profile names → Opens new layout (not individual chat pages)</li>
              <li>• Layout: Group Chats | Private Chats | Chat Interface</li>
              <li>• All three panels visible side by side</li>
            </ul>
          </div>

          {/* Current Status */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 className={`${poppinsMedium.className} text-sm mb-2`}>Current Implementation:</h4>
            <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
              <li>✅ New MessagesLayout component created</li>
              <li>✅ Side-by-side layout with 3 panels</li>
              <li>✅ Group chats sidebar (with mock data)</li>
              <li>✅ Private chats panel (loads when group selected)</li>
              <li>✅ Chat interface (SimpleChatRoom integration)</li>
              <li>✅ ProfileClickHandler updated to use new layout</li>
              <li>✅ Sidebar navigation updated</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestNewLayout;

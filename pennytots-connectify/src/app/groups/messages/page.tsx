"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, MessageCircle, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import MainLayout from "@/components/layout/main-layout"
import { SimpleChatRoom } from "@/components/chat/SimpleChatRoom"
import { useSelector } from "react-redux"
import { userId as currentUserId, userToken } from "@/redux/user/reducer"
import { Loading } from "@/components/ui/loading"
import { poppinsSemibold, poppinsRegular, poppinsMedium } from "@/fonts"
import { useMyGroups, useSuggestedGroups } from "@/redux/groups/hooks"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

interface GroupMessagesPageProps {}

interface GroupItem {
  _id: string
  name: string
  description?: string
  image?: string
  profilePicture?: string
  participants: string[]
  memberCount: number
  lastMessage?: {
    sender: {
      firstName: string
    }
    message: string
    createdAt: string
  }
  unreadCount?: number
  isAdmin?: boolean
  muted?: boolean
}

export default function GroupMessagesPage({}: GroupMessagesPageProps) {
  const router = useRouter()
  const myId = useSelector(currentUserId)
  const token = useSelector(userToken)
  
  const [selectedGroup, setSelectedGroup] = useState<GroupItem | null>(null)
  const [activeTab, setActiveTab] = useState<'my-groups' | 'recommended'>('my-groups')
  const [searchQuery, setSearchQuery] = useState('')

  const { data: myGroups, isLoading: myGroupsLoading } = useMyGroups()
  const { data: suggestedGroups, isLoading: suggestedLoading } = useSuggestedGroups()

  const currentGroups = activeTab === 'my-groups' ? myGroups : suggestedGroups
  const isLoading = activeTab === 'my-groups' ? myGroupsLoading : suggestedLoading

  // Filter groups based on search query
  const filteredGroups = currentGroups?.filter(group =>
    group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    group.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || []

  const handleBack = () => {
    router.push('/groups')
  }

  const handleGroupSelect = (group: GroupItem) => {
    setSelectedGroup(group)
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' })
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <MainLayout activeScreen="groups" currentScreen="groups" disableContentStyling={true}>
        <div className="flex items-center justify-center h-64">
          <Loading size="lg" />
        </div>
      </MainLayout>
    )
  }

  // Main group messages interface - Similar to Messages [id] page
  return (
    <MainLayout activeScreen="groups" currentScreen="groups" disableContentStyling={true}>
      <div className="flex h-full">
        {/* Left sidebar - Group list */}
        <div className="w-80 border-r border-gray-200 bg-white flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <Button variant="ghost" size="sm" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <h1 className={`${poppinsSemibold.className} text-lg text-gray-900`}>
                Group Messages
              </h1>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search groups..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setActiveTab('my-groups')}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'my-groups'
                  ? 'border-b-2 border-[#F2875D] text-[#F2875D] bg-orange-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              My Groups
            </button>
            <button
              onClick={() => setActiveTab('recommended')}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'recommended'
                  ? 'border-b-2 border-[#F2875D] text-[#F2875D] bg-orange-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              Recommended
            </button>
          </div>

          {/* Group List */}
          <div className="flex-1 overflow-y-auto">
            {filteredGroups.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Users className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                  <p className={`${poppinsRegular.className} text-gray-500`}>
                    {searchQuery ? 'No groups found' : 'No groups available'}
                  </p>
                </div>
              </div>
            ) : (
              <div>
                {filteredGroups.map((group) => (
                  <GroupChatItem
                    key={group._id}
                    group={group}
                    isSelected={selectedGroup?._id === group._id}
                    onClick={() => handleGroupSelect(group)}
                    formatTime={formatTime}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Right side - Chat conversation */}
        <div className="flex-1">
          {selectedGroup ? (
            <SimpleChatRoom
              chat={{
                _id: selectedGroup._id,
                accountId: {
                  _id: selectedGroup._id,
                  firstName: selectedGroup.name,
                  lastName: '',
                  first_name: selectedGroup.name,
                  last_name: '',
                  profilePicture: selectedGroup.image || selectedGroup.profilePicture,
                  profile_picture: selectedGroup.image || selectedGroup.profilePicture
                },
                messages: [],
                isNewChat: false,
                isGroupChat: true
              }}
              onBack={() => setSelectedGroup(null)}
              userDetails={{
                _id: selectedGroup._id,
                firstName: selectedGroup.name,
                lastName: '',
                first_name: selectedGroup.name,
                last_name: '',
                profilePicture: selectedGroup.image || selectedGroup.profilePicture,
                profile_picture: selectedGroup.image || selectedGroup.profilePicture
              }}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className={`${poppinsSemibold.className} text-lg text-gray-900 mb-2`}>
                  Select a group
                </h3>
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  Choose a group from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}

// Group Chat Item Component
interface GroupChatItemProps {
  group: GroupItem
  isSelected: boolean
  onClick: () => void
  formatTime: (dateString: string) => string
}

const GroupChatItem: React.FC<GroupChatItemProps> = ({
  group,
  isSelected,
  onClick,
  formatTime
}) => {
  return (
    <div
      onClick={onClick}
      className={`flex items-center p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 ${
        isSelected ? 'bg-blue-50 border-blue-200' : ''
      }`}
    >
      {/* Group Avatar */}
      <div className="relative mr-3">
        <div className="w-12 h-12 rounded-full bg-[#F2875D] flex items-center justify-center">
          {group.image || group.profilePicture ? (
            <img
              src={group.image || group.profilePicture}
              alt="Group"
              className="w-12 h-12 rounded-full object-cover"
            />
          ) : (
            <Users className="h-6 w-6 text-white" />
          )}
        </div>
        {group.unreadCount && group.unreadCount > 0 && (
          <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {group.unreadCount > 9 ? '9+' : group.unreadCount}
          </div>
        )}
      </div>

      {/* Group Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className={`${poppinsMedium.className} text-gray-900 truncate`}>
              {group.name}
            </h3>
            {group.isAdmin && (
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                Admin
              </span>
            )}
          </div>
          {group.lastMessage && (
            <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
              {formatTime(group.lastMessage.createdAt)}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2 mt-1">
          <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
            {group.memberCount} members
          </span>
          {group?.lastMessage && (
            <>
              <span className="text-gray-300">•</span>
              <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate`}>
                {group?.lastMessage?.sender?.firstName}: {group?.lastMessage?.message}
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

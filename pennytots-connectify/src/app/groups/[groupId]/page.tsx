"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, MessageCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import MainLayout from "@/components/layout/main-layout"
import { GroupChatRoom } from "@/components/chat/GroupChatRoom"
import { useSelector } from "react-redux"
import { userId as currentUserId, userToken } from "@/redux/user/reducer"
import { Loading } from "@/components/ui/loading"
import { poppinsSemibold, poppinsRegular } from "@/fonts"
import { group as groupAPI } from "@/api/group"

interface GroupChatPageProps {}

export default function GroupChatPage({}: GroupChatPageProps) {
  const params = useParams()
  const router = useRouter()
  const groupId = params.groupId as string
  const myId = useSelector(currentUserId)
  const token = useSelector(userToken)
  
  const [groupData, setGroupData] = useState<any>(null)
  const [isLoadingGroup, setIsLoadingGroup] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Debug logging
  console.log('GroupChatPage - groupId:', groupId)
  console.log('GroupChatPage - myId:', myId)
  console.log('GroupChatPage - groupData:', groupData)
  console.log('GroupChatPage - isLoadingGroup:', isLoadingGroup)

  // Load group data
  useEffect(() => {
    const initializeGroup = async () => {
      if (!groupId || !myId || !token) {
        setError("Invalid group or not authenticated")
        setIsLoadingGroup(false)
        return
      }

      try {
        setIsLoadingGroup(true)
        setError(null)

        console.log('Initializing group chat:', groupId)

        // Get group details
        const groupResponse = await groupAPI.getGroup(groupId)
        
        console.log('Group response:', groupResponse)

        if (groupResponse && groupResponse.group) {
          const formattedGroup = {
            _id: groupResponse.group._id,
            name: groupResponse.group.name,
            description: groupResponse.group.description,
            image: groupResponse.group.image,
            participants: groupResponse.group.participants || [],
            admins: groupResponse.group.admins || [],
            memberCount: groupResponse.group.participants?.length || 0,
            createdBy: groupResponse.group.createdBy,
            tag: groupResponse.group.tag
          }

          console.log('Setting group data:', formattedGroup)
          setGroupData(formattedGroup)
        } else {
          setError("Group not found")
        }
      } catch (err: any) {
        console.error('Error initializing group:', err)
        if (err.response?.status === 404) {
          setError("Group not found")
        } else if (err.response?.status === 403) {
          setError("You don't have access to this group")
        } else {
          setError("Failed to load group")
        }
      } finally {
        setIsLoadingGroup(false)
      }
    }

    initializeGroup()
  }, [groupId, myId, token])

  const handleBack = () => {
    router.push('/groups')
  }

  // Loading state
  if (isLoadingGroup) {
    return (
      <MainLayout activeScreen="groups" currentScreen="groups" disableContentStyling={true}>
        <div className="flex items-center justify-center h-64">
          <Loading size="lg" />
        </div>
      </MainLayout>
    )
  }

  // Error state
  if (error || !groupData) {
    return (
      <MainLayout activeScreen="groups" currentScreen="groups" disableContentStyling={true}>
        <div className="flex flex-col items-center justify-center h-64">
          <h2 className={`${poppinsSemibold.className} text-xl text-gray-900 mb-2`}>
            {error === "Group not found" ? "Group Not Found" : "Access Denied"}
          </h2>
          <p className={`${poppinsRegular.className} text-gray-600 mb-4`}>
            {error === "Group not found" 
              ? "The group you're looking for doesn't exist or has been deleted."
              : error === "You don't have access to this group"
              ? "You need to be a member of this group to view messages."
              : "Unable to load group. Please try again later."
            }
          </p>
          <Button onClick={handleBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Groups
          </Button>
        </div>
      </MainLayout>
    )
  }

  // Main group chat interface
  return (
    <MainLayout activeScreen="groups" currentScreen="groups" disableContentStyling={true}>
      <div className="flex h-full">
        {/* Group Chat Room */}
        <div className="flex-1">
          <GroupChatRoom
            group={groupData}
            onBack={handleBack}
            isLoading={isLoadingGroup}
          />
        </div>
      </div>
    </MainLayout>
  )
}

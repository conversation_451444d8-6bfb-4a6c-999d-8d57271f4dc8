"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, MessageCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import MainLayout from "@/components/layout/main-layout"
import { PrivateChatRoom } from "@/components/chat/PrivateChatRoom"
import SimpleChatRoom  from "@/components/chat/SimpleChatRoom"
import { useProfileById } from "@/redux/profile/hooks"
import { useChatActions } from "@/redux/chat/hooks"
import { useSelector } from "react-redux"
import { userId as currentUserId, userToken } from "@/redux/user/reducer"
import { Loading } from "@/components/ui/loading"
import { poppinsSemibold, poppinsRegular } from "@/fonts"
import OpenChatItem from "@/components/screens/openChatItem"

interface UserChatPageProps {}

export default function UserChatPage({}: UserChatPageProps) {
  const params = useParams()
  const router = useRouter()
  const userId = params.userId as string
  const myId = useSelector(currentUserId)
  const token = useSelector(userToken)
  
  const [chatData, setChatData] = useState<any>(null)
  const [isLoadingChat, setIsLoadingChat] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedChatUser, setSelectedChatUser] = useState<any>(null)

  // Get user profile data
  const {
    data: userProfile,
    isLoading: profileLoading,
    error: profileError
  } = useProfileById(userId)

  // Debug logging
  console.log('UserChatPage - userId:', userId)
  console.log('UserChatPage - myId:', myId)
  console.log('UserChatPage - userProfile:', userProfile)
  console.log('UserChatPage - profileLoading:', profileLoading)
  console.log('UserChatPage - profileError:', profileError)
  console.log('UserChatPage - chatData:', chatData)
  console.log('UserChatPage - isLoadingChat:', isLoadingChat)

  // Get chat actions
  const { getUserChats } = useChatActions()

  // Load or create chat with the user
  useEffect(() => {
    const initializeChat = async () => {
      if (!userId || !myId || !token || userId === myId) {
        setError("Invalid user or not authenticated")
        setIsLoadingChat(false)
        return
      }

      // If profile is still loading, wait for it
      if (profileLoading) {
        return
      }

      // If profile failed to load, still proceed with fallback data
      if (!userProfile && !profileLoading) {
        console.log('Profile not found, creating fallback user data')
        const fallbackUser = {
          _id: userId,
          first_name: 'Unknown',
          last_name: 'User',
          firstName: 'Unknown',
          lastName: 'User',
          profile_picture: '',
          profilePicture: ''
        }

        // Create new chat with fallback user data
        const newChat = {
          _id: `temp-${userId}-${Date.now()}`,
          accountId: fallbackUser,
          messages: [],
          isNewChat: true
        }

        setChatData(newChat)
        setSelectedChatUser({
          ...fallbackUser,
          chatId: null,
          messages: []
        })
        setIsLoadingChat(false)
        return
      }

      try {
        setIsLoadingChat(true)
        setError(null)

        console.log('Initializing chat with user:', userId)

        // Try to get existing chat with this user
        try {
          const chatResponse = await getUserChats({
            userId: userId,
            page: 1, // Fix: use pageNumber instead of page
            limit: 10
          })

          console.log('Chat response:', chatResponse)

          if (chatResponse && chatResponse.chats && chatResponse.chats.chatDetails) {
            // Format the chat data to match expected structure
            const formattedChat = {
              _id: chatResponse.chats.chatDetails._id,
              accountId: {
                _id: userId,
                firstName: userProfile?.first_name || '',
                lastName: userProfile?.last_name || '',
                profilePicture: userProfile?.profile_picture || '',
                first_name: userProfile?.first_name || '',
                last_name: userProfile?.last_name || '',
                profile_picture: userProfile?.profile_picture || ''
              },
              messages: chatResponse.chats.docs || [],
              chatDetails: chatResponse.chats.chatDetails
            }

            console.log('Setting existing chat data:', formattedChat)
            setChatData(formattedChat)
            // Also set as selected chat user for immediate display
            setSelectedChatUser({
              _id: userId,
              firstName: userProfile?.first_name || '',
              lastName: userProfile?.last_name || '',
              profilePicture: userProfile?.profile_picture || '',
              first_name: userProfile?.first_name || '',
              last_name: userProfile?.last_name || '',
              profile_picture: userProfile?.profile_picture || '',
              chatId: chatResponse.chats.chatDetails._id,
              messages: chatResponse.chats.docs || []
            })
          } else {
            // Create a new chat structure if no existing chat
            console.log('Creating new chat structure')
            const newChat = {
              _id: `temp-${userId}-${Date.now()}`, // Temporary ID until server creates real one
              accountId: {
                _id: userId,
                firstName: userProfile?.first_name || '',
                lastName: userProfile?.last_name || '',
                profilePicture: userProfile?.profile_picture || '',
                first_name: userProfile?.first_name || '',
                last_name: userProfile?.last_name || '',
                profile_picture: userProfile?.profile_picture || ''
              },
              messages: [],
              isNewChat: true
            }

            setChatData(newChat)
            // Also set as selected chat user for immediate display
            setSelectedChatUser({
              _id: userId,
              firstName: userProfile?.first_name || '',
              lastName: userProfile?.last_name || '',
              profilePicture: userProfile?.profile_picture || '',
              first_name: userProfile?.first_name || '',
              last_name: userProfile?.last_name || '',
              profile_picture: userProfile?.profile_picture || '',
              chatId: null,
              messages: []
            })
          }
        } catch (chatError) {
          console.log('No existing chat found, creating new one:', chatError)
          // Create a new chat structure if API call fails (expected for new chats)
          const newChat = {
            _id: `temp-${userId}-${Date.now()}`,
            accountId: {
              _id: userId,
              firstName: userProfile?.first_name || '',
              lastName: userProfile?.last_name || '',
              profilePicture: userProfile?.profile_picture || '',
              first_name: userProfile?.first_name || '',
              last_name: userProfile?.last_name || '',
              profile_picture: userProfile?.profile_picture || ''
            },
            messages: [],
            isNewChat: true
          }

          setChatData(newChat)
          // Also set as selected chat user for immediate display
          setSelectedChatUser({
            _id: userId,
            firstName: userProfile?.first_name || '',
            lastName: userProfile?.last_name || '',
            profilePicture: userProfile?.profile_picture || '',
            first_name: userProfile?.first_name || '',
            last_name: userProfile?.last_name || '',
            profile_picture: userProfile?.profile_picture || '',
            chatId: null,
            messages: []
          })
        }
      } catch (err) {
        console.error('Error initializing chat:', err)
        setError("Failed to load chat")
      } finally {
        setIsLoadingChat(false)
      }
    }

    initializeChat()
  }, [userId, myId, token, userProfile, profileLoading, getUserChats])

  const handleBack = () => {
    router.push('/messages')
  }

  // Handle chat selection from OpenChatItem
  const handleChatSelect = (chatUser: any) => {
    setSelectedChatUser(chatUser)
    console.log('Selected chat user:', chatUser)
  }

  // Redirect if trying to chat with self
  if (userId === myId) {
    return (
      <MainLayout activeScreen="chats" currentScreen="chats">
        <div className="flex flex-col items-center justify-center h-64">
          <p className={`${poppinsRegular.className} text-gray-500 mb-4`}>
            You cannot chat with yourself
          </p>
          <Button onClick={handleBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Messages
          </Button>
        </div>
      </MainLayout>
    )
  }

  // Loading state
  if (profileLoading || isLoadingChat) {
    return (
      <MainLayout activeScreen="chats" currentScreen="chats">
        <div className="flex items-center justify-center h-64">
          <Loading size="lg" />
        </div>
      </MainLayout>
    )
  }

  // Error state
  if (profileError || error || !userProfile) {
    return (
      <MainLayout activeScreen="chats" currentScreen="chats">
        <div className="flex flex-col items-center justify-center h-64">
          <h2 className={`${poppinsSemibold.className} text-xl text-gray-900 mb-2`}>
            User Not Found
          </h2>
          <p className={`${poppinsRegular.className} text-gray-600 mb-4`}>
            The user you're trying to chat with doesn't exist or is unavailable.
          </p>
          <Button onClick={handleBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Messages
          </Button>
        </div>
      </MainLayout>
    )
  }

  // Create a fallback user object if userProfile is not available
  const fallbackUser = userProfile || {
    _id: userId,
    first_name: 'Unknown',
    last_name: 'User',
    firstName: 'Unknown',
    lastName: 'User',
    profile_picture: '',
    profilePicture: ''
  };

  // Main chat interface - Show chat list and selected chat
  return (
    <MainLayout activeScreen="chats" currentScreen="chats" disableContentStyling={true}>
      <div className="flex h-full">
        {/* Left sidebar - Chat list */}
        <OpenChatItem
          chat={chatData}
          onBack={handleBack}
          userDetails={fallbackUser}
          onChatSelect={handleChatSelect}
        />

        {/* Right side - Chat conversation */}
        <div className="flex-1">
          {selectedChatUser ? (
            <SimpleChatRoom
              chat={{
                _id: selectedChatUser.chatId || `temp-${selectedChatUser._id}`,
                accountId: selectedChatUser,
                messages: [],
                isNewChat: !selectedChatUser.chatId
              }}
              onBack={() => setSelectedChatUser(null)}
              userDetails={selectedChatUser}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className={`${poppinsSemibold.className} text-lg text-gray-900 mb-2`}>
                  Select a conversation
                </h3>
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  Choose a chat from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}

"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, ArrowRight, CheckCircle } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import Sidebar from "@/components/sidebar/onboarding-sidebar"
import Image from 'next/image'
import { poppinsRegular, poppinsSemibold } from "@/fonts"
import '@/components/ui/phone-input.css'
import { useSelector, useDispatch } from "react-redux"
import { appLoading, setLoading } from "@/redux/main/reducer"
import { useSendResetPasswordToken } from "@/redux/user/hooks"

export default function Component() {
  const [email, setEmail] = useState('')
  const router = useRouter()
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')
  const [disableButton, setDisableButton] = useState(true);
  const dispatch = useDispatch()

  const isLoading = useSelector(appLoading)
  const sendResetPasswordToken = useSendResetPasswordToken()

  const validateEmail = (email: string): boolean => {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(email);
  };



  const handleSubmitPress = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess("")

    // Validate email
    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    dispatch(setLoading(true))

    try {
      // Send reset password token
      await sendResetPasswordToken.mutateAsync({ email });

      setSuccess('Password reset instructions have been sent to your email address. Please check your inbox.');

      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push('/login');
      }, 3000);

    } catch (error: any) {
      console.error('Reset password error:', error);
      setError(error?.response?.data?.message || 'An error occurred. Please try again.');
    } finally {
      dispatch(setLoading(false))
    }
  }


  const validateForm = () => {
    const isEmailValid = email.trim().length > 0;
    return isEmailValid
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, nextFieldId: string) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      const nextInput = document.getElementById(nextFieldId)
      if (nextInput) {
        nextInput.focus()

      }
    }
  }

  // Update button state whenever form fields change
  useEffect(() => {
    setDisableButton(!validateForm());
  }, [email]);

  return (
    <div className="w-full flex justify-center h-[100vh]">
      <div className="flex  w-full max-w-[1250px] justify-between items-center">
        <Sidebar />
        <div className="w-full max-w-[454px] ">
          {<Image
            src="/connectify-logo.svg"
            width={210}
            height={56}
            alt='connectify-logo'
          />/* Logo */}
          <div className="flex items-center justify-center mb-8">

          </div>

          {/* Welcome Text */}
          <div className=" mb-8">
            <h1 className={`${poppinsSemibold.className} text-[2rem] text-[#4F4F4F] `}>Forgot your password?</h1>
            <p className={`${poppinsRegular.className} text-[#645D5D] text-[14px]`}>
              {"Don’t worry, we’ve got you covered"}{" "}
              <Link
                href="/login"
                className={`${poppinsSemibold.className} text-[#F2875D] text-[14px]`}
              >
                Login
              </Link>
            </p>

          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm text-green-700">{success}</span>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmitPress} className="space-y-6">
            <div className="flex flex-col gap-[4px]">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, 'button')}
                disabled={isLoading}
                required
              />
            </div>

            {/* Login Button */}
            <Button
              id="button"
              variant="formButton"
              type="submit"
              className={`w-full mt-[2rem] flex items-center justify-between ${disableButton
                ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                : 'bg-[#163E23] text-white cursor-pointer'
                }`}
              disabled={disableButton}
            >
              {isLoading ? (
                <span className="flex justify-center items-center gap-2">
                  <svg
                    className="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                  </svg>
                  Sending Reset Link...
                </span>
              ) : success ? (
                'Email Sent!'
              ) : (
                'Send Reset Link'
              )}
              <ArrowRight width={26} height={21} className="" />
            </Button>

            {/* Forgot Password */}
            <div className="text-center mt-[26px]">
              <p className={`mt-[26px] text-center text-[#4F4F4F] text-[14px] ${poppinsRegular.className}`}>
                Don`t have an account?
                <Link href="/signup" className="text-[#F2875D]">
                  Signup
                </Link>
              </p>

            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

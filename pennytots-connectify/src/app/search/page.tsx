'use client';

import React from 'react';
import SearchScreen from '@/components/screens/chatsearch';
import { poppinsBold } from '@/fonts';

export default function SearchPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <h1 className={`${poppinsBold.className} text-xl text-gray-900`}>
          Search
        </h1>
        <p className="text-sm text-gray-600 mt-1">
          Find users, chats, groups, posts, and comments
        </p>
      </div>

      {/* Search Content */}
      <div className="flex-1">
        <SearchScreen />
      </div>
    </div>
  );
}

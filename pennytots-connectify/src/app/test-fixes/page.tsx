'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ProfileClickHandler from '@/components/profile/ProfileClickHandler';
import { MessageInput } from '@/components/chat/MessageInput';
import { poppinsRegular, poppinsMedium } from '@/fonts';

// Test user data
const testUser = {
  _id: '686f48e54622ba4de88913cb',
  first_name: 'Test',
  last_name: 'User',
  profile_picture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
};

const TestFixesPage: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [testMessage, setTestMessage] = useState('');

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const handleChatStart = (chatData: any) => {
    addLog(`✅ Chat started: ${JSON.stringify(chatData)}`);
  };

  const handleSendMessage = (message: string, attachment?: File, attachmentType?: string) => {
    addLog(`📤 Message sent: "${message}" ${attachment ? `with ${attachmentType} attachment` : ''}`);
  };

  const testSocketConnection = () => {
    addLog('🔌 Testing socket connection...');
    // This will show if socket is working or disabled
    try {
      const socketService = require('@/services/socket').default;
      const isConnected = socketService.isConnected();
      addLog(`🔌 Socket status: ${isConnected ? 'Connected' : 'Disconnected'}`);
    } catch (error: any) {
      addLog(`❌ Socket error: ${error.message}`);
    }
  };

  const testProfileAPI = async () => {
    addLog('👤 Testing profile API...');
    try {
      const response = await fetch(`/api/user/${testUser._id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        addLog('✅ Profile API working');
      } else {
        addLog(`❌ Profile API failed: ${response.status}`);
      }
    } catch (error: any) {
      addLog(`❌ Profile API error: ${error.message}`);
    }
  };

  const testSponsoredAds = async () => {
    addLog('📺 Testing sponsored ads...');
    try {
      // Test the actual API function that includes fallback logic
      const { sponsoredAd } = await import('@/api/sponsoredAds');
      const result = await sponsoredAd.getSponsoredAd();

      if (result && result.ad) {
        addLog('✅ Sponsored ads working with data');
        addLog(`📊 Ad title: ${result.ad.title || 'Default ad'}`);
        addLog(`🏢 Company: ${result.ad.company || 'PennyTots'}`);
      } else {
        addLog('⚠️ Sponsored ads returned null');
      }
    } catch (error: any) {
      addLog(`❌ Sponsored ads error: ${error.message}`);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className={poppinsMedium.className}>
            🔧 Fix Verification Test Page
          </CardTitle>
          <p className={`${poppinsRegular.className} text-gray-600`}>
            Test all the fixes that were applied to resolve the issues.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Test Profile Click */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>1. Profile Click Test</h3>
            <div className="flex items-center gap-4 mb-2">
              <ProfileClickHandler
                user={testUser}
                showAvatar={true}
                avatarSize={40}
                defaultAction="chat"
                onChatStart={handleChatStart}
                className="p-2 border rounded hover:bg-gray-50"
              >
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
                    {testUser.first_name[0]}
                  </div>
                  <span>{testUser.first_name} {testUser.last_name}</span>
                </div>
              </ProfileClickHandler>
              <span className={`${poppinsRegular.className} text-gray-500 text-sm`}>
                ← Click to test profile navigation
              </span>
            </div>
            <p className={`${poppinsRegular.className} text-xs text-gray-500`}>
              Should navigate to /messages/{testUser._id} without infinite loading
            </p>
          </div>

          {/* Test Message Input */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>2. Message Input Test</h3>
            <MessageInput
              onSendMessage={handleSendMessage}
              placeholder="Type a test message..."
              chatId="test-chat"
            />
            <p className={`${poppinsRegular.className} text-xs text-gray-500 mt-2`}>
              Should show input field and allow typing/sending messages
            </p>
          </div>

          {/* Manual Tests */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>3. API Tests</h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={testSocketConnection} variant="outline" size="sm">
                Test Socket
              </Button>
              <Button onClick={testProfileAPI} variant="outline" size="sm">
                Test Profile API
              </Button>
              <Button onClick={testSponsoredAds} variant="outline" size="sm">
                Test Sponsored Ads
              </Button>
            </div>
          </div>

          {/* Status Indicators */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>4. Status Check</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Socket Errors:</strong> Should be reduced/eliminated
              </div>
              <div>
                <strong>Profile Loading:</strong> Should work without hanging
              </div>
              <div>
                <strong>Message Input:</strong> Should always be visible
              </div>
              <div>
                <strong>Sponsored Ads:</strong> Should fail silently
              </div>
            </div>
          </div>

          {/* Test Logs */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>5. Test Logs</h3>
            <div className="bg-gray-50 rounded p-3 max-h-48 overflow-y-auto">
              {logs.length === 0 ? (
                <p className={`${poppinsRegular.className} text-gray-500 text-sm`}>
                  No logs yet. Try the tests above.
                </p>
              ) : (
                <div className="space-y-1">
                  {logs.map((log, index) => (
                    <div 
                      key={index} 
                      className={`${poppinsRegular.className} text-xs font-mono`}
                    >
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

        </CardContent>
      </Card>
    </div>
  );
};

export default TestFixesPage;

"use client";

import Topic from '@/components/screens/topic-list'
import React from 'react'
import { useSearchParams } from 'next/navigation'

const Page = () => {
  const searchParams = useSearchParams()
  const topicIdParam = searchParams.get('postid')
  const topicId = topicIdParam === null ? undefined : topicIdParam;
  
  return (
    <div className="">
      
      <Topic topicId={topicId} />
    </div>
  )
}

export default Page

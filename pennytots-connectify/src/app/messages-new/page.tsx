'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/main-layout';
import { MessagesLayout } from '@/components/chat/MessagesLayout';

const NewMessagesPage: React.FC = () => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <MainLayout activeScreen="chats" currentScreen="chats">
      <MessagesLayout onBack={handleBack} />
    </MainLayout>
  );
};

export default NewMessagesPage;

'use client';

import React, { useState } from 'react';
import { Plus, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Loading } from '@/components/ui/loading';
import { useTopics } from '@/redux/topic/hooks';
import { TopicCard } from '@/components/screens/topic-list';
import { PostModal } from '@/components/ui/post-modal';
import { poppinsSemibold, poppinsRegular } from '@/fonts';

export default function TopicsPage() {
  const [isPostModalOpen, setIsPostModalOpen] = useState(false);
  const { data: topics, isLoading, error, refetch, isFetching } = useTopics();

  const handleOpenPostModal = () => {
    setIsPostModalOpen(true);
  };

  const handleClosePostModal = () => {
    setIsPostModalOpen(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-10">
        <div className="">
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`${poppinsSemibold.className} text-2xl text-gray-900`}>
                Topics
              </h1>
              <p className={`${poppinsRegular.className} text-gray-600 mt-1`}>
                Discover and share interesting topics
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isFetching}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                onClick={handleOpenPostModal}
                className="bg-[#F2875D] hover:bg-[#E07A52] text-white flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create Topic
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl ">
        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <Loading size="lg" />
              <p className={`${poppinsRegular.className} text-gray-600 mt-4`}>
                Loading topics...
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-center">
              <h3 className={`${poppinsSemibold.className} text-lg text-gray-900 mb-2`}>
                Error Loading Topics
              </h3>
              <p className={`${poppinsRegular.className} text-gray-600 mb-4`}>
                There was an error loading the topics. Please try again.
              </p>
              <Button onClick={handleRefresh} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        ) : topics && topics.length > 0 ? (
          <div className="bg-white">
            {topics.map((topic: any) => (
              <TopicCard
                key={topic._id}
                item={topic}
                convo={false}
                refreshFunction={refetch}
                onToggleMuted={() => {
                  // Implement mute functionality if needed
                  console.log('Toggle mute for topic:', topic._id);
                }}
                muted={false}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center ">
                <Plus className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className={`${poppinsSemibold.className} text-lg text-gray-900 mb-2`}>
                No Topics Yet
              </h3>
              <p className={`${poppinsRegular.className} text-gray-600 mb-6 max-w-md`}>
                There are no topics to display yet. Be the first to create a topic and start the conversation!
              </p>
              <Button
                onClick={handleOpenPostModal}
                className="bg-[#F2875D] hover:bg-[#E07A52] text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Topic
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Post Modal */}
      <PostModal 
        isOpen={isPostModalOpen} 
        onClose={handleClosePostModal} 
      />
    </div>
  );
}

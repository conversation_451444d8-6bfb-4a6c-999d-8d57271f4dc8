@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Custom responsive spacing variables */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 0.875rem;  /* 14px */
  --spacing-lg: 1rem;      /* 16px */
  --spacing-xl: 1.5rem;    /* 24px */
  --spacing-2xl: 2rem;     /* 32px */

  /* Responsive font sizes */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */

  /* Container max widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom responsive breakpoints */
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Layout specific widths */
  --layout-sidebar-width: 14.6875rem;    /* 235px */
  --layout-content-width: 42.25rem;      /* 676px */
  --layout-right-sidebar-width: 22rem;   /* 352px */
  --layout-max-width: 86.5rem;           /* 1384px */
  --layout-content-max-width: 66.25rem;  /* 1060px */
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #FFFFFF;
  }
}

/* Responsive font scaling */
@media (max-width: 640px) {
  :root {
    --text-xs: 0.6875rem;    /* 11px */
    --text-sm: 0.8125rem;    /* 13px */
    --text-base: 0.9375rem;  /* 15px */
    --text-lg: 1.0625rem;    /* 17px */
    --text-xl: 1.1875rem;    /* 19px */
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-size: var(--text-base);
}

/* Base responsive utilities */
.container-responsive {
  width: 100%;
  max-width: var(--layout-max-width);
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

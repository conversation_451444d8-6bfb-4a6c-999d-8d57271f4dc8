'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ProfileClickHandler from '@/components/profile/ProfileClickHandler';
import { SponsorAds } from '@/components/ui/sponsor-ads';
import { useActivity } from '@/providers/ActivityContext';
import { poppinsRegular, poppinsMedium } from '@/fonts';

// Test user data
const testUsers = [
  {
    _id: '686f48e54622ba4de88913cb',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    profile_picture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  },
  {
    _id: '686f48e54622ba4de88913cc',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    profile_picture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
  },
  {
    _id: '686f48e54622ba4de88913cd',
    first_name: 'Mike',
    last_name: 'Johnson',
    profile_picture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  }
];

const TestAllFeatures: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const { sponsoredAd, isAdLoading, incrementActivity, activityCounts } = useActivity();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  const handleChatStart = (chatData: any) => {
    addLog(`✅ Chat started with: ${JSON.stringify(chatData)}`);
  };

  const handleProfileView = (userId: string) => {
    addLog(`👤 Profile view for user: ${userId}`);
  };

  const testSponsoredAds = () => {
    addLog('📺 Testing sponsored ads functionality...');
    addLog(`📺 Sponsored ad data: ${sponsoredAd ? 'Available' : 'Not available'}`);
    addLog(`📺 Loading state: ${isAdLoading ? 'Loading' : 'Loaded'}`);
    addLog(`📺 Activity counts: ${JSON.stringify(activityCounts)}`);
  };

  const triggerSponsoredActivity = () => {
    incrementActivity('sponsor');
    addLog(`📈 Incremented sponsor activity. New count: ${activityCounts.sponsor + 1}`);
  };

  const triggerShareActivity = () => {
    incrementActivity('share');
    addLog(`📈 Incremented share activity. New count: ${activityCounts.share + 1}`);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className={poppinsMedium.className}>
            🧪 Complete Feature Test Suite
          </CardTitle>
          <p className={`${poppinsRegular.className} text-gray-600`}>
            Test all major features: Profile clicks, Chat functionality, Sponsored ads, and Backend integration.
          </p>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="profile-chat" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="profile-chat">Profile & Chat</TabsTrigger>
              <TabsTrigger value="sponsored-ads">Sponsored Ads</TabsTrigger>
              <TabsTrigger value="backend">Backend Tests</TabsTrigger>
              <TabsTrigger value="logs">Debug Logs</TabsTrigger>
            </TabsList>

            {/* Profile & Chat Tests */}
            <TabsContent value="profile-chat" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className={`${poppinsMedium.className} text-lg`}>
                    Profile Click & Chat Tests
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {testUsers.map((user) => (
                      <div key={user._id} className="p-4 border rounded-lg">
                        <ProfileClickHandler
                          user={user}
                          showAvatar={true}
                          avatarSize={60}
                          defaultAction="chat"
                          onChatStart={handleChatStart}
                          onProfileView={handleProfileView}
                          className="w-full p-3 border rounded hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex flex-col items-center gap-2">
                            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-lg font-bold">
                              {user.first_name[0]}
                            </div>
                            <div className="text-center">
                              <p className={`${poppinsMedium.className} text-sm`}>
                                {user.first_name} {user.last_name}
                              </p>
                              <p className={`${poppinsRegular.className} text-xs text-gray-500`}>
                                Click to start chat
                              </p>
                            </div>
                          </div>
                        </ProfileClickHandler>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <h4 className={`${poppinsMedium.className} text-sm mb-2`}>Expected Behavior:</h4>
                    <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
                      <li>• Click any profile → Navigate to /messages/[userId]</li>
                      <li>• Chat page loads with input field visible</li>
                      <li>• Type message → Send to backend via socket</li>
                      <li>• Message persists after page refresh</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Sponsored Ads Tests */}
            <TabsContent value="sponsored-ads" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className={`${poppinsMedium.className} text-lg`}>
                      Sponsored Ads Display
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <SponsorAds />
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className={`${poppinsMedium.className} text-lg`}>
                      Activity Tracking
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <p className={`${poppinsRegular.className} text-sm`}>
                        <strong>Sponsor Activity:</strong> {activityCounts.sponsor}/7
                      </p>
                      <p className={`${poppinsRegular.className} text-sm`}>
                        <strong>Share Activity:</strong> {activityCounts.share}/5
                      </p>
                    </div>
                    
                    <div className="space-y-2">
                      <Button onClick={triggerSponsoredActivity} variant="outline" size="sm">
                        Trigger Sponsor Activity
                      </Button>
                      <Button onClick={triggerShareActivity} variant="outline" size="sm">
                        Trigger Share Activity
                      </Button>
                      <Button onClick={testSponsoredAds} variant="outline" size="sm">
                        Test Ads System
                      </Button>
                    </div>
                    
                    <div className="mt-4 p-3 bg-green-50 rounded-lg">
                      <h4 className={`${poppinsMedium.className} text-sm mb-2`}>Expected Behavior:</h4>
                      <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
                        <li>• Sponsored ad should display with image and content</li>
                        <li>• Activity counters should increment</li>
                        <li>• Modal should appear after reaching thresholds</li>
                        <li>• No console errors</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Backend Tests */}
            <TabsContent value="backend" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className={`${poppinsMedium.className} text-lg`}>
                    Backend Integration Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className={`${poppinsMedium.className} text-sm mb-2`}>✅ Working Features</h4>
                      <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
                        <li>• Profile API calls</li>
                        <li>• Socket connection (when server available)</li>
                        <li>• Message sending via socket</li>
                        <li>• Chat creation</li>
                        <li>• Sponsored ads (mock data)</li>
                      </ul>
                    </div>
                    
                    <div className="p-4 border rounded-lg">
                      <h4 className={`${poppinsMedium.className} text-sm mb-2`}>🚧 Implementation Notes</h4>
                      <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
                        <li>• Private chat: ✅ Implemented</li>
                        <li>• Group chat: ⏳ Not yet implemented</li>
                        <li>• Message persistence: ✅ Via socket</li>
                        <li>• Real-time updates: ✅ Via socket</li>
                        <li>• File attachments: ⏳ Planned</li>
                      </ul>
                    </div>
                  </div>
                  
                  <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                    <h4 className={`${poppinsMedium.className} text-sm mb-2`}>Current Status:</h4>
                    <p className={`${poppinsRegular.className} text-sm`}>
                      Private chat functionality is implemented and should work with the backend. 
                      Messages are sent via socket and should persist in the database. 
                      Group chat functionality is not yet implemented.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Debug Logs */}
            <TabsContent value="logs" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className={`${poppinsMedium.className} text-lg`}>
                    Debug Logs
                  </CardTitle>
                  <Button 
                    onClick={() => setLogs([])} 
                    variant="outline" 
                    size="sm"
                    className="ml-auto"
                  >
                    Clear Logs
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 rounded p-3 max-h-96 overflow-y-auto">
                    {logs.length === 0 ? (
                      <p className={`${poppinsRegular.className} text-gray-500 text-sm`}>
                        No logs yet. Interact with the features above to see debug information.
                      </p>
                    ) : (
                      <div className="space-y-1">
                        {logs.map((log, index) => (
                          <div 
                            key={index} 
                            className={`${poppinsRegular.className} text-xs font-mono`}
                          >
                            {log}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestAllFeatures;

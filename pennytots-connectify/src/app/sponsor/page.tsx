'use client';

import React, { useEffect } from 'react';
import { useActivity } from '@/providers/ActivityContext';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

const SponsorAdScreen = () => {
  const { sponsoredAd, isAdLoading, resetModalVisibility } = useActivity();
  const router = useRouter();

  // Reset modal visibility when the component unmounts
  useEffect(() => {
    return () => {
      resetModalVisibility('sponsor');
    };
  }, [resetModalVisibility]);

  const handleClose = () => {
    resetModalVisibility('sponsor');
    router.back();
  };

  if (isAdLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900  mb-4"></div>
          <p className="text-gray-600">Loading Ad...</p>
        </div>
      </div>
    );
  }

  // Use sponsoredAd if available; otherwise, use a fallback image
  const adImageSrc = sponsoredAd?.image || '/sponsored-fallback.svg';

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {/* Header with hashtag and X button */}
      <div className="flex justify-between items-center p-4 bg-white shadow-sm">
        <h1 className="text-lg font-semibold text-gray-800">#Sponsored post</h1>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="p-2"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      {/* Content Container */}
      <div className="flex-1 flex flex-col justify-center items-center p-4 space-y-8">
        {/* Spacer */}
        <div className="flex-1" />

        {/* Ad Image */}
        <div className="w-full max-w-md ">
          <div className="relative w-full aspect-square">
            <Image
              src={adImageSrc}
              alt="Sponsored Advertisement"
              fill
              className="object-contain rounded-lg"
              onError={() => {
                console.error('Failed to load sponsored ad image');
              }}
            />
          </div>
          
          {/* Ad Details */}
          {sponsoredAd && (
            <div className="mt-4 text-center">
              {sponsoredAd.title && (
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  {sponsoredAd.title}
                </h2>
              )}
              {sponsoredAd.description && (
                <p className="text-gray-600 mb-4">
                  {sponsoredAd.description}
                </p>
              )}
              {sponsoredAd.company && (
                <p className="text-sm text-gray-500">
                  by {sponsoredAd.company}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Spacer */}
        <div className="flex-1" />

        {/* Close Ad Button */}
        <Button
          onClick={handleClose}
          className="bg-[#163E23] hover:bg-[#163E23]/90 text-white px-8 py-3 rounded-md font-medium"
        >
          Close Ad
        </Button>

        {/* Spacer */}
        <div className="flex-1" />
      </div>
    </div>
  );
};

export default SponsorAdScreen;

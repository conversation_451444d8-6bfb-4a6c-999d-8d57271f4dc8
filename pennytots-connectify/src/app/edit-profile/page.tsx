"use client"

import React from "react"
import EditProfileForm from "@/components/screens/edit-profile";
import MainLayout from "@/components/layout/main-layout"

export default function EditProfilePage() {
  return (
    <MainLayout currentScreen="edit-profile">
      <EditProfileForm
        onBack={() => {
          // handle back action
        }}
        onUpdate={(data?: any) => {
          // handle update action
        }}
      />
    </MainLayout>
  );
}
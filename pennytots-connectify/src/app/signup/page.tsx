'use client';

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Off, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { accountRegister } from "../../redux/user/hooks";
import { useRouter } from "next/navigation";
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';
import Sidebar from "@/components/sidebar/onboarding-sidebar";
import { montserratMedium, poppinsRegular, poppinsSemibold } from "@/fonts";
import '../../components/ui/phone-input.css'
import Link from 'next/link';
import {useSelector, useDispatch} from 'react-redux'
import { appLoading, setLoading } from "@/redux/main/reducer";
// import { setLoading } from "@/redux/main/reducer";


interface IInterest {
    _id: string;
    name: string;
}

export default function PersonalDetailsForm() {
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [email, setEmail] = useState('');
    const [pin, setPin] = useState('');
    const [showPin, setShowPin] = useState(false);
    const [error, setError] = useState('');
    // const [loading, setLoading] = useState(false);
    const [countryCode, setCountryCode] = useState("NG");
    const [phoneNumber, setPhoneNumber] = useState("");
    const [disableButton, setDisableButton] = useState(true)
    const router = useRouter();
    const isLoading = useSelector(appLoading)
    console.log(isLoading, 'isloading')
    
    const dispatch = useDispatch()

    const validateEmail = (email: string): boolean => {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(email);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        dispatch(setLoading(true));

        // Validation
        if (!firstName) {
            setError('Please enter your first name');
            
            return;
        }

        if (!validateEmail(email)) {
            setError('Please enter a valid email address');
            
            return;
        }

        try {
            const formattedPhone = phoneNumber.replace(/\s+/g, '');
            const code = formattedPhone.match(/^\+\d{1,3}/)?.[0] || "";
            const number = formattedPhone.slice(code.length);

            if (!code) {
                setError("Invalid phone number format");
                
                return;
            }

            const submitData = {
                first_name: firstName,
                last_name: lastName,
                email,
                phone_number: {
                    code: code,
                    number: number
                },
                country: countryCode,
                password: pin
            };

            // 1. Register the user
            await accountRegister(submitData, );
            router.push('/onboarding/welcome')

        } catch (error) {
            console.error('Registration error:', error);
            setError('Registration failed. Please try again.');
        }
    };

    const validateForm = () => {
        const isFirstNameValid = firstName.trim().length > 0;
        const isLastNameValid = lastName.trim().length > 0;
        const isEmailValid = email.trim().length > 0 && email.includes('@');
        const isPinValid = pin.trim().length >= 4; // Assuming PIN should be at least 4 characters
        const isPhoneValid = phoneNumber.trim().length > 0;

        return isFirstNameValid &&
            isLastNameValid &&
            isEmailValid &&
            isPinValid &&
            isPhoneValid

    };

    useEffect(() => {
        router.prefetch("/onboarding/welcome");
    }, []);

    // Update button state whenever form fields change
    useEffect(() => {
        setDisableButton(!validateForm());
    }, [firstName, lastName, email, pin, phoneNumber]);


    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, nextFieldId: string) => {
        if(e.key === 'Enter'){
            e.preventDefault()
            const nextInput = document.getElementById(nextFieldId)
            if(nextInput){
                nextInput.focus()

            }
        }
    }

    return (
      <div className="flex justify-center w-full">
          <div className="flex  w-full max-w-[1250px] justify-between items-center">
            <Sidebar />
            <div className="w-[1px] bg-[#D9D9D9] h-[751px] flex justify-center"></div>
            <div className="w-full max-w-[454px] flex  ">
                <div className="w-full">
                    <div className="mb-8">
                        <h1 className={`${poppinsSemibold.className} text-[#4F4F4F] text-[1.25rem]`}>Personal Details</h1>
                        <p className={`text-[#645D5D] text-[14px] ${montserratMedium.className}`}>Create your account with your phone number and email</p>
                    </div>

                    {error && (
                        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md text-sm">
                            {error}
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="mt-[2rem] flex flex-col gap-[1rem]">
                        <div className="flex flex-col gap-[4px]">
                            <Label htmlFor="phone">Phone Number</Label>
                            <PhoneInput
                                defaultCountry="ng"
                                value={phoneNumber}
                                onChange={(value) => setPhoneNumber(value)}
                                disabled={isLoading}
                                className="w-full gap-[10px] flex"
                               
                            />
                        </div>
                        <div className="flex flex-col gap-[4px]">
                            <Label htmlFor="firstName">First Name</Label>
                            <Input
                                id="firstName"
                                value={firstName}
                                onChange={(e) => setFirstName(e.target.value)}
                                onKeyDown={(e) => handleKeyDown(e, 'lastName')}
                               disabled={isLoading}
                                required
                            />
                        </div>

                        <div className="flex flex-col gap-[4px]">
                            <Label htmlFor="lastName">Last Name</Label>
                            <Input
                                id="lastName"
                                value={lastName}
                                onChange={(e) => setLastName(e.target.value)}
                                onKeyDown={(e) => handleKeyDown(e, 'email')}
                               disabled={isLoading}
                                required
                            />
                        </div>

                        <div className="flex flex-col gap-[4px]">
                            <Label htmlFor="email">Email</Label>
                            <Input
                                id="email"
                                type="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                onKeyDown={(e) => handleKeyDown(e, 'pin')}
                               disabled={isLoading}
                                required
                            />
                        </div>



                        <div className="flex flex-col gap-[4px]">
                            <Label htmlFor="pin">Create a PIN</Label>
                            <div className="relative">
                                <Input
                                    id="pin"
                                    type={showPin ? "text" : "password"}
                                    value={pin}
                                    onChange={(e) => setPin(e.target.value)}
                                    onKeyDown={(e) => handleKeyDown(e, 'buton')}
                                   disabled={isLoading}
                                    minLength={4}
                                    maxLength={4}
                                    pattern="\d{4}"
                                    title="Please enter a 4-digit PIN"
                                    required
                                />
                                <button
                                
                                    type="button"
                                    onClick={() => setShowPin(!showPin)}
                                    className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                                   
                                >
                                    {showPin ? <EyeOff size={18} /> : <Eye size={18} />}
                                </button>
                            </div>
                        </div>
      <Button
      id="button"
                   variant="formButton"
                   type="submit"
                   className={`w-full mt-[2rem] flex items-center justify-between ${disableButton
                     ? 'cursor-not-allowed text-[#5A5E5C33] bg-[#F2F4F3]'
                     : 'bg-[#163E23] text-white cursor-pointer'
                     }`}
                   disabled={disableButton}
                 >
                   {isLoading ? (
                     <span className="flex justify-center items-center gap-2">
                       <svg
                         className="animate-spin h-4 w-4 text-white"
                         xmlns="http://www.w3.org/2000/svg"
                         fill="none"
                         viewBox="0 0 24 24"
                       >
                         <circle
                           className="opacity-25"
                           cx="12"
                           cy="12"
                           r="10"
                           stroke="currentColor"
                           strokeWidth="4"
                         ></circle>
                         <path
                           className="opacity-75"
                           fill="currentColor"
                           d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                         ></path>
                       </svg>
                       Updating Profile...
                     </span>
                   ) : (
                     'Next'
                   )}
   
                   <ArrowRight width={26} height={21} className="" />
                 </Button>
                        <p className={`mt-[26px] text-center text-[#4F4F4F] text-[14px] ${poppinsRegular.className}`}>
                            Forgotten Password?
                            <Link href="/forgot-password" className="text-[#F2875D]">
                                Recover
                            </Link>
                        </p>
                    </form>
                </div>
            </div>

        </div>
      </div>
    );
}

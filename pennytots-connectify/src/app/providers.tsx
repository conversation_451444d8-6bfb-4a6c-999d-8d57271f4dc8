'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '../redux';
import { useState } from 'react';
import { ActivityProvider } from '../providers/ActivityContext';
import { AuthInitializer } from '../components/auth/AuthInitializer';
import { ModalProvider } from '../providers/ModalContext';

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
        refetchOnWindowFocus: false,
      },
    },
  }));

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <QueryClientProvider client={queryClient}>
          <ModalProvider>
            <ActivityProvider>
              <AuthInitializer />
              {children}
            </ActivityProvider>
          </ModalProvider>
        </QueryClientProvider>
      </PersistGate>
    </Provider>
  );
}

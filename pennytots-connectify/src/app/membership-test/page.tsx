'use client';

import React, { useState } from 'react';
import { MembershipScreen } from '@/components/settings/membership-screen';
import { SponsorshipPlansModal } from '@/components/ui/sponsorship-plans-modal';
import { Button } from '@/components/ui/button';
import { poppinsMedium } from '@/fonts';

export default function MembershipTestPage() {
  const [showMembership, setShowMembership] = useState(false);
  const [showPlansModal, setShowPlansModal] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className={`${poppinsMedium.className} text-2xl text-[#5A5E5C] mb-8`}>
          Membership Integration Test
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Test Membership Screen */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className={`${poppinsMedium.className} text-lg text-[#5A5E5C] mb-4`}>
              Membership Screen Test
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Test the main membership access screen with subscription status and action buttons.
            </p>
            <Button
              onClick={() => setShowMembership(!showMembership)}
              className="w-full"
            >
              {showMembership ? 'Hide' : 'Show'} Membership Screen
            </Button>
          </div>

          {/* Test Sponsorship Plans Modal */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className={`${poppinsMedium.className} text-lg text-[#5A5E5C] mb-4`}>
              Sponsorship Plans Modal Test
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Test the sponsorship plans modal with all four subscription tiers.
            </p>
            <Button
              onClick={() => setShowPlansModal(true)}
              className="w-full"
            >
              Open Sponsorship Plans
            </Button>
          </div>
        </div>

        {/* Features List */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className={`${poppinsMedium.className} text-lg text-[#5A5E5C] mb-4`}>
            Implemented Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className={`${poppinsMedium.className} text-md text-[#5A5E5C] mb-2`}>
                Membership Screen
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>✅ Days left display</li>
                <li>✅ Current plan information</li>
                <li>✅ Sponsorship plans button</li>
                <li>✅ Free plan button</li>
                <li>✅ Loading states</li>
                <li>✅ Error handling</li>
                <li>✅ Refresh functionality</li>
              </ul>
            </div>
            <div>
              <h3 className={`${poppinsMedium.className} text-md text-[#5A5E5C] mb-2`}>
                Sponsorship Plans
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>✅ Diamond Plan ($99.99)</li>
                <li>✅ Gold Plan ($79.99)</li>
                <li>✅ Silver Plan ($49.99)</li>
                <li>✅ Bronze Plan ($29.99)</li>
                <li>✅ Plan selection</li>
                <li>✅ Purchase flow</li>
                <li>✅ Current plan indication</li>
              </ul>
            </div>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className={`${poppinsMedium.className} text-lg text-[#5A5E5C] mb-4`}>
            API Integration
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className={`${poppinsMedium.className} text-md text-[#5A5E5C] mb-2`}>
                Subscription APIs
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• GET /subscription/get-subscription</li>
                <li>• POST /subscription/buy</li>
                <li>• GET /subscription/get-free-subscription</li>
              </ul>
            </div>
            <div>
              <h3 className={`${poppinsMedium.className} text-md text-[#5A5E5C] mb-2`}>
                Credit APIs
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• GET /credit/get-credit</li>
                <li>• POST /credit/buy</li>
                <li>• GET /credit/transaction-history</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Settings Integration */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className={`${poppinsMedium.className} text-lg text-[#5A5E5C] mb-4`}>
            Settings Integration
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            The membership functionality is integrated into the Settings page:
          </p>
          <ol className="text-sm text-gray-600 space-y-1">
            <li>1. Go to Settings page</li>
            <li>2. Click "Membership" in the Account section</li>
            <li>3. Modal opens with membership access screen</li>
            <li>4. Click "Sponsorship Plans" to see all plans</li>
            <li>5. Select and purchase desired plan</li>
          </ol>
        </div>

        {/* Conditional Rendering */}
        {showMembership && (
          <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className={`${poppinsMedium.className} text-lg text-[#5A5E5C]`}>
                Membership Screen Preview
              </h2>
              <Button
                variant="ghost"
                onClick={() => setShowMembership(false)}
                className="text-sm"
              >
                Close
              </Button>
            </div>
            <MembershipScreen />
          </div>
        )}
      </div>

      {/* Sponsorship Plans Modal */}
      <SponsorshipPlansModal
        isOpen={showPlansModal}
        onClose={() => setShowPlansModal(false)}
        currentPlan="silver" // Example current plan
      />
    </div>
  );
}

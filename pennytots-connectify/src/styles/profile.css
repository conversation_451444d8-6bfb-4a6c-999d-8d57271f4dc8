/* Profile Component Styles */

/* Profile Header Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Profile Container */
.profile-container {
  animation: fadeInUp 0.6s ease-out;
}

/* Profile Header */
.profile-header {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.profile-header-image {
  transition: transform 0.3s ease, filter 0.3s ease;
}

.profile-header-image:hover {
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* Profile Avatar */
.profile-avatar {
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.profile-avatar-ring {
  position: absolute;
  inset: -4px;
  border: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  padding: 4px;
}

/* Profile Stats */
.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  animation: slideInFromRight 0.6s ease-out 0.2s both;
}

.profile-stat-item {
  text-align: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.profile-stat-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.profile-stat-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  display: block;
}

.profile-stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* Profile Info Section */
.profile-info {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.profile-bio {
  line-height: 1.6;
  color: #4b5563;
  margin-bottom: 1rem;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.profile-detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.profile-detail-item:hover {
  color: #374151;
}

.profile-detail-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Social Links */
.profile-social-links {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.profile-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: #f3f4f6;
  color: #6b7280;
  transition: all 0.3s ease;
  text-decoration: none;
}

.profile-social-link:hover {
  background-color: #3b82f6;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Interest Tags */
.profile-interests {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.profile-interest-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-color: #e5e7eb;
  color: #374151;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  margin: 0.25rem;
}

.profile-interest-tag:hover {
  background-color: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

/* Profile Tabs */
.profile-tabs {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.profile-tab-nav {
  display: flex;
  gap: 2rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.profile-tab-nav::-webkit-scrollbar {
  display: none;
}

.profile-tab-button {
  position: relative;
  padding: 0.75rem 0.25rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: #6b7280;
  border: none;
  background: none;
  cursor: pointer;
  transition: color 0.2s ease;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

.profile-tab-button:hover {
  color: #374151;
}

.profile-tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.profile-tab-button::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
  transform: scaleX(0);
  transition: transform 0.2s ease;
}

.profile-tab-button.active::after {
  transform: scaleX(1);
}

/* Profile Content */
.profile-content {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

/* Post Cards */
.profile-post-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.profile-post-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
  transform: translateY(-2px);
}

.profile-post-content {
  line-height: 1.6;
  color: #374151;
}

.profile-post-image {
  border-radius: 0.5rem;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.profile-post-image:hover {
  transform: scale(1.02);
}

.profile-post-actions {
  display: flex;
  gap: 1rem;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #f3f4f6;
}

.profile-post-action {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #6b7280;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.profile-post-action:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.profile-post-action.liked {
  color: #ef4444;
}

.profile-post-action.liked:hover {
  background-color: #fef2f2;
}

/* Group Cards */
.profile-group-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.profile-group-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
  transform: translateY(-2px);
}

.profile-group-image {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.profile-group-info {
  flex: 1;
  min-width: 0;
}

.profile-group-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.profile-group-description {
  color: #6b7280;
  font-size: 0.75rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.profile-group-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Empty States */
.profile-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.profile-empty-icon {
  width: 3rem;
  height: 3rem;
  margin: 0 auto 1rem;
  color: #d1d5db;
}

.profile-empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.profile-empty-description {
  font-size: 0.875rem;
  color: #6b7280;
}

/* Loading States */
.profile-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .profile-tab-nav {
    gap: 1rem;
  }

  .profile-social-links {
    justify-content: center;
  }

  .profile-post-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

@media (max-width: 640px) {
  .profile-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.25rem;
  }

  .profile-stat-number {
    font-size: 1rem;
  }

  .profile-stat-label {
    font-size: 0.75rem;
  }
}

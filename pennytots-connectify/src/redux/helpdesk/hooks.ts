 import { tickets } from '@/api/helpdesk';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient } from '@/redux/user/hooks';
import { HelpDeskDTO } from './types';

export function useGetHelpDeskTickets() {
  return useQuery({
    queryKey: ['helpdesk-tickets'],
    queryFn: tickets.getHelpDeskTickets,
    staleTime: 30 * 1000, // 30 seconds - more frequent updates
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchInterval: 60 * 1000, // Auto-refetch every minute
    select: (data) => {
      // Sort tickets to show recently updated ones first
      if (!data) return data;
      return [...data].sort((a, b) => {
        // First sort by updatedAt if available, then by createdAt
        const aTime = new Date(a.updatedAt || a.createdAt).getTime();
        const bTime = new Date(b.updatedAt || b.createdAt).getTime();
        return bTime - aTime; // Most recent first
      });
    },
  });
}

export function useGetHelpDeskCategories() {
  return useQuery({
    queryKey: ['helpdesk-categories'],
    queryFn: tickets.getHelpDeskCategories,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

export function useCreateTicket() {
  return useMutation({
    mutationFn: (payload: any) => tickets.createTicket(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['helpdesk-tickets'] });
    },
  });
}

export function useGetHelpdeskMessages(helpdeskId: string) {
  return useQuery({
    queryKey: ['helpdesk-messages', helpdeskId],
    queryFn: () => tickets.getHelpDeskMessages(helpdeskId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!helpdeskId, // Only run query if helpdeskId exists
  });
}

export function useReplyHelpDeskMessage() {
  return useMutation({
    mutationFn: (payload: HelpDeskDTO) => tickets.replyHelpDeskMessage(payload),
    onSuccess: (_data, variables) => {
      // Invalidate messages for this specific ticket
      queryClient.invalidateQueries({ queryKey: ['helpdesk-messages', variables.helpdeskId] });

      // Invalidate tickets list to refresh and reorder
      queryClient.invalidateQueries({ queryKey: ['helpdesk-tickets'] });

      // Optionally, we can also refetch immediately to ensure quick updates
      queryClient.refetchQueries({ queryKey: ['helpdesk-tickets'] });
    },
  });
}




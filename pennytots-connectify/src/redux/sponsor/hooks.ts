import { sponsoredAd } from '@/api';
import { useQuery } from '@tanstack/react-query';

// Hook to get sponsored ads
export function useGetSponsoredAd() {
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['sponsoredAd'],
    queryFn: sponsoredAd.getSponsoredAd,
    // Configuration options for dynamic ad updates
    staleTime: 30 * 60 * 1000, // 30 minutes - ads change every 30 minutes
    refetchInterval: 30 * 60 * 1000, // Auto-refetch every 30 minutes
    refetchOnMount: true, // Refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
    retry: 1, // Only retry once to avoid excessive requests
    retryDelay: 5000, // Wait 5 seconds before retry
  });

  // Data structure from the API is { ad: { ...adData } }
  return {
    adData: data?.ad, // Extract the ad object
    isLoading,
    isError,
    refetch,
  };
}

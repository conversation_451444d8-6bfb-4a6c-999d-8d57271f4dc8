// Types for sponsored ads functionality

export interface SponsoredAd {
  _id: string;
  title?: string;
  description?: string;
  image: string;
  link?: string;
  company?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ActivityCounts {
  sponsor: number;
  share: number;
}

export interface ModalVisible {
  sponsor: boolean;
  share: boolean;
}

export interface ActivityContextType {
  activityCounts: ActivityCounts;
  incrementActivity: (type: keyof ActivityCounts) => void;
  modalVisible: ModalVisible;
  resetModalVisibility: (type: keyof ModalVisible) => void;
  sponsoredAd: SponsoredAd | null;
  isAdLoading: boolean;
}

export type ActivityType = 'sponsor' | 'share';

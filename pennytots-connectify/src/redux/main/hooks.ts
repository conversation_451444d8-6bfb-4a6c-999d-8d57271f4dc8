
import { main } from '../../api/main';
import { useQuery } from '@tanstack/react-query';

interface SearchPayload {
  search: string;
  searchType: string;
}

export default function useSearch(payload: SearchPayload) {
  return useQuery({
    queryKey: ['search', payload],
    queryFn: () => main.search({ queryKey: ['search', payload] }),
    enabled: payload.search.length > 2, //enable if search has value greater than 2
  });
}

export function useGetUnreadNotifications(enabled: boolean) {
  return useQuery({
    queryKey: ['unread-notifications'],
    queryFn: main.unreadNotifications,
    refetchInterval: 60000, // 1 min
    enabled: enabled == true,
  });
}

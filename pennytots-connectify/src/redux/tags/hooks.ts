import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { tags } from '@/api/tags';

// Export the hooks for React Query integration
export function useGetTags() {
  return useQuery({
    queryKey: ['tags'],
    queryFn: tags.getTags,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

export function useSetUserTags() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (interests: string[]) => tags.setUserTags(interests),
    onSuccess: () => {
      // Invalidate user info to refresh the data
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['userInfo'] });
    },
  });
}

export function useCreateTopic() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ content, topicTags }: { content: string; topicTags: string[] }) =>
      tags.createTopic(content, topicTags),
    onSuccess: () => {
      // Invalidate topics to refresh the list
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['topic'] });
    },
  });
}

// Re-export the API functions for direct use
export { tags };
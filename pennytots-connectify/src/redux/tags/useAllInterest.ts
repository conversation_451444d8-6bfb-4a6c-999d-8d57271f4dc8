import { useState, useEffect } from "react";
import { tags } from "@/api/tags";

interface IInterest {
  _id: string;
  name: string;
  description?: string;
  color?: string;
  createdAt?: string;
  updatedAt?: string;
}

export const useInterests = () => {
  const [allInterests, setAllInterests] = useState<IInterest[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchInterests = async () => {
      try {
        setLoading(true);

        const response = await tags.getTags();
        const interestData = response.data ? [...response.data] : [];

        // Sort interests with 'business' first, then alphabetically
        interestData.sort((a: IInterest, b: IInterest) => {
          if (a.name.toLowerCase() === "business") return -1;
          if (b.name.toLowerCase() === "business") return 1;
          return a.name.localeCompare(b.name);
        });

        setAllInterests(interestData);
        setError(null);
      } catch (err) {
        console.error("Error fetching interests:", err);
        setError(err as Error);

        // Set fallback interests if API fails
        const fallbackInterests: IInterest[] = [
          { _id: 'business', name: 'Business' },
          { _id: 'technology', name: 'Technology' },
          { _id: 'health', name: 'Health & Fitness' },
          { _id: 'education', name: 'Education' },
          { _id: 'entertainment', name: 'Entertainment' },
        ];
        setAllInterests(fallbackInterests);
      } finally {
        setLoading(false);
      }
    };

    fetchInterests();
  }, []);

  const refetchInterests = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await tags.getTags();
      const interestData = response.data ? [...response.data] : [];

      interestData.sort((a: IInterest, b: IInterest) => {
        if (a.name.toLowerCase() === "business") return -1;
        if (b.name.toLowerCase() === "business") return 1;
        return a.name.localeCompare(b.name);
      });

      setAllInterests(interestData);
      setError(null);
    } catch (err) {
      console.error("Error refetching interests:", err);
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  return {
    allInterests,
    loading,
    error,
    refetch: refetchInterests
  };
};

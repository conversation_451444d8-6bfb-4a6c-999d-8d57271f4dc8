export interface IInterest {
  _id: string;
  name: string;
  description?: string;
  color?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ITag {
  _id: string;
  name: string;
  description?: string;
  color?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ICreateTopicResponse {
  topic: {
    _id: string;
    content: string;
    author: {
      _id: string;
      first_name: string;
      last_name: string;
      email: string;
    };
    tags: ITag[];
    createdAt: string;
    updatedAt: string;
    likes?: number;
    comments?: number;
  };
}

export interface IUserTagsResponse {
  message: string;
  user: {
    _id: string;
    interests: string[];
  };
}

export interface ITagsListResponse {
  data: ITag[];
  message?: string;
}

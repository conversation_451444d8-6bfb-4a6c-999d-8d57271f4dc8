import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { tags } from '../../api/tags';
import { updateUserInfo } from '../user/hooks';

interface UseAreaOfInterestsOptions {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  navigateTo?: string;
  replace?: boolean;
  navigateBack?: boolean;
  showSuccessMessage?: boolean;
}

export const useAreaOfInterests = (options: UseAreaOfInterestsOptions = {}) => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const updateInterests = async (interests: string[]) => {
    setLoading(true);
    
    try {
      // Set user tags
      await tags.setUserTags(interests);
      
      // Update user info
      await updateUserInfo();
      
      // Show success message if requested
      if (options.showSuccessMessage) {
        // You can implement a toast notification here
        console.log('Your interests have been updated successfully!');
      }
      
      // Handle navigation based on options
      if (options.navigateTo) {
        if (options.replace) {
          router.replace(options.navigateTo);
        } else {
          router.push(options.navigateTo);
        }
      } else if (options.navigateBack) {
        router.back();
      }
      
      options.onSuccess?.();
    } catch (error: any) {
      const errorMsg = error?.response?.data?.message?.includes(
        'Please select at least one interest'
      )
        ? 'Please select at least one interest to continue.'
        : error?.response?.data?.message || 'Failed to update interests. Please try again.';

      console.error('Error updating interests:', error);
      options.onError?.(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    updateInterests
  };
};

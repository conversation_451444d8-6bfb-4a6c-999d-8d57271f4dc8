import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { tags } from '../../api/tags';
import { updateUserInfo, queryClient } from '../user/hooks';

interface UseTagsOptions {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  navigateTo?: string;
  replace?: boolean;
  navigateBack?: boolean;
}

export const useTags = (options: UseTagsOptions = {}) => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const updateAreaOfInterests = async (interests: string[], welcomeMessage?: string) => {
    setLoading(true);
    try {
      // Set user tags
      await tags.setUserTags(interests);
      
      // Update user info
      await updateUserInfo();
      
      // Create welcome topic if message is provided
      if (welcomeMessage) {
        const response = await tags.createTopic(welcomeMessage, interests);
        
        if (response.topic && response.topic._id) {
          // Navigate to topic list with the new post
          (`/topic-list?postid=${response.topic._id}`);
          router.push('/navigation');
          
          // Invalidate topics query to refresh the list
          queryClient.invalidateQueries({ queryKey: ['topics'] });
          
          options.onSuccess?.();
          return response;
        } else {
          console.warn('No topic returned from createTopic');
        }
      } else {
        // Just update interests without creating a topic
        // Handle navigation based on options
        if (options.navigateTo) {
          if (options.replace) {
            router.replace(options.navigateTo);
          } else {
            router.push(options.navigateTo);
          }
        } else if (options.navigateBack) {
          router.back();
        }
        
        options.onSuccess?.();
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.message?.includes(
        'Please select at least one interest'
      )
        ? 'Please select at least one interest to continue.'
        : error?.response?.data?.message || 'Failed to update interests. Please try again.';

      console.error('Error updating interests:', error);
      options.onError?.(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    updateAreaOfInterests
  };
};

import { profile } from "@/api/profile";
import { useRouter } from 'next/navigation'
import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

interface ProfileUpdateData {
  first_name?: string;
  last_name?: string;
  bio?: string;
  website?: string;
  facebook?: string;
  linkedin?: string;
  company?: string;
  company_position?: string;
  city?: string;
  state?: string;
  age?: number | string; // Allow both number and string for age ranges
  gender?: string;
  [key: string]: any;
}

export const useUpdateProfile = () => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  const updateProfile = async (
    formData: ProfileUpdateData,
    redirectPath?: string
  ) => {
    setLoading(true);

    try {
      await profile.updateProfile(formData);

    //   showModal({
    //     modalVisible: true,
    //     title: "Success",
    //     message: "Updated successfully",
    //     setModalVisible: hideModal,
    //     type: "success-alert",
    //     handleConfirm: hideModal,
    //     handleAlert: hideModal,
    //   });

      if (redirectPath) {
        router.push(redirectPath);
      }
    } catch (error) {
      console.error("Update Profile Error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateProfileWithImages = async (
    formData: ProfileUpdateData,
    profilePicture?: File,
    headerImage?: File,
    redirectPath?: string
  ) => {
    setLoading(true);

    try {
    const updaRes =  await profile.updateProfileWithImages(formData, profilePicture, headerImage);  

    console.log('Profile update response:', updaRes);

      console.log('Profile update with images successful!');

      // Invalidate user profile queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['userInfo'] });
      queryClient.invalidateQueries({ queryKey: ['profile'] });

      if (redirectPath) {
        router.push(redirectPath);
      }
    } catch (error) {
      console.error("Update Profile with Images Error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    updateProfile,
    updateProfileWithImages,
    loading,
  };
};

export const useProfileById = (userId: string) => {
  return useQuery({
    queryKey: ['profile', userId],
    queryFn: () => profile.getProfileById(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useUploadProfilePicture = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => profile.uploadProfilePicture(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
  });
};

export const useUploadHeaderImage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => profile.uploadHeaderImage(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
  });
};

"use client";

import { clearU<PERSON><PERSON><PERSON>, setUser, update<PERSON>ser, IsLoggedIn, userAuthInfo } from './reducer';
import { IAccountSignInDTO, reportUserDTO } from "./types"
import { clearDeviceToken, setLoading } from '../main/reducer';
import { useMutation, useQueryClient, QueryClient } from '@tanstack/react-query';
import { store } from '..';
import { user } from '../../api/user';
import { Axios } from '../../api/axios';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import { toast } from 'react-toastify';

// Simple alert function to replace modal system
const showAlert = (message: string) => {
  if (typeof window !== 'undefined') {
    alert(message);
  }
};

// Define missing types locally
interface ChangePinParams {
  currentPIN: string;
  newPIN: string;
}

interface IAccountRegisterDTO {
  first_name: string;
  last_name: string;
  phone_number: {
    code: string;
    number: string;
  };
  password: string;
  email?: string;
}

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      // staleTime: ,
    },
  },
});

export const accountLogin = async ({ phone_number, password }: IAccountSignInDTO, router: any) => {

  try {
    store.dispatch(setLoading(true));
    const response = await user.login({ phone_number, password });

    // Set Axios default headers with the token
    Axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token.token}`;

    // Set user in the store
    store.dispatch(
      setUser({
        userId: response.data._id,
        authInfo: response.data,
        isLoggedIn: true,
        token: response.data.token.token,
      }),
    );

    // Fetch and update user profile after successful login
    try {
      const profileData = await user.profile();
      store.dispatch(updateUser(profileData));
    } catch (profileError) {
      console.warn("Failed to fetch profile after login:", profileError);
      // Don't throw error here as login was successful
    }

     if (response.data.interests && response.data.interests.length > 0) {
      router.replace("/home");
    } else {
      router.replace("/onboarding/welcome");
    }

    console.log("I ran X 2");
  } catch (error: any) {
    console.log(error, "error")
    // Re-throw the error so the login component can handle it
    throw error;
    // ShowAlert({
    //   type: 'error',
    //   className: 'Error',
    //   message: error?.data?.message || "Hello",
    // });
  } finally {
    // Ensure loading state is set to false
    store.dispatch(setLoading(false));
  }
};

export const accountRegister = async (payload: IAccountRegisterDTO) => {


  try {
    // Attempt to register the user
    store.dispatch(setLoading(true));
    const response = await user.register(payload);
    console.log(response, 'response')

    // Set authorization header with token
    Axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token.token}`;

    // Set user in the store
    store.dispatch(
      setUser({
        userId: response.data._id,
        authInfo: response.data,
        isLoggedIn: true,
        token: response.data.token.token,
      }),
    );

    // Fetch and update user profile after successful registration
    try {
      const profileData = await user.profile();
      store.dispatch(updateUser(profileData));
    } catch (profileError) {
      console.warn("Failed to fetch profile after registration:", profileError);
      // Don't throw error here as registration was successful
    }

    // Optionally handle notifications or other logic
    setTimeout(() => {
      // Wait a few seconds before setting notifications
      // useNotification();
    }, 1000);
    showAlert('Updated successfully');

  } catch (error: any) {
    console.log(error, "error")
    console.log("Error message from backend:", error?.data?.message); // Log the specific error message

    // Show error alert
    showAlert(error?.data?.message || error?.message || "Something Went Wrong Check Your Internet Connection and try again");

    // ShowAlert({
    //   type: 'error',
    //   className: 'Error',
    //   message: error?.data?.message || "Hello",
    // });
  } finally {
    // Ensure loading state is set to false
    store.dispatch(setLoading(false));
  }
  

  //   return data; // Return data in case further processing is needed

  // }
  //  catch (error: unknown) {
  //   if (error instanceof Error) {
  //     return {
  //       error: error.message
  //     }

  //   } 
  //    if (error && typeof error === 'object' &&  'Email already exist' in error) {
  //     showModal({
  //       modalVisible: true,
  //       title: 'Oops',
  //       message: 'email already in use',
  //       setModalVisible: hideModal, // Function to close the modal
  //       type: 'alert',
  //       handleConfirm: () => {
  //         console.log('Confirmed!');
  //         hideModal();
  //       },
  //       handleAlert: () => {
  //         console.log('Alert handled!');
  //         hideModal();
  //       },

  //     });

  //   } 
 

  //   throw error; 
    
  // } finally {
  //   // Always stop the loading state
  //   store.dispatch(setLoading(false));
  // }
};



export const updateUserInfo = async () => {
  const data = await user.profile();

  store.dispatch(updateUser(data));
};

// Hook to fetch user profile
export function useGetProfile() {
  return useMutation({
    mutationFn: async () => {
      const profileData = await user.profile();
      return profileData;
    },
    onSuccess: (profileData) => {
      store.dispatch(updateUser(profileData));
    },
    onError: (error) => {
      console.error("Failed to fetch profile:", error);
    },
  });
}

// Hook to automatically fetch profile on component mount (for authenticated users)
export function useAutoFetchProfile() {
  const getProfile = useGetProfile();

  return {
    fetchProfile: getProfile.mutate,
    isLoading: getProfile.isPending,
    error: getProfile.error,
  };
}

// Hook to ensure user profile is always fresh
export function useEnsureFreshProfile() {
  const isLoggedIn = useSelector(IsLoggedIn);
  const userAuth = useSelector(userAuthInfo);
  const getProfile = useGetProfile();

  const refreshProfile = () => {
    if (isLoggedIn) {
      getProfile.mutate();
    }
  };

  // Auto-fetch profile if user is logged in but profile is missing
  useEffect(() => {
    if (isLoggedIn && !userAuth) {
      refreshProfile();
    }
  }, [isLoggedIn, userAuth]);

  return {
    refreshProfile,
    isLoading: getProfile.isPending,
    error: getProfile.error,
    userProfile: userAuth,
    isLoggedIn,
  };
}

export function useSendPhoneVerificationCode() {
  return useMutation({
    mutationFn: (payload: any) => {
      return user.sendPhoneVerification(payload);
    }
  });
}

export function useValidatePhoneCode() {
  return useMutation({
    mutationFn: (payload: any) => {
      return user.validatePhoneNumber(payload);
    },
    onSuccess: (response: any) => {
      console.log(response, ' data from response');
      store.dispatch(updateUser(response.user));
      showAlert(response.message);
    },
  });
}

export function useSendResetPasswordToken() {
  return useMutation({
    mutationFn: (payload: any) => {
      return user.sendResetPasswordToken(payload);
    },
    onSuccess: (response: any) => {
      showAlert(response.message);
    },
  });
}

export function useReportUser() {
  return useMutation({
    mutationFn: (payload: reportUserDTO) => {
      return user.reportUser(payload.userId, payload.description);
    },
    onSuccess: (response: any) => {
      showAlert(response.message);
    },
  });
}

export function useDeactivateAccount() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async () => {
      const response: any = user.deactivateAccount();
      return response.data;
    },
    onSuccess: () => {
      // Perform logout after successful deactivation
      store.dispatch(clearUserAuth());
      store.dispatch(clearDeviceToken());
      setTimeout(() => {
        queryClient.invalidateQueries();
        delete Axios.defaults.headers.common['Authorization'];
      }, 1000);
    },
    onError: (error: any) => {
      console.error('Error deactivating account:', error);
    },
  });

  return mutation;
};

export function useDeleteAccount() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async () => {
      const response: any = user.deleteAccount();
      return response.data;
    },
    onSuccess: () => {
      // Perform logout after successful deactivation
      store.dispatch(clearUserAuth());
      store.dispatch(clearDeviceToken());
      setTimeout(() => {
        queryClient.invalidateQueries();
        delete Axios.defaults.headers.common['Authorization'];
      }, 1000);
      toast.success('Account deleted successfully');
    },
    onError: (error: any) => {
      console.error('Error deactivating account:', error);
    },
  });

  return mutation;
};

export const logout = () => {
  const router = useRouter()
  //clear all saved cache for user
  store.dispatch(clearUserAuth()); // clear user auth from redux
  store.dispatch(clearDeviceToken()); //delete device token

  //wait for route switch before clearing cache to prevent api calls
  setTimeout(() => {
    queryClient.invalidateQueries();
    delete Axios.defaults.headers.common['Authorization'];
  }, 1000);
  toast.success('Logged out successfully');
  router.push('/'); 
};

export function useChangePin() {
  return useMutation({
    mutationFn: (params: ChangePinParams) => user.changeUserPin(params)
  });
}

export interface IncreaseOrReduce {
  type: "increase" | "reduce";
}

export interface QuizQuestion {
  _id: string;
  question: string;
  options: string[];
  answer: string;
  country?: string;
  difficulty?: string;
  category?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface QuizState {
  currentQuestion: QuizQuestion | null;
  score: number;
  gameOver: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface QuizAnswer {
  questionId: string;
  selectedOption: string;
  isCorrect: boolean;
  timeSpent: number;
}

export interface QuizSession {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  totalQuestions: number;
  correctAnswers: number;
  score: number;
  answers: QuizAnswer[];
}

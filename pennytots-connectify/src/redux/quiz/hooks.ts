import { quiz } from '@/api';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { country } from '@/redux/main/reducer';

export function useGetQuestionsNew() {
  const { data, isLoading, refetch, isFetching } = useQuery({
    queryKey: ['quiz'],
    queryFn: quiz.getQuestionsOld,
    staleTime: 0, // Disable caching
    refetchOnMount: false, // Do not refetch on mount
    refetchOnWindowFocus: false, // Do not refetch on window focus
  });
  return { data, isLoading, refetch, isFetching };
}

export function useGetQuestions() {
  const countryReal = useSelector(country);
  const { data, isLoading, refetch, isFetching } = useQuery({
    queryKey: ['quiz', countryReal],
    queryFn: () => quiz.getQuestions(countryReal),
    staleTime: 0, // Disable caching
    refetchOnMount: false, // Do not refetch on mount
    refetchOnWindowFocus: false, // Do not refetch on window focus
  });

  return { data, isLoading, refetch, isFetching };
}

export function useChangeUserPennytots() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (type: "reduce" | "increase") => {
      return await quiz.changeUserPennyTots(type);
    },
    onSuccess: () => {
      // Invalidate the credit query
      queryClient.invalidateQueries({ queryKey: ['credit'] });
    },
  });
}

export function useStakeUserPennytots() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ type, amount }: { type: "reduce" | "increase", amount: number }) => {
      return await quiz.stakeUserPennyTots(type, amount);
    },
    onSuccess: () => {
      // Invalidate the credit query
      queryClient.invalidateQueries({ queryKey: ['credit'] });
    },
  });
}

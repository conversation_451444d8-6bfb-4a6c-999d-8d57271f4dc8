import { group } from '@/api/group';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export function useMyGroups() {
  return useQuery({
    queryKey: ['my-groups'],
    queryFn: group.getMyGroups,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useFetchGroups(userInfo: any) {
  return useQuery({
    queryKey: ['user-groups', userInfo?._id],
    queryFn: () => group.fetchUserGroups(userInfo),
    enabled: !!userInfo?._id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useSuggestedGroups() {
  return useQuery({
    queryKey: ['suggested-groups'],
    queryFn: group.getSuggestedGroups,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useGetGroup(id: string) {
  return useQuery({
    queryKey: ['group', id],
    queryFn: () => group.getGroup(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (formData: FormData) => group.createGroup(formData),
    onSuccess: () => {
      // Invalidate and refetch groups after successful creation
      queryClient.invalidateQueries({ queryKey: ['my-groups'] });
      queryClient.invalidateQueries({ queryKey: ['suggested-groups'] });
    },
  });
}

export function useJoinGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (groupId: string) => group.joinGroup(groupId),
    onSuccess: () => {
      // Invalidate and refetch groups after joining
      queryClient.invalidateQueries({ queryKey: ['my-groups'] });
      queryClient.invalidateQueries({ queryKey: ['suggested-groups'] });
    },
  });
}

export function useLeaveGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (groupId: string) => group.leaveGroup(groupId),
    onSuccess: () => {
      // Invalidate and refetch groups after leaving
      queryClient.invalidateQueries({ queryKey: ['my-groups'] });
      queryClient.invalidateQueries({ queryKey: ['suggested-groups'] });
    },
  });
}

export function useUpdateGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { id: string; data: any }) => group.updateGroup(payload),
    onSuccess: (data, variables) => {
      // Invalidate specific group and groups list
      queryClient.invalidateQueries({ queryKey: ['group', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['my-groups'] });
    },
  });
}

export function useMuteGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: {
      contentId: string;
      type: 'group';
      action: 'mute' | 'unmute';
    }) => group.muteGroup(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-groups'] });
    },
  });
}

export function useReportGroup() {
  return useMutation({
    mutationFn: ({ groupId, description }: { groupId: string; description: string }) => 
      group.reportGroup(groupId, description),
  });
}

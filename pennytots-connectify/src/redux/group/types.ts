export interface GroupParticipant {
  _id: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
  bio?: string;
  website?: string;
  linkedin?: string;
  facebook?: string;
  twitter?: string;
  instagram?: string;
  company?: string;
  company_position?: string;
  interests?: string[];
  header_image?: string;
  muted?: boolean;
  age?: string;
  city?: string;
  state?: string;
  gender?: string;
  phone_number_verified?: boolean;
  admin?: boolean;
}

export interface GroupProps {
  lastMessage: any;
  profilePicture: string | Blob | undefined;
  unreadCount: number;
  isAdmin: any;
  _id: string;
  image?: string;
  name: string;
  description?: string;
  createdBy: {
    first_name: string;
    last_name: string;
    _id: string;
    profile_picture?: string;
  };
  participants: GroupParticipant[];
  muted?: boolean;
  memberCount?: number;
  isJoined?: boolean;
  tag?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateGroupData {
  name: string;
  description: string;
  tag: string;
  file?: File;
}

export interface UpdateGroupData {
  name?: string;
  description?: string;
  tag?: string;
}

export interface GroupChatMessage {
  _id: string;
  message: string;
  sender: GroupParticipant;
  createdAt: string;
  type: 'text' | 'image' | 'file';
  file?: string;
  fileName?: string;
  fileSize?: number;
}

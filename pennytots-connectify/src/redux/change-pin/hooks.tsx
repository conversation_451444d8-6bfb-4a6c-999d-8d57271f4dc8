import { changePinAPI } from "@/api/change-pin";
import { useMutation } from "@tanstack/react-query";

export const useChangePin = () => {
  return useMutation({
    mutationFn: changePinAPI,
    onSuccess: (data) => {
      console.log('PIN changed successfully:', data);
    },
    onError: (error: any) => {
      console.error('Error changing PIN:', error);
      // You can add additional error handling here
    },
  });
};
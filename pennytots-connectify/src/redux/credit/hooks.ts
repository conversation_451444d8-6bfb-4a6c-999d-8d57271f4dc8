import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/api/axios';

// API functions for credits
const getCredit = async () => {
  const { data } = await Axios.get('/credit/get-credit');
  return data.credit;
};

const getSubscription = async () => {
  const { data } = await Axios.get('/subscription/get-subscription');
  return data;
};

const getFreeCredit = async () => {
  const { data } = await Axios.get('/credit/get-free-credit');
  return data;
};

const getFreeSubscription = async () => {
  const { data } = await Axios.get('/subscription/get-free-subscription');
  return data;
};

const buyCredit = async (data: any) => {
  return await Axios.post('/credit/buy', data);
};

const buySubscription = async (data: any) => {
  return await Axios.post('/subscription/buy', data);
};

const restoreSubscription = async (data: any) => {
  const { data: responseData } = await Axios.post('/subscription/restore-purchases', data);
  return responseData;
};

const getCreditTransactionHistory = async () => {
  const { data } = await Axios.get('/credit/transaction-history');
  return data.transactions.docs;
};

// Hooks
export function useGetCredit() {
  return useQuery({
    queryKey: ['credit'],
    queryFn: getCredit,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

export function useGetSubscription() {
  return useQuery({
    queryKey: ['subscription'],
    queryFn: getSubscription,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

export function useGetCreditTransactionHistory() {
  return useQuery({
    queryKey: ['credit-history'],
    queryFn: getCreditTransactionHistory,
    staleTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
  });
}

export function useGetFreeCredit() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: getFreeCredit,
    onSuccess: (response: any) => {
      queryClient.invalidateQueries({ queryKey: ['credit'] });
    },
  });
}

export function useGetFreeSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: getFreeSubscription,
    onSuccess: (response: any) => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
  });
}

export function useBuyCredit() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: buyCredit,
    onSuccess: (response: any) => {
      queryClient.invalidateQueries({ queryKey: ['credit'] });
      queryClient.invalidateQueries({ queryKey: ['credit-history'] });
    },
  });
}

export function useBuySubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: buySubscription,
    onSuccess: (response: any) => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
  });
}

export function useRestoreSubscription() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: restoreSubscription,
    onSuccess: (response: any) => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
  });
}

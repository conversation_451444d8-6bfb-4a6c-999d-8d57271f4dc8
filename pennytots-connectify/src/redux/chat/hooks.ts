import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { chat } from "../../api/chat";

export function useMyChats() {
  return useQuery({
    queryKey: ["my-chats"],
    queryFn: () => chat.getChats(),
    refetchOnWindowFocus: true,
  });
}

export function useMyBlockedChats() {
  return useQuery({
    queryKey: ["my-blocked-chats"],
    queryFn: () => chat.getBlockedChats(),
    refetchOnWindowFocus: true,
  });
}

export function useBlockUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (accountId: string) => chat.blockUser(accountId),
    onSuccess: (data: any) => {
      const message =
        data.message === "Blocked successfully" ? "User blocked" : data.message;

      // You can add toast notification here
      console.log("Success:", message);

      queryClient.invalidateQueries({ queryKey: ["my-chats"] });
      queryClient.invalidateQueries({ queryKey: ["my-blocked-chats"] });
    },
  });
}

export function useUnblockUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (accountId: string) => chat.unblockUser(accountId),
    onSuccess: (data: any) => {
      const message =
        data.message === "Chat has been unblocked successfully"
          ? "User unblocked"
          : data.message;

      // You can add toast notification here
      console.log("Success:", message);

      queryClient.invalidateQueries({ queryKey: ["my-chats"] });
      queryClient.invalidateQueries({ queryKey: ["my-blocked-chats"] });
    },
  });
}

export function useMuteNotification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (item: {
      _id: string;
      muted?: boolean;
      accountId: { _id: string };
    }) => {
      const payload = {
        contentId: item.accountId._id,
        type: "user" as const,
        action: item.muted ? ("unmute" as const) : ("mute" as const),
      };
      return await chat.muteNotification(payload);
    },
    onSuccess: (_, variables) => {
      const message = variables.muted
        ? "Chat unmuted"
        : "This chat has now been muted";

      // You can add toast notification here
      console.log("Success:", message);

      queryClient.invalidateQueries({ queryKey: ["my-chats"] });
      queryClient.invalidateQueries({ queryKey: ["my-blocked-chats"] });
    },
  });
}

export function useGetUserChats() {
  return useMutation({
    mutationFn: (variables: { userId: string; page: number; limit: number }) =>
      chat.getUserChats(variables),
  });
}

export function useGetBotChats() {
  return useMutation({
    mutationFn: (variables: { page: number; limit: number }) =>
      chat.getBotChats(variables),
  });
}

export function useSearchChats() {
  return useMutation({
    mutationFn: (variables: { chatId: string; search: string }) =>
      chat.getSearchChats(variables.chatId, variables.search),
  });
}

export function useGetOrCreateUserChat() {
  return useMutation({
    mutationFn: (userId: string) => chat.getOrCreateUserChat(userId),
  });
}

export function useChatActions() {
  const { mutateAsync: blockUser } = useBlockUser();
  const { mutateAsync: unBlockUser } = useUnblockUser();
  const { mutateAsync: muteNotification } = useMuteNotification();
  const { mutateAsync: getUserChats } = useGetUserChats();
  const { mutateAsync: getBotChats } = useGetBotChats();
  const { mutateAsync: searchChats } = useSearchChats();
  const { mutateAsync: getOrCreateUserChat } = useGetOrCreateUserChat();
  const {
    data: chatList,
    isLoading,
    isFetching,
    error,
    refetch: refreshMyChat,
  } = useMyChats();

  return {
    searchChats,
    blockUser,
    getBotChats,
    unBlockUser,
    muteNotification,
    getUserChats,
    getOrCreateUserChat,
    chatList,
    isLoading,
    isFetching,
    error,
    refreshMyChat,
  };
}

import { topic } from '@/api/topic';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ICreateCommentDTO, ICreateSubCommentDTO } from './types';
import { AxiosResponse } from 'axios';

export function useTopics(p0?: { topicId: any; }){
  // Always fetch all topics regardless of topicId
  return useQuery({
    queryKey: ['topic'],
    queryFn: topic.getTopics,
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

export function useSingleTopic(topicId: string) {
  return useQuery({
    queryKey: ['topic', topicId],
    queryFn: () => topic.getSingleTopic(topicId)
  });
}

export function useFetchTopic(userInfo: any) {
  return useQuery({
    queryKey: ['userTopics', userInfo],
    queryFn: () => topic.fetchTopic(userInfo)
  });
}
export function useDeleteTopic(userId: (state: any) => any) {
  return useMutation({
    mutationFn: (topicId: string) => {
      if (typeof topicId !== 'string') {
        console.error('Invalid topicId:', topicId);
        throw new Error('Invalid topicId');
      }
      return topic.deleteTopic(topicId);
    }
  });
}

export function useTopicComments(topicId: string) {
  return useQuery({
    queryKey: ['topic-comments', topicId],
    queryFn: () => topic.getTopicComments(topicId)
  });
}

type ConvosData = AxiosResponse<any>; // Update 'any' to your actual data type if known

export function useTopicSubComments(commentId: string) {
  const { data, isLoading: isQueryLoading } = useQuery({
    queryKey: ['topic-sub-comments', commentId],
    queryFn: () => topic.getTopicSubComments(commentId!),
    enabled: commentId !== null,
  });

  const isLoading = commentId !== null && isQueryLoading;

  return { data, isLoading };
}

export function useConvos(options = {}) {
  return useQuery({
    queryKey: ['convos'],
    queryFn: topic.getConvos,
    ...options
  });
}

export function useCreateComment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: ICreateCommentDTO) => {
      return topic.createComment(payload);
    },
    onSuccess: (response: any, variables: any, context: any) => {
      queryClient.invalidateQueries({ queryKey: ['topic-comments'] });
    },
  });
}

export function useCreateSubComment() {
  return useMutation({
    mutationFn: (payload: ICreateSubCommentDTO) => {
      return topic.createSubComment(payload);
    }
  });
}

export const useSearchTopics = (tabIndex: number,searchInput:string, setDataFunctions: any) => {
  
  // Map tabIndex to corresponding searchType
  const searchTypes = [
    'users',           // tabIndex 0
    'chats',           // tabIndex 1
    'groups',          // tabIndex 2
    'topics',          // tabIndex 3
    'topic-comments',  // tabIndex 4
    'topic-sub-comments', // tabIndex 5
  ];

  // Determine searchType using the tabIndex
  const searchType = searchTypes[tabIndex] || ''; // Fallback to an empty string if tabIndex is out of range

  const mutation = useMutation({
    mutationFn: (search: string) => topic.search({ search, searchType }), // Mutation function
    onSuccess: (data) => {
        const {
          setUsers,
          setChats,
          setGroups,
          setData,
          setComments,
          setSubComments,
          setTotalResults,
        } = setDataFunctions;


        if (!searchInput.trim()) {
          // If the search input is empty, you can clear the relevant state
          switch (searchType) {
            case 'users':
              setUsers([]);
              setTotalResults(0);
              break;
            case 'chats':
              setChats([]);
              setTotalResults(0);
              break;
            case 'groups':
              setGroups([]);
              setTotalResults(0);
              break;
            case 'topics':
              setData([]);
              setTotalResults(0);
              break;
            case 'topic-comments':
              setComments([]);
              setTotalResults(0);
              break;
            case 'topic-sub-comments':
              setSubComments([]);
              setTotalResults(0);
              break;
            default:
              break;
          }
          return;
        }
        // Handle successful response
        switch (searchType) {
          case 'users':
            setUsers(data.users.docs);
            setTotalResults(data.users.totalDocs);
            break;
          case 'chats':
            setChats(data.chats);
            setTotalResults(data.chats.length);
            break;
          case 'groups':
            setGroups(data.groups.docs);
            setTotalResults(data.groups.totalDocs);
            break;
          case 'topics':
            setData(data.topics.docs);
            setTotalResults(data.topics.totalDocs);
            break;
          case 'topic-comments':
            setComments(data.topicComments.docs);
            setTotalResults(data.topicComments.totalDocs);
            break;
          case 'topic-sub-comments':
            setSubComments(data.topicComments.docs);
            setTotalResults(data.topicComments.totalDocs);
            break;
          default:
            break;
        }
      },
      onError: (error) => {
        console.error('Error fetching topics:', error);
      }
    }
  );

  return mutation;
}

// Add this hook for creating topics
export function useCreateTopic() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (topicData: FormData) => {
      return topic.createTopic(topicData);
    },
    onSuccess: (data) => {
      // Invalidate and refetch topics after successful creation
      queryClient.invalidateQueries({ queryKey: ['topic'] });
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['topics-all'] });
      return data;
    }
  });
}


import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { search, SearchParams, SearchResults } from '@/api/search';

// Query keys
export const searchKeys = {
  all: ['search'] as const,
  search: (params: SearchParams) => [...searchKeys.all, 'query', params] as const,
  users: (query: string, page?: number) => [...searchKeys.all, 'users', query, page] as const,
  chats: (query: string, page?: number) => [...searchKeys.all, 'chats', query, page] as const,
  groups: (query: string, page?: number) => [...searchKeys.all, 'groups', query, page] as const,
  topics: (query: string, page?: number) => [...searchKeys.all, 'topics', query, page] as const,
  comments: (query: string, page?: number) => [...searchKeys.all, 'comments', query, page] as const,
  all_types: (query: string, page?: number) => [...searchKeys.all, 'all_types', query, page] as const,
  chat_messages: (chatId: string, query: string) => [...searchKeys.all, 'chat_messages', chatId, query] as const,
};

// Main search hook
export const useSearch = (params: SearchParams, options?: {
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.search(params),
    queryFn: () => search.performSearch(params),
    enabled: options?.enabled !== false && params.search.trim().length > 0,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    cacheTime: options?.cacheTime || 10 * 60 * 1000, // 10 minutes
  });
};

// Specific search hooks
export const useSearchUsers = (query: string, page = 1, limit = 50, options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.users(query, page),
    queryFn: () => search.searchUsers(query, page, limit),
    enabled: options?.enabled !== false && query.trim().length > 0,
    staleTime: options?.staleTime || 5 * 60 * 1000,
  });
};

export const useSearchChats = (query: string, page = 1, limit = 50, options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.chats(query, page),
    queryFn: () => search.searchChats(query, page, limit),
    enabled: options?.enabled !== false && query.trim().length > 0,
    staleTime: options?.staleTime || 5 * 60 * 1000,
  });
};

export const useSearchGroups = (query: string, page = 1, limit = 50, options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.groups(query, page),
    queryFn: () => search.searchGroups(query, page, limit),
    enabled: options?.enabled !== false && query.trim().length > 0,
    staleTime: options?.staleTime || 5 * 60 * 1000,
  });
};

export const useSearchTopics = (query: string, page = 1, limit = 50, options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.topics(query, page),
    queryFn: () => search.searchTopics(query, page, limit),
    enabled: options?.enabled !== false && query.trim().length > 0,
    staleTime: options?.staleTime || 5 * 60 * 1000,
  });
};

export const useSearchComments = (query: string, page = 1, limit = 50, options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.comments(query, page),
    queryFn: () => search.searchComments(query, page, limit),
    enabled: options?.enabled !== false && query.trim().length > 0,
    staleTime: options?.staleTime || 5 * 60 * 1000,
  });
};

// Combined search hook for all types
export const useSearchAll = (query: string, page = 1, limit = 10, options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.all_types(query, page),
    queryFn: () => search.searchAll(query, page, limit),
    enabled: options?.enabled !== false && query.trim().length > 0,
    staleTime: options?.staleTime || 5 * 60 * 1000,
  });
};

// Chat messages search hook
export const useSearchChatMessages = (chatId: string, query: string, options?: {
  enabled?: boolean;
  staleTime?: number;
}) => {
  return useQuery({
    queryKey: searchKeys.chat_messages(chatId, query),
    queryFn: () => search.searchChatMessages(chatId, query),
    enabled: options?.enabled !== false && query.trim().length > 0 && chatId.length > 0,
    staleTime: options?.staleTime || 2 * 60 * 1000, // 2 minutes for chat messages
  });
};

// Search mutation for immediate results
export const useSearchMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: SearchParams) => search.performSearch(params),
    onSuccess: (data, variables) => {
      // Cache the result
      queryClient.setQueryData(searchKeys.search(variables), data);
    },
  });
};

// Hook for managing search state and debouncing
export const useSearchState = (initialQuery = '', debounceMs = 300) => {
  const [query, setQuery] = React.useState(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = React.useState(initialQuery);
  const [searchType, setSearchType] = React.useState<SearchParams['searchType']>('users');
  const [isSearching, setIsSearching] = React.useState(false);

  // Debounce the search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
      setIsSearching(false);
    }, debounceMs);

    if (query !== debouncedQuery) {
      setIsSearching(true);
    }

    return () => clearTimeout(timer);
  }, [query, debounceMs, debouncedQuery]);

  const clearSearch = React.useCallback(() => {
    setQuery('');
    setDebouncedQuery('');
    setIsSearching(false);
  }, []);

  return {
    query,
    debouncedQuery,
    searchType,
    isSearching,
    setQuery,
    setSearchType,
    clearSearch,
  };
};

// Hook for search suggestions/autocomplete
export const useSearchSuggestions = (query: string, limit = 5) => {
  return useQuery({
    queryKey: [...searchKeys.all, 'suggestions', query, limit],
    queryFn: async () => {
      if (query.trim().length < 2) return [];
      
      // Get quick results from all types with small limit
      const results = await search.searchAll(query, 1, limit);
      
      // Combine and format suggestions
      const suggestions = [
        ...results.users.map(user => ({
          type: 'user' as const,
          id: user._id,
          title: `${user.first_name} ${user.last_name}`,
          subtitle: user.company_position && user.company ? `${user.company_position} at ${user.company}` : undefined,
          avatar: user.profile_picture,
        })),
        ...results.topics.map(topic => ({
          type: 'topic' as const,
          id: topic._id,
          title: topic.title,
          subtitle: `by ${topic.author.first_name} ${topic.author.last_name}`,
          avatar: topic.author.profile_picture,
        })),
        ...results.groups.map(group => ({
          type: 'group' as const,
          id: group._id,
          title: group.name,
          subtitle: group.description,
          avatar: group.profile_picture,
        })),
      ].slice(0, limit);

      return suggestions;
    },
    enabled: query.trim().length >= 2,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Import React for hooks
import React from 'react';

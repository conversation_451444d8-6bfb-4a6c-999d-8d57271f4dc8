import { UserModel } from '@/redux/user/types';
import { chat as chatAPI } from '@/api/chat';

/**
 * Utility functions for chat initiation and management
 */

export interface ChatUser {
  _id: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
  firstName?: string;
  lastName?: string;
  profilePicture?: string;
}

export interface ChatData {
  _id: string;
  accountId: ChatUser;
  messages?: any[];
  lastMessage?: {
    message: string;
    createdAt: string;
  };
  unreadCount?: number;
  muted?: boolean;
  blocked?: boolean;
  isNewChat?: boolean;
  chatDetails?: any;
}

export interface ProfileClickOptions {
  openInNewTab?: boolean;
  showChatOption?: boolean;
  showProfileOption?: boolean;
  onChatStart?: (chatData: ChatData) => void;
  onProfileView?: (userId: string) => void;
}

/**
 * Navigate to private chat with a user
 * This function handles the navigation logic and creates/gets chat if needed
 */
export const navigateToPrivateChat = async (
  router: any,
  userDetails: ChatUser | UserModel,
  currentUserId?: string,
  options?: ProfileClickOptions
) => {
  console.log('navigateToPrivateChat: Starting with user:', userDetails);
  console.log('navigateToPrivateChat: Current user ID:', currentUserId);

  // Don't allow chatting with self
  if (userDetails._id === currentUserId) {
    console.warn('Cannot chat with yourself');
    return;
  }

  // Always navigate first to provide immediate feedback
  console.log('navigateToPrivateChat: Navigating to:', `/messages/${userDetails._id}`);
  router.push(`/messages/${userDetails._id}`);

  try {
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Chat creation timeout')), 10000); // 10 second timeout
    });

    // Check if chat exists or create new one with timeout
    const chatDataPromise = chatAPI.getOrCreateUserChat(userDetails._id);
    const chatData = await Promise.race([chatDataPromise, timeoutPromise]);

    console.log('navigateToPrivateChat: Chat data received:', chatData);

    // Call the onChatStart callback if provided
    if (options?.onChatStart && chatData) {
      options.onChatStart(chatData);
    }

    return chatData;
  } catch (error) {
    console.error('navigateToPrivateChat: Error creating/getting chat:', error);

    // Create a fallback chat structure so the UI doesn't break
    const fallbackChat = createNewChatData(formatUserForChat(userDetails));

    // Call the onChatStart callback with fallback data
    if (options?.onChatStart) {
      options.onChatStart(fallbackChat);
    }

    // Navigation already happened, so the chat page will handle loading
    return fallbackChat;
  }
};

/**
 * Format user data for chat display
 * Handles different user data formats from various parts of the app
 */
export const formatUserForChat = (user: any): ChatUser => {
  return {
    _id: user._id,
    first_name: user.first_name || user.firstName || '',
    last_name: user.last_name || user.lastName || '',
    profile_picture: user.profile_picture || user.profilePicture || '',
    firstName: user.first_name || user.firstName || '',
    lastName: user.last_name || user.lastName || '',
    profilePicture: user.profile_picture || user.profilePicture || '',
  };
};

/**
 * Create a new chat data structure for a user
 * Used when no existing chat is found
 */
export const createNewChatData = (userDetails: ChatUser): ChatData => {
  const formattedUser = formatUserForChat(userDetails);
  
  return {
    _id: `temp-${userDetails._id}-${Date.now()}`,
    accountId: formattedUser,
    messages: [],
    isNewChat: true,
    unreadCount: 0,
    muted: false,
    blocked: false,
  };
};

/**
 * Format existing chat data from API response
 * Ensures consistent structure across the app
 */
export const formatChatData = (
  chatResponse: any,
  userDetails: ChatUser
): ChatData => {
  const formattedUser = formatUserForChat(userDetails);
  
  return {
    _id: chatResponse.chats?.chatDetails?._id || `temp-${userDetails._id}-${Date.now()}`,
    accountId: formattedUser,
    messages: chatResponse.chats?.docs || [],
    chatDetails: chatResponse.chats?.chatDetails,
    isNewChat: !chatResponse.chats?.docs?.length,
    unreadCount: 0,
    muted: false,
    blocked: false,
  };
};

/**
 * Get display name for a user
 */
export const getUserDisplayName = (user: ChatUser): string => {
  const firstName = user.first_name || user.firstName || '';
  const lastName = user.last_name || user.lastName || '';
  return `${firstName} ${lastName}`.trim() || 'Unknown User';
};

/**
 * Safely get user initials, handling undefined/null values
 */
export const getSafeUserInitials = (user: any): string => {
  const firstName = user?.first_name || user?.firstName || '';
  const lastName = user?.last_name || user?.lastName || '';

  const firstInitial = firstName.charAt(0).toUpperCase();
  const lastInitial = lastName.charAt(0).toUpperCase();

  return `${firstInitial}${lastInitial}` || 'U';
};

/**
 * Get user initials for avatar fallback
 */
export const getUserInitials = (user: ChatUser): string => {
  const firstName = user.first_name || user.firstName || '';
  const lastName = user.last_name || user.lastName || '';
  
  const firstInitial = firstName.charAt(0).toUpperCase();
  const lastInitial = lastName.charAt(0).toUpperCase();
  
  return `${firstInitial}${lastInitial}` || 'U';
};

/**
 * Check if a user can be messaged
 * Add any business logic for blocking, permissions, etc.
 */
export const canMessageUser = (
  targetUser: ChatUser,
  currentUserId?: string
): { canMessage: boolean; reason?: string } => {
  if (!targetUser._id) {
    return { canMessage: false, reason: 'Invalid user' };
  }
  
  if (targetUser._id === currentUserId) {
    return { canMessage: false, reason: 'Cannot message yourself' };
  }
  
  // Add more validation logic here as needed
  // e.g., check if user is blocked, has permissions, etc.
  
  return { canMessage: true };
};

/**
 * Extract user details from topic/post data
 * Handles the userId field structure from topics
 */
export const extractUserFromTopic = (topicItem: any): ChatUser | null => {
  console.log('Extracting user from topic:', topicItem);

  if (!topicItem?.userId) {
    console.log('No userId found in topic item');
    return null;
  }

  const user = formatUserForChat(topicItem.userId);
  console.log('Extracted user:', user);
  return user;
};

/**
 * Extract user details from search results
 * Handles different user data structures from search
 */
export const extractUserFromSearch = (searchItem: any): ChatUser | null => {
  if (!searchItem) {
    return null;
  }
  
  // Handle direct user search results
  if (searchItem.first_name || searchItem.firstName) {
    return formatUserForChat(searchItem);
  }
  
  // Handle nested user data (e.g., from comments, posts)
  if (searchItem.user) {
    return formatUserForChat(searchItem.user);
  }
  
  return null;
};

/**
 * Create chat initiation handler for components
 * Returns a function that can be used in onClick handlers
 */
export const createChatHandler = (
  router: any,
  currentUserId?: string,
  options?: ProfileClickOptions
) => {
  return async (userDetails: ChatUser | UserModel) => {
    const validation = canMessageUser(userDetails, currentUserId);

    if (!validation.canMessage) {
      console.warn(`Cannot message user: ${validation.reason}`);
      return;
    }

    return await navigateToPrivateChat(router, userDetails, currentUserId, options);
  };
};

/**
 * Handle profile name click with multiple options
 * Can show chat, profile, or both options
 */
export const handleProfileClick = async (
  userDetails: ChatUser | UserModel,
  router: any,
  currentUserId?: string,
  options: ProfileClickOptions = {}
) => {
  const {
    showChatOption = true,
    showProfileOption = false,
    openInNewTab = false,
    onChatStart,
    onProfileView
  } = options;

  // If only chat option is enabled, go directly to chat
  if (showChatOption && !showProfileOption) {
    return await navigateToPrivateChat(router, userDetails, currentUserId, { onChatStart });
  }

  // If only profile option is enabled, go to profile
  if (showProfileOption && !showChatOption) {
    const profileUrl = `/profile/${userDetails._id}`;
    if (openInNewTab) {
      window.open(profileUrl, '_blank');
    } else {
      router.push(profileUrl);
    }
    if (onProfileView) {
      onProfileView(userDetails._id);
    }
    return;
  }

  // If both options are enabled, you might want to show a menu
  // For now, default to chat
  return await navigateToPrivateChat(router, userDetails, currentUserId, { onChatStart });
};

/**
 * Create a profile click handler that opens chat by default
 */
export const createProfileChatHandler = (
  router: any,
  currentUserId?: string,
  options?: ProfileClickOptions
) => {
  return (userDetails: ChatUser | UserModel) => {
    return handleProfileClick(userDetails, router, currentUserId, {
      showChatOption: true,
      showProfileOption: false,
      ...options
    });
  };
};

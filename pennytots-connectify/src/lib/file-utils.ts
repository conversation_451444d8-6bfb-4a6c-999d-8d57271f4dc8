// File utility functions for handling file types and extensions

export interface FileTypeInfo {
  extension: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'other';
  category: string;
  color: string;
  bgColor: string;
}

// Get file extension from filename
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

// Get file type information based on extension
export function getFileTypeInfo(filename: string): FileTypeInfo {
  const extension = getFileExtension(filename);
  
  // Image files
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'].includes(extension)) {
    return {
      extension: extension.toUpperCase(),
      type: 'image',
      category: 'Image',
      color: '#10B981', // green
      bgColor: '#ECFDF5'
    };
  }
  
  // Video files
  if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'].includes(extension)) {
    return {
      extension: extension.toUpperCase(),
      type: 'video',
      category: 'Video',
      color: '#3B82F6', // blue
      bgColor: '#EFF6FF'
    };
  }
  
  // Audio files
  if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'].includes(extension)) {
    return {
      extension: extension.toUpperCase(),
      type: 'audio',
      category: 'Audio',
      color: '#8B5CF6', // purple
      bgColor: '#F3E8FF'
    };
  }
  
  // Document files
  if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
    return {
      extension: extension.toUpperCase(),
      type: 'document',
      category: 'Document',
      color: '#EF4444', // red
      bgColor: '#FEF2F2'
    };
  }
  
  // Spreadsheet files
  if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
    return {
      extension: extension.toUpperCase(),
      type: 'document',
      category: 'Spreadsheet',
      color: '#059669', // emerald
      bgColor: '#ECFDF5'
    };
  }
  
  // Presentation files
  if (['ppt', 'pptx', 'odp'].includes(extension)) {
    return {
      extension: extension.toUpperCase(),
      type: 'document',
      category: 'Presentation',
      color: '#DC2626', // red
      bgColor: '#FEF2F2'
    };
  }
  
  // Archive files
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return {
      extension: extension.toUpperCase(),
      type: 'other',
      category: 'Archive',
      color: '#6B7280', // gray
      bgColor: '#F9FAFB'
    };
  }
  
  // Default for unknown files
  return {
    extension: extension.toUpperCase() || 'FILE',
    type: 'other',
    category: 'File',
    color: '#6B7280', // gray
    bgColor: '#F9FAFB'
  };
}

// Format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Check if file type is supported
export function isSupportedFileType(filename: string): boolean {
  const extension = getFileExtension(filename);
  const supportedExtensions = [
    // Images
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg',
    // Videos
    'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv',
    // Audio
    'mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a',
    // Documents
    'pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx'
  ];
  
  return supportedExtensions.includes(extension);
}

// Get MIME type from file extension
export function getMimeType(filename: string): string {
  const extension = getFileExtension(filename);
  
  const mimeTypes: { [key: string]: string } = {
    // Images
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    
    // Videos
    'mp4': 'video/mp4',
    'avi': 'video/x-msvideo',
    'mov': 'video/quicktime',
    'wmv': 'video/x-ms-wmv',
    'flv': 'video/x-flv',
    'webm': 'video/webm',
    'mkv': 'video/x-matroska',
    
    // Audio
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav',
    'flac': 'audio/flac',
    'aac': 'audio/aac',
    'ogg': 'audio/ogg',
    'm4a': 'audio/mp4',
    
    // Documents
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'txt': 'text/plain',
    'rtf': 'application/rtf',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  };
  
  return mimeTypes[extension] || 'application/octet-stream';
}

// Responsive utilities for Next.js web application
// Replaces React Native responsive-value and responsive-screen utilities

// Breakpoint definitions matching Tailwind config
const breakpoints = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

type BreakpointKey = keyof typeof breakpoints;

/**
 * Convert px value to rem units
 * @param px - pixel value
 * @param baseFontSize - base font size in pixels (default: 16)
 * @returns rem value as string
 */
export function pxToRem(px: number, baseFontSize: number = 16): string {
  return `${px / baseFontSize}rem`;
}

/**
 * Convert px value to em units
 * @param px - pixel value
 * @param baseFontSize - base font size in pixels (default: 16)
 * @returns em value as string
 */
export function pxToEm(px: number, baseFontSize: number = 16): string {
  return `${px / baseFontSize}em`;
}

/**
 * Responsive value function that scales based on screen size
 * @param value - base value in pixels
 * @param unit - output unit ('rem', 'em', 'px')
 * @returns scaled value with unit
 */
export function responsiveValue(
  value: number,
  unit: 'rem' | 'em' | 'px' = 'rem'
): string {
  // Base scaling factor for different screen sizes
  const getScaleFactor = (): number => {
    if (typeof window === 'undefined') return 1;

    const width = window.innerWidth;

    if (width < breakpoints.sm) {
      return 0.875; // 87.5% for mobile
    } else if (width < breakpoints.md) {
      return 0.9375; // 93.75% for small tablets
    } else if (width < breakpoints.lg) {
      return 1; // 100% for tablets
    } else {
      return 1; // 100% for desktop and larger
    }
  };

  const scaledValue = value * getScaleFactor();

  switch (unit) {
    case 'rem':
      return pxToRem(scaledValue);
    case 'em':
      return pxToEm(scaledValue);
    case 'px':
      return `${scaledValue}px`;
    default:
      return pxToRem(scaledValue);
  }
}

/**
 * Convert percentage to viewport width units
 * @param percentage - percentage as string (e.g., "50%")
 * @returns vw value as string
 */
export function widthPercentageToDP(percentage: string): string {
  const num = parseFloat(percentage.replace('%', ''));
  return `${num}vw`;
}

/**
 * Convert percentage to viewport height units
 * @param percentage - percentage as string (e.g., "50%")
 * @returns vh value as string
 */
export function heightPercentageToDP(percentage: string): string {
  const num = parseFloat(percentage.replace('%', ''));
  return `${num}vh`;
}

/**
 * Get responsive spacing value based on screen size
 * @param base - base spacing value in pixels
 * @returns CSS custom property or rem value
 */
export function responsiveSpacing(base: number): string {
  // Use CSS custom properties for consistent spacing
  const spacingMap: Record<number, string> = {
    4: 'var(--spacing-xs)',
    8: 'var(--spacing-sm)',
    14: 'var(--spacing-md)',
    16: 'var(--spacing-lg)',
    24: 'var(--spacing-xl)',
    32: 'var(--spacing-2xl)',
  };

  return spacingMap[base] || pxToRem(base);
}

/**
 * Get responsive font size
 * @param base - base font size in pixels
 * @returns CSS custom property or rem value
 */
export function responsiveFontSize(base: number): string {
  const fontSizeMap: Record<number, string> = {
    12: 'var(--text-xs)',
    14: 'var(--text-sm)',
    16: 'var(--text-base)',
    18: 'var(--text-lg)',
    20: 'var(--text-xl)',
  };

  return fontSizeMap[base] || pxToRem(base);
}

/**
 * Check if current screen size matches breakpoint
 * @param breakpoint - breakpoint key
 * @returns boolean
 */
export function isBreakpoint(breakpoint: BreakpointKey): boolean {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= breakpoints[breakpoint];
}

/**
 * Get current breakpoint
 * @returns current breakpoint key
 */
export function getCurrentBreakpoint(): BreakpointKey {
  if (typeof window === 'undefined') return 'lg';

  const width = window.innerWidth;

  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
}

// Aliases for compatibility with existing code
export const rv = responsiveValue;
export const wp = widthPercentageToDP;
export const hp = heightPercentageToDP;
export const rs = responsiveSpacing;
export const rf = responsiveFontSize;

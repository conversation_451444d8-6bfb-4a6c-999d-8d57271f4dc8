export interface ValidationError {
  field: string;
  message: string;
}

export interface ProfileFormData {
  first_name?: string;
  last_name?: string;
  bio?: string;
  website?: string;
  facebook?: string;
  linkedin?: string;
  company?: string;
  company_position?: string;
  city?: string;
  state?: string;
  age?: string | number;
  gender?: string;
  email?: string;
  phone_number?: string;
}

export class ProfileValidator {
  private errors: ValidationError[] = [];

  // URL validation
  private isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Email validation
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Phone number validation (basic)
  private isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  }

  // Name validation
  private isValidName(name: string): boolean {
    return name.trim().length >= 2 && name.trim().length <= 50 && /^[a-zA-Z\s'-]+$/.test(name.trim());
  }

  // Bio validation
  private isValidBio(bio: string): boolean {
    return bio.trim().length <= 500;
  }

  // Age validation
  private isValidAge(age: string | number): boolean {
    const ageNum = typeof age === 'string' ? parseInt(age) : age;
    return !isNaN(ageNum) && ageNum >= 13 && ageNum <= 120;
  }

  // Social media URL validation
  private isValidSocialURL(url: string, platform: 'facebook' | 'linkedin' | 'twitter' | 'instagram'): boolean {
    if (!this.isValidURL(url)) return false;
    
    const platformPatterns = {
      facebook: /^https?:\/\/(www\.)?(facebook|fb)\.com\/.+/i,
      linkedin: /^https?:\/\/(www\.)?linkedin\.com\/(in|company)\/.+/i,
      twitter: /^https?:\/\/(www\.)?(twitter|x)\.com\/.+/i,
      instagram: /^https?:\/\/(www\.)?instagram\.com\/.+/i
    };

    return platformPatterns[platform].test(url);
  }

  // Company name validation
  private isValidCompanyName(name: string): boolean {
    return name.trim().length >= 2 && name.trim().length <= 100;
  }

  // Position validation
  private isValidPosition(position: string): boolean {
    return position.trim().length >= 2 && position.trim().length <= 100;
  }

  // Location validation
  private isValidLocation(location: string): boolean {
    return location.trim().length >= 2 && location.trim().length <= 50 && /^[a-zA-Z\s\-'.,]+$/.test(location.trim());
  }

  // Main validation method
  validate(data: ProfileFormData): ValidationError[] {
    this.errors = [];

    // Required fields validation
    if (data.first_name !== undefined) {
      if (!data.first_name || !data.first_name.trim()) {
        this.errors.push({ field: 'first_name', message: 'First name is required' });
      } else if (!this.isValidName(data.first_name)) {
        this.errors.push({ field: 'first_name', message: 'First name must be 2-50 characters and contain only letters, spaces, hyphens, and apostrophes' });
      }
    }

    if (data.last_name !== undefined) {
      if (!data.last_name || !data.last_name.trim()) {
        this.errors.push({ field: 'last_name', message: 'Last name is required' });
      } else if (!this.isValidName(data.last_name)) {
        this.errors.push({ field: 'last_name', message: 'Last name must be 2-50 characters and contain only letters, spaces, hyphens, and apostrophes' });
      }
    }

    // Optional fields validation
    if (data.bio && !this.isValidBio(data.bio)) {
      this.errors.push({ field: 'bio', message: 'Bio must be 500 characters or less' });
    }

    if (data.email && !this.isValidEmail(data.email)) {
      this.errors.push({ field: 'email', message: 'Please enter a valid email address' });
    }

    if (data.phone_number && !this.isValidPhoneNumber(data.phone_number)) {
      this.errors.push({ field: 'phone_number', message: 'Please enter a valid phone number' });
    }

    if (data.website && !this.isValidURL(data.website)) {
      this.errors.push({ field: 'website', message: 'Please enter a valid website URL' });
    }

    if (data.facebook && !this.isValidSocialURL(data.facebook, 'facebook')) {
      this.errors.push({ field: 'facebook', message: 'Please enter a valid Facebook profile URL' });
    }

    if (data.linkedin && !this.isValidSocialURL(data.linkedin, 'linkedin')) {
      this.errors.push({ field: 'linkedin', message: 'Please enter a valid LinkedIn profile URL' });
    }

    if (data.age !== undefined && data.age !== '' && !this.isValidAge(data.age)) {
      this.errors.push({ field: 'age', message: 'Age must be between 13 and 120' });
    }

    if (data.company && !this.isValidCompanyName(data.company)) {
      this.errors.push({ field: 'company', message: 'Company name must be 2-100 characters' });
    }

    if (data.company_position && !this.isValidPosition(data.company_position)) {
      this.errors.push({ field: 'company_position', message: 'Position must be 2-100 characters' });
    }

    if (data.city && !this.isValidLocation(data.city)) {
      this.errors.push({ field: 'city', message: 'City name must be 2-50 characters and contain only letters, spaces, and common punctuation' });
    }

    if (data.state && !this.isValidLocation(data.state)) {
      this.errors.push({ field: 'state', message: 'State name must be 2-50 characters and contain only letters, spaces, and common punctuation' });
    }

    if (data.gender && !['male', 'female', 'other', 'prefer_not_to_say'].includes(data.gender)) {
      this.errors.push({ field: 'gender', message: 'Please select a valid gender option' });
    }

    return this.errors;
  }

  // Get errors for a specific field
  getFieldErrors(field: string): string[] {
    return this.errors.filter(error => error.field === field).map(error => error.message);
  }

  // Check if form is valid
  isValid(): boolean {
    return this.errors.length === 0;
  }

  // Get all error messages
  getAllErrors(): string[] {
    return this.errors.map(error => error.message);
  }

  // Format errors for display
  getFormattedErrors(): Record<string, string> {
    const formatted: Record<string, string> = {};
    this.errors.forEach(error => {
      if (!formatted[error.field]) {
        formatted[error.field] = error.message;
      }
    });
    return formatted;
  }
}

// Image validation
export interface ImageValidationOptions {
  maxSizeInMB?: number;
  allowedFormats?: string[];
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export class ImageValidator {
  private defaultOptions: Required<ImageValidationOptions> = {
    maxSizeInMB: 5,
    allowedFormats: ['image/jpeg', 'image/png', 'image/webp'],
    minWidth: 100,
    minHeight: 100,
    maxWidth: 2000,
    maxHeight: 2000
  };

  async validateImage(file: File, options: ImageValidationOptions = {}): Promise<string | null> {
    const opts = { ...this.defaultOptions, ...options };

    // Check file type
    if (!opts.allowedFormats.includes(file.type)) {
      return `Please select a valid image file (${opts.allowedFormats.map(f => f.split('/')[1]).join(', ')})`;
    }

    // Check file size
    const maxSizeInBytes = opts.maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      return `File size must be less than ${opts.maxSizeInMB}MB`;
    }

    // Check image dimensions
    return new Promise<string | null>((resolve) => {
      const img = new Image();
      img.onload = () => {
        if (img.width < opts.minWidth || img.height < opts.minHeight) {
          resolve(`Image must be at least ${opts.minWidth}x${opts.minHeight} pixels`);
        } else if (img.width > opts.maxWidth || img.height > opts.maxHeight) {
          resolve(`Image must be less than ${opts.maxWidth}x${opts.maxHeight} pixels`);
        } else {
          resolve(null);
        }
        URL.revokeObjectURL(img.src);
      };
      img.onerror = () => {
        resolve('Invalid image file');
        URL.revokeObjectURL(img.src);
      };
      img.src = URL.createObjectURL(file);
    });
  }
}

// Utility functions
export const validateProfileForm = (data: ProfileFormData): ValidationError[] => {
  const validator = new ProfileValidator();
  return validator.validate(data);
};

export const validateImage = async (file: File, options?: ImageValidationOptions): Promise<string | null> => {
  const validator = new ImageValidator();
  return validator.validateImage(file, options);
};

'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useChat, ChatState, UseChatOptions } from '@/hooks/useChat';
import { ChatUser, ChatData } from '@/lib/chat-utils';
import { useProfileNotifications } from '@/components/profile/ProfileNotifications';
import toastService from '@/services/toast';

export interface ChatContextType extends ReturnType<typeof useChat> {
  // Additional context-specific methods
  initializeToasts: () => void;
  globalChatState: ChatState;
}

interface ChatProviderProps {
  children: ReactNode;
  options?: UseChatOptions;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const ChatProvider: React.FC<ChatProviderProps> = ({ 
  children, 
  options = {} 
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Initialize the chat hook with default options
  const defaultOptions: UseChatOptions = {
    autoConnect: true,
    enableTypingIndicators: true,
    enableNotifications: true,
    enableToasts: true,
    ...options
  };

  const chatHook = useChat(defaultOptions);
  
  // Initialize toast notifications
  const { addNotification } = useProfileNotifications();

  const initializeToasts = () => {
    if (!isInitialized) {
      toastService.setToastHandler(addNotification);
      setIsInitialized(true);
    }
  };

  // Auto-initialize toasts when provider mounts
  useEffect(() => {
    initializeToasts();
  }, []);

  // Create the context value
  const contextValue: ChatContextType = {
    ...chatHook,
    initializeToasts,
    globalChatState: {
      isConnected: chatHook.isConnected,
      isLoading: chatHook.isLoading,
      error: chatHook.error,
      activeChat: chatHook.activeChat,
      messages: chatHook.messages,
      typingUsers: chatHook.typingUsers,
      uploadProgress: chatHook.uploadProgress,
      isUploading: chatHook.isUploading,
    }
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// Hook to use the chat context
export const useChatContext = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

// Convenience hooks for specific chat operations
export const useChatConnection = () => {
  const { isConnected, connectSocket, disconnectSocket, error } = useChatContext();
  return { isConnected, connectSocket, disconnectSocket, error };
};

export const useChatMessages = () => {
  const { 
    messages, 
    sendMessage, 
    sendMessageWithAttachment, 
    activeChat,
    isUploading,
    uploadProgress 
  } = useChatContext();
  
  return { 
    messages, 
    sendMessage, 
    sendMessageWithAttachment, 
    activeChat,
    isUploading,
    uploadProgress 
  };
};

export const useChatNavigation = () => {
  const { createChat, navigateToChat, setActiveChat } = useChatContext();
  return { createChat, navigateToChat, setActiveChat };
};

export const useChatTyping = () => {
  const { startTyping, stopTyping, typingUsers } = useChatContext();
  return { startTyping, stopTyping, typingUsers };
};

export const useChatActions = () => {
  const { 
    blockUser, 
    unBlockUser, 
    muteNotification,
    chatList,
    refreshMyChat 
  } = useChatContext();
  
  return { 
    blockUser, 
    unBlockUser, 
    muteNotification,
    chatList,
    refreshMyChat 
  };
};

// Higher-order component for easy chat integration
export const withChat = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P & { chatOptions?: UseChatOptions }> => {
  return ({ chatOptions, ...props }) => (
    <ChatProvider options={chatOptions}>
      <Component {...(props as P)} />
    </ChatProvider>
  );
};

// Hook for quick chat initiation from any component
export const useQuickChat = () => {
  const { navigateToChat, createChat, isLoading, error } = useChatContext();

  const startChatWithUser = async (user: ChatUser) => {
    try {
      await navigateToChat(user);
      return true;
    } catch (error) {
      console.error('Failed to start chat:', error);
      return false;
    }
  };

  const createChatWithUser = async (user: ChatUser) => {
    try {
      const chatData = await createChat(user);
      return chatData;
    } catch (error) {
      console.error('Failed to create chat:', error);
      return null;
    }
  };

  return {
    startChatWithUser,
    createChatWithUser,
    isLoading,
    error
  };
};

// Hook for chat status and indicators
export const useChatStatus = () => {
  const { 
    isConnected, 
    isLoading, 
    error, 
    typingUsers, 
    isUploading,
    uploadProgress 
  } = useChatContext();

  const getConnectionStatus = () => {
    if (isLoading) return 'connecting';
    if (error) return 'error';
    if (isConnected) return 'connected';
    return 'disconnected';
  };

  const hasTypingUsers = typingUsers.length > 0;
  const isProcessing = isLoading || isUploading;

  return {
    connectionStatus: getConnectionStatus(),
    isConnected,
    isLoading,
    error,
    typingUsers,
    hasTypingUsers,
    isUploading,
    uploadProgress,
    isProcessing
  };
};

// Hook for chat list management
export const useChatList = () => {
  const { 
    chatList, 
    isLoading, 
    isFetching, 
    refreshMyChat,
    searchChats 
  } = useChatContext();

  const [searchQuery, setSearchQuery] = useState('');
  const [filteredChats, setFilteredChats] = useState(chatList || []);

  useEffect(() => {
    if (chatList) {
      if (searchQuery.trim()) {
        const filtered = chatList.filter((chat: any) =>
          `${chat.accountId.firstName || chat.accountId.first_name} ${chat.accountId.lastName || chat.accountId.last_name}`
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
        );
        setFilteredChats(filtered);
      } else {
        setFilteredChats(chatList);
      }
    }
  }, [chatList, searchQuery]);

  return {
    chats: filteredChats,
    allChats: chatList,
    isLoading,
    isFetching,
    searchQuery,
    setSearchQuery,
    refreshChats: refreshMyChat,
    searchChats
  };
};

export default ChatProvider;

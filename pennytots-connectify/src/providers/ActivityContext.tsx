'use client';

import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { useGetSponsoredAd } from '@/redux/sponsor/hooks';
import { ActivityCounts, ModalVisible, ActivityContextType, SponsoredAd } from '@/redux/sponsor/types';
import { useRouter } from 'next/navigation';

// Create the context
const ActivityContext = createContext<ActivityContextType | undefined>(undefined);

// Key for storing activity counts in localStorage
const STORAGE_KEY = 'activity_counts';

// Provider component
export const ActivityProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [activityCounts, setActivityCounts] = useState<ActivityCounts>({ sponsor: 0, share: 0 });
  const [modalVisible, setModalVisible] = useState<ModalVisible>({ sponsor: false, share: false });
  const [nextModal, setNextModal] = useState<'share' | 'sponsor'>('share');
  const { adData, isLoading, refetch } = useGetSponsoredAd();
  const [sponsoredAd, setSponsoredAd] = useState<SponsoredAd | null>(null);
  const router = useRouter();

  // Load activity counts from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedCounts = localStorage.getItem(STORAGE_KEY);
      if (storedCounts) {
        try {
          const parsedCounts = JSON.parse(storedCounts);
          setActivityCounts(parsedCounts);
        } catch (error) {
          console.error('Error parsing stored activity counts:', error);
        }
      }
    }
  }, []);

  // Save activity counts to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(activityCounts));
    }
  }, [activityCounts]);

  // Update sponsored ad when data is received
  useEffect(() => {
    if (adData) {
      setSponsoredAd(adData);
    }
  }, [adData]);

  // Function to increment activity count
  const incrementActivity = (type: keyof ActivityCounts) => {
    setActivityCounts((prev) => ({
      ...prev,
      [type]: prev[type] + 1,
    }));
  };

  // Function to reset activity count
  const resetActivityCount = (type: keyof ActivityCounts) => {
    setActivityCounts((prev) => ({
      ...prev,
      [type]: 0,
    }));
  };

  // Check if modals should be shown based on activity counts
  useEffect(() => {
    console.log('🔍 Activity check:', {
      nextModal,
      activityCounts,
      shareReady: nextModal === 'share' && activityCounts.share >= 5,
      sponsorReady: nextModal === 'sponsor' && activityCounts.sponsor >= 7
    });

    if (nextModal === 'share' && activityCounts.share >= 5) {
      console.log('🎯 Share modal triggered!');
      // Show share modal (you can implement this based on your needs)
      setModalVisible((prev) => ({ ...prev, share: true }));

      // Reset counts and set next modal
      resetActivityCount('share');
      resetActivityCount('sponsor');
      setNextModal('sponsor');
    } else if (nextModal === 'sponsor' && activityCounts.sponsor >= 7) {
      console.log('🎯 Sponsor modal triggered!');
      // Refetch sponsored ad
      refetch();

      // Show sponsor modal
      setModalVisible((prev) => ({ ...prev, sponsor: true }));

      // Navigate to sponsor page after a delay
      setTimeout(() => {
        console.log('🚀 Navigating to sponsor page...');
        router.push('/sponsor');
      }, 5000);

      // Reset counts and set next modal
      resetActivityCount('sponsor');
      resetActivityCount('share');
      setNextModal('share');
    }
  }, [activityCounts, nextModal, refetch, router]);

  // Function to reset modal visibility
  const resetModalVisibility = (type: keyof ModalVisible) => {
    setModalVisible((prev) => ({ ...prev, [type]: false }));
  };

  return (
    <ActivityContext.Provider
      value={{
        activityCounts,
        incrementActivity,
        modalVisible,
        resetModalVisibility,
        sponsoredAd,
        isAdLoading: isLoading,
      }}
    >
      {children}
    </ActivityContext.Provider>
  );
};

// Custom hook to use the ActivityContext
export const useActivity = (): ActivityContextType => {
  const context = useContext(ActivityContext);
  if (!context) {
    throw new Error('useActivity must be used within an ActivityProvider');
  }
  return context;
};

// Custom hook for tracking specific activity types
export const useTrackActivity = (type: 'sponsor' | 'share') => {
  const { activityCounts, incrementActivity, modalVisible } = useActivity();
  
  const trackActivity = () => {
    incrementActivity(type);
  };

  return {
    trackActivity,
    activityCounts,
    modalVisible,
  };
};

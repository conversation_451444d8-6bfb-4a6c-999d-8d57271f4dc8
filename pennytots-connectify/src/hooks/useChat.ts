import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { userAuthInfo, userToken } from '@/redux/user/reducer';
import { useChatActions } from '@/redux/chat/hooks';
import socketService from '@/services/socket';
import attachmentUploadService, {
  AttachmentUploadOptions,
  AttachmentUploadResult,
  UploadProgress
} from '@/services/attachment-upload';
import { ChatUser, ChatData, navigateToPrivateChat } from '@/lib/chat-utils';
import toastService, { formatFileName, getUserDisplayName } from '@/services/toast';

export interface ChatMessage {
  _id: string;
  message: string;
  senderId: string;
  createdAt: string | Date;
  user: {
    _id: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
  type?: string;
  attachment?: string;
  attachmentType?: string;
  quotedReply?: any;
  pending?: boolean;
}

export interface UseChatOptions {
  autoConnect?: boolean;
  enableTypingIndicators?: boolean;
  enableNotifications?: boolean;
  enableToasts?: boolean;
}

export interface ChatState {
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  activeChat: ChatData | null;
  messages: ChatMessage[];
  typingUsers: string[];
  uploadProgress: UploadProgress | null;
  isUploading: boolean;
}

export const useChat = (options: UseChatOptions = {}) => {
  const {
    autoConnect = true,
    enableTypingIndicators = true,
    enableNotifications = true,
    enableToasts = true
  } = options;

  const router = useRouter();
  const currentUser = useSelector(userAuthInfo);
  const token = useSelector(userToken);
  const chatActions = useChatActions();

  const [state, setState] = useState<ChatState>({
    isConnected: false,
    isLoading: false,
    error: null,
    activeChat: null,
    messages: [],
    typingUsers: [],
    uploadProgress: null,
    isUploading: false
  });

  // Initialize socket connection
  useEffect(() => {
    if (autoConnect && token && currentUser) {
      connectSocket();
    }

    return () => {
      disconnectSocket();
    };
  }, [autoConnect, token, currentUser]);

  const connectSocket = useCallback(() => {
    if (!token) return;

    try {
      const socket = socketService.connect(token);
      setState(prev => ({ ...prev, isConnected: true, error: null }));

      if (enableToasts) {
        toastService.connectionRestored();
      }

      // Set up socket event listeners
      socketService.onPrivateMessage((message: ChatMessage) => {
        setState(prev => ({
          ...prev,
          messages: [message, ...prev.messages]
        }));
      });

      if (enableTypingIndicators) {
        socketService.onUserTyping((data: any) => {
          if (data.userId !== currentUser?._id) {
            setState(prev => ({
              ...prev,
              typingUsers: [...prev.typingUsers.filter(id => id !== data.userId), data.userId]
            }));
          }
        });

        socketService.onUserStoppedTyping((data: any) => {
          setState(prev => ({
            ...prev,
            typingUsers: prev.typingUsers.filter(id => id !== data.userId)
          }));
        });
      }

    } catch (error) {
      console.error('Failed to connect socket:', error);
      setState(prev => ({
        ...prev,
        isConnected: false,
        error: 'Failed to connect to chat service'
      }));

      if (enableToasts) {
        toastService.connectionLost();
      }
    }
  }, [token, currentUser, enableTypingIndicators]);

  const disconnectSocket = useCallback(() => {
    socketService.disconnect();
    setState(prev => ({ ...prev, isConnected: false }));
  }, []);

  // Create or get chat with user
  const createChat = useCallback(async (targetUser: ChatUser): Promise<ChatData | null> => {
    if (!currentUser || targetUser._id === currentUser._id) {
      setState(prev => ({ ...prev, error: 'Cannot chat with yourself' }));
      return null;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const chatData = await chatActions.getOrCreateUserChat(targetUser._id);
      setState(prev => ({
        ...prev,
        activeChat: chatData,
        isLoading: false
      }));

      if (enableToasts) {
        const userName = getUserDisplayName(targetUser);
        toastService.chatCreated(userName);
      }

      return chatData;
    } catch (error: any) {
      console.error('Failed to create chat:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to create chat'
      }));

      if (enableToasts) {
        const userName = getUserDisplayName(targetUser);
        toastService.chatCreationFailed(userName, error.message);
      }

      return null;
    }
  }, [currentUser, chatActions]);

  // Navigate to chat with user
  const navigateToChat = useCallback(async (targetUser: ChatUser) => {
    const chatData = await createChat(targetUser);
    if (chatData) {
      await navigateToPrivateChat(router, targetUser, currentUser?._id);
    }
  }, [createChat, router, currentUser]);

  // Send message
  const sendMessage = useCallback(async (
    messageText: string, 
    chatId: string,
    options: {
      quotedMessage?: any;
      attachment?: File;
      attachmentType?: string;
    } = {}
  ) => {
    if (!currentUser || !token) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }));
      return false;
    }

    const { quotedMessage, attachment, attachmentType } = options;

    // Create local message
    const localId = `local-${Date.now()}`;
    const now = new Date().toISOString();
    
    const localMessage: ChatMessage = {
      _id: localId,
      message: messageText,
      senderId: currentUser._id,
      createdAt: now,
      user: {
        _id: currentUser._id,
        first_name: currentUser.first_name,
        last_name: currentUser.last_name,
        profile_picture: currentUser.profile_picture,
      },
      pending: true,
      ...(quotedMessage && { quotedReply: quotedMessage }),
      ...(attachmentType && { type: attachmentType }),
    };

    // Add to messages immediately
    setState(prev => ({
      ...prev,
      messages: [localMessage, ...prev.messages]
    }));

    try {
      // If there's an attachment, upload it first
      if (attachment && attachmentType) {
        return await sendMessageWithAttachment(messageText, chatId, attachment, attachmentType, quotedMessage);
      }

      // Send text message via socket
      const messageToSend = {
        chatId,
        accountId: state.activeChat?.accountId._id,
        message: messageText,
        type: 'message',
        token,
        localId,
        clientCreatedAt: now,
        ...(quotedMessage && { quotedData: quotedMessage }),
      };

      socketService.sendPrivateMessage(messageToSend, (response) => {
        if (!response.success) {
          // Remove the pending message on error
          setState(prev => ({
            ...prev,
            messages: prev.messages.filter(msg => msg._id !== localId),
            error: response.message || 'Failed to send message'
          }));

          if (enableToasts) {
            toastService.messageSendFailed(response.message);
          }
        } else {
          // Update the local message with server response
          setState(prev => ({
            ...prev,
            messages: prev.messages.map(msg =>
              msg._id === localId
                ? { ...msg, _id: response.message._id, pending: false }
                : msg
            )
          }));

          if (enableToasts) {
            toastService.messageSent();
          }
        }
      });

      return true;
    } catch (error: any) {
      console.error('Failed to send message:', error);
      setState(prev => ({
        ...prev,
        messages: prev.messages.filter(msg => msg._id !== localId),
        error: error.message || 'Failed to send message'
      }));

      if (enableToasts) {
        toastService.messageSendFailed(error.message);
      }

      return false;
    }
  }, [currentUser, token, state.activeChat]);

  // Send message with attachment
  const sendMessageWithAttachment = useCallback(async (
    messageText: string,
    chatId: string,
    file: File,
    attachmentType: string,
    quotedMessage?: any
  ) => {
    setState(prev => ({ ...prev, isUploading: true, uploadProgress: null }));

    try {
      const uploadOptions: AttachmentUploadOptions = {
        chatId,
        file,
        message: messageText,
        type: attachmentType as any,
        onProgress: (progress: UploadProgress) => {
          setState(prev => ({ ...prev, uploadProgress: progress }));
        },
        onSuccess: (result: AttachmentUploadResult) => {
          setState(prev => ({
            ...prev,
            isUploading: false,
            uploadProgress: null
          }));

          if (enableToasts) {
            const fileName = formatFileName(file.name);
            toastService.attachmentUploaded(fileName);
          }
        },
        onError: (error: string) => {
          setState(prev => ({
            ...prev,
            isUploading: false,
            uploadProgress: null,
            error
          }));

          if (enableToasts) {
            const fileName = formatFileName(file.name);
            toastService.attachmentUploadFailed(fileName, error);
          }
        }
      };

      if (enableToasts) {
        const fileName = formatFileName(file.name);
        toastService.attachmentUploading(fileName);
      }

      let result: AttachmentUploadResult;
      
      switch (attachmentType) {
        case 'image':
          result = await attachmentUploadService.uploadImage(uploadOptions);
          break;
        case 'video':
          result = await attachmentUploadService.uploadVideo(uploadOptions);
          break;
        case 'audio':
          result = await attachmentUploadService.uploadAudio(uploadOptions);
          break;
        case 'document':
          result = await attachmentUploadService.uploadDocument(uploadOptions);
          break;
        default:
          throw new Error('Unsupported attachment type');
      }

      return result.success;
    } catch (error: any) {
      console.error('Failed to upload attachment:', error);
      setState(prev => ({
        ...prev,
        isUploading: false,
        uploadProgress: null,
        error: error.message || 'Failed to upload attachment'
      }));

      if (enableToasts) {
        const fileName = formatFileName(file.name);
        toastService.attachmentUploadFailed(fileName, error.message);
      }

      return false;
    }
  }, []);

  // Typing indicators
  const startTyping = useCallback((chatId: string) => {
    if (enableTypingIndicators && state.isConnected) {
      socketService.emitTyping(chatId);
    }
  }, [enableTypingIndicators, state.isConnected]);

  const stopTyping = useCallback((chatId: string) => {
    if (enableTypingIndicators && state.isConnected) {
      socketService.emitStoppedTyping(chatId);
    }
  }, [enableTypingIndicators, state.isConnected]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Set active chat
  const setActiveChat = useCallback((chat: ChatData | null) => {
    setState(prev => ({ ...prev, activeChat: chat, messages: [] }));
  }, []);

  return {
    // State
    ...state,
    
    // Actions
    connectSocket,
    disconnectSocket,
    createChat,
    navigateToChat,
    sendMessage,
    sendMessageWithAttachment,
    startTyping,
    stopTyping,
    clearError,
    setActiveChat,
    
    // Chat actions from Redux
    ...chatActions
  };
};

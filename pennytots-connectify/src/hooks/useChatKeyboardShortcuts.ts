import { useEffect, useCallback, useState, RefObject } from 'react';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  description: string;
  action: () => void;
  preventDefault?: boolean;
  stopPropagation?: boolean;
  disabled?: boolean;
}

export interface ChatKeyboardShortcutsOptions {
  inputRef?: RefObject<HTMLTextAreaElement | HTMLInputElement>;
  containerRef?: RefObject<HTMLElement>;
  shortcuts?: KeyboardShortcut[];
  enableGlobalShortcuts?: boolean;
  enableHelpDialog?: boolean;
}

export const useChatKeyboardShortcuts = (options: ChatKeyboardShortcutsOptions = {}) => {
  const {
    inputRef,
    containerRef,
    shortcuts: customShortcuts = [],
    enableGlobalShortcuts = true,
    enableHelpDialog = true
  } = options;

  const [showHelpDialog, setShowHelpDialog] = useState(false);

  // Default chat shortcuts
  const defaultShortcuts: KeyboardShortcut[] = [
    {
      key: 'Enter',
      description: 'Send message',
      action: () => {
        // This is handled separately in the input component
        // to avoid conflicts with shift+enter for new line
      },
      disabled: true
    },
    {
      key: 'Escape',
      description: 'Cancel reply / Close dialog',
      action: () => {
        if (showHelpDialog) {
          setShowHelpDialog(false);
        }
      }
    },
    {
      key: '/',
      description: 'Focus message input',
      action: () => {
        if (inputRef?.current) {
          inputRef.current.focus();
        }
      }
    },
    {
      key: '?',
      shiftKey: true,
      description: 'Show keyboard shortcuts',
      action: () => {
        setShowHelpDialog(prev => !prev);
      }
    }
  ];

  // Combine default and custom shortcuts
  const allShortcuts = [...defaultShortcuts, ...customShortcuts];

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Skip if target is an input or textarea (except for global shortcuts)
    const isInputElement = 
      event.target instanceof HTMLInputElement || 
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement ||
      (event.target as HTMLElement).isContentEditable;

    // Find matching shortcut
    const matchingShortcut = allShortcuts.find(shortcut => {
      if (shortcut.disabled) return false;
      
      // Skip non-global shortcuts if target is an input element
      if (isInputElement && !enableGlobalShortcuts) return false;
      
      const keyMatch = event.key === shortcut.key;
      const ctrlMatch = !!shortcut.ctrlKey === event.ctrlKey;
      const altMatch = !!shortcut.altKey === event.altKey;
      const shiftMatch = !!shortcut.shiftKey === event.shiftKey;
      const metaMatch = !!shortcut.metaKey === event.metaKey;
      
      return keyMatch && ctrlMatch && altMatch && shiftMatch && metaMatch;
    });

    if (matchingShortcut) {
      if (matchingShortcut.preventDefault !== false) {
        event.preventDefault();
      }
      
      if (matchingShortcut.stopPropagation !== false) {
        event.stopPropagation();
      }
      
      matchingShortcut.action();
    }
  }, [allShortcuts, enableGlobalShortcuts, inputRef]);

  // Set up event listeners
  useEffect(() => {
    const targetElement = containerRef?.current || document;
    
    targetElement.addEventListener('keydown', handleKeyDown);
    
    return () => {
      targetElement.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, containerRef]);

  // Helper to register a new shortcut
  const registerShortcut = useCallback((shortcut: KeyboardShortcut) => {
    allShortcuts.push(shortcut);
  }, [allShortcuts]);

  // Helper to unregister a shortcut
  const unregisterShortcut = useCallback((key: string, modifiers?: { 
    ctrlKey?: boolean;
    altKey?: boolean;
    shiftKey?: boolean;
    metaKey?: boolean;
  }) => {
    const index = allShortcuts.findIndex(shortcut => {
      if (shortcut.key !== key) return false;
      
      if (modifiers) {
        return (
          (modifiers.ctrlKey === undefined || shortcut.ctrlKey === modifiers.ctrlKey) &&
          (modifiers.altKey === undefined || shortcut.altKey === modifiers.altKey) &&
          (modifiers.shiftKey === undefined || shortcut.shiftKey === modifiers.shiftKey) &&
          (modifiers.metaKey === undefined || shortcut.metaKey === modifiers.metaKey)
        );
      }
      
      return true;
    });
    
    if (index !== -1) {
      allShortcuts.splice(index, 1);
    }
  }, [allShortcuts]);

  // Format shortcut for display
  const formatShortcut = useCallback((shortcut: KeyboardShortcut): string => {
    const parts: string[] = [];
    
    if (shortcut.ctrlKey) parts.push('Ctrl');
    if (shortcut.altKey) parts.push('Alt');
    if (shortcut.shiftKey) parts.push('Shift');
    if (shortcut.metaKey) parts.push('⌘');
    
    // Format special keys
    let key = shortcut.key;
    if (key === ' ') key = 'Space';
    if (key === 'ArrowUp') key = '↑';
    if (key === 'ArrowDown') key = '↓';
    if (key === 'ArrowLeft') key = '←';
    if (key === 'ArrowRight') key = '→';
    
    parts.push(key);
    
    return parts.join(' + ');
  }, []);

  return {
    shortcuts: allShortcuts,
    registerShortcut,
    unregisterShortcut,
    formatShortcut,
    showHelpDialog,
    setShowHelpDialog
  };
};

export default useChatKeyboardShortcuts;

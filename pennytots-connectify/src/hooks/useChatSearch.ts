import { useState, useCallback, useEffect } from 'react';
import { useChatContext } from '@/providers/ChatProvider';
import { ChatSearchFilters, ChatSearchResult } from '@/components/chat/ChatSearch';

export interface UseChatSearchOptions {
  initialQuery?: string;
  initialFilters?: ChatSearchFilters;
  debounceMs?: number;
  maxResults?: number;
  searchInMessages?: boolean;
}

export const useChatSearch = (options: UseChatSearchOptions = {}) => {
  const {
    initialQuery = '',
    initialFilters = {},
    debounceMs = 300,
    maxResults = 20,
    searchInMessages = true
  } = options;

  const { chatList, searchChats } = useChatContext();
  
  const [query, setQuery] = useState(initialQuery);
  const [filters, setFilters] = useState<ChatSearchFilters>(initialFilters);
  const [results, setResults] = useState<ChatSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Search in local chat list
  const searchLocalChats = useCallback((
    searchQuery: string, 
    searchFilters: ChatSearchFilters
  ): ChatSearchResult[] => {
    if (!chatList) return [];

    return chatList
      .filter(chat => {
        // Apply filters
        if (searchFilters.hasAttachments && !chat.lastMessage?.attachment) {
          return false;
        }
        
        if (searchFilters.isUnread && !chat.unreadCount) {
          return false;
        }
        
        if (searchFilters.isMuted && !chat.muted) {
          return false;
        }

        // Apply text search
        if (searchQuery) {
          const userName = `${chat.accountId.firstName || chat.accountId.first_name} ${chat.accountId.lastName || chat.accountId.last_name}`.toLowerCase();
          const lastMessage = chat.lastMessage?.message?.toLowerCase() || '';
          
          return userName.includes(searchQuery.toLowerCase()) || 
                 lastMessage.includes(searchQuery.toLowerCase());
        }
        
        return true;
      })
      .map(chat => ({
        chatId: chat._id,
        type: 'chat',
        title: `${chat.accountId.firstName || chat.accountId.first_name} ${chat.accountId.lastName || chat.accountId.last_name}`,
        subtitle: chat.lastMessage?.message || 'No messages yet',
        timestamp: new Date(chat.lastMessage?.createdAt || Date.now()),
        participants: [
          {
            id: chat.accountId._id,
            name: `${chat.accountId.firstName || chat.accountId.first_name} ${chat.accountId.lastName || chat.accountId.last_name}`,
            avatar: chat.accountId.profilePicture || chat.accountId.profile_picture
          }
        ],
        hasAttachments: !!chat.lastMessage?.attachment,
        isUnread: !!chat.unreadCount
      }))
      .slice(0, maxResults);
  }, [chatList, maxResults]);

  // Search in messages via API
  const searchMessages = useCallback(async (
    searchQuery: string,
    searchFilters: ChatSearchFilters
  ): Promise<ChatSearchResult[]> => {
    if (!searchQuery || !searchInMessages) return [];
    
    try {
      const response = await searchChats({
        query: searchQuery,
        filters: {
          hasAttachments: searchFilters.hasAttachments,
          messageTypes: searchFilters.messageTypes,
          dateRange: searchFilters.dateRange
        }
      });
      
      if (!response || !response.results) return [];
      
      return response.results.map(item => ({
        chatId: item.chatId,
        messageId: item.messageId,
        type: 'message',
        title: item.chatName || 'Chat',
        subtitle: item.message || '',
        snippet: item.message,
        timestamp: new Date(item.timestamp),
        participants: item.participants.map(p => ({
          id: p.id,
          name: p.name,
          avatar: p.avatar
        })),
        messageType: item.messageType,
        hasAttachments: item.hasAttachment
      }));
    } catch (err: any) {
      setError(err.message || 'Failed to search messages');
      return [];
    }
  }, [searchChats, searchInMessages]);

  // Combined search function
  const performSearch = useCallback(async (
    searchQuery: string,
    searchFilters: ChatSearchFilters
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // First search in local chats (synchronous)
      const chatResults = searchLocalChats(searchQuery, searchFilters);
      
      // Then search in messages (asynchronous)
      let messageResults: ChatSearchResult[] = [];
      if (searchQuery && searchInMessages) {
        messageResults = await searchMessages(searchQuery, searchFilters);
      }
      
      // Combine and sort results
      const combinedResults = [...chatResults, ...messageResults]
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, maxResults);
      
      setResults(combinedResults);
    } catch (err: any) {
      setError(err.message || 'Search failed');
    } finally {
      setIsLoading(false);
    }
  }, [searchLocalChats, searchMessages, searchInMessages, maxResults]);

  // Debounced search
  useEffect(() => {
    if (!query && Object.keys(filters).length === 0) {
      setResults([]);
      return;
    }
    
    const timer = setTimeout(() => {
      performSearch(query, filters);
    }, debounceMs);
    
    return () => clearTimeout(timer);
  }, [query, filters, debounceMs, performSearch]);

  // Handle search
  const handleSearch = useCallback((searchQuery: string, searchFilters: ChatSearchFilters) => {
    setQuery(searchQuery);
    setFilters(searchFilters);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery('');
    setFilters({});
    setResults([]);
  }, []);

  return {
    query,
    filters,
    results,
    isLoading,
    error,
    handleSearch,
    clearSearch,
    setQuery,
    setFilters
  };
};

export default useChatSearch;

'use client';

import React, { useState } from 'react';
import {
    Bell,
    Shield,
    Moon,
    Globe,
    Smartphone,
    Lock,
    User,
    Settings as SettingsIcon,
   
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';
import { useRouter } from 'next/navigation';
import { PostModal } from '../ui/post-modal';
import Image from 'next/image'
import InterestIcon from '@/assets/svg/interest.svg';
import BlockedIcon from '@/assets/svg/blocked-chat.svg';
import LogoutIcon from '@/assets/svg/logout.svg'
import ChangePin from '@/assets/svg/change-pin.svg';
import DeleteIcon from '@/assets/svg/delete-account.svg'
import MembershipIcon from '@/assets/svg/membership-icon.svg';
import UsertermsIcon from '@/assets/svg/user-terms.svg';
import AboutIcon from '@/assets/svg/about.svg'
import EmptyPagesSVG from '@/assets/svg/empty-pages.svg'

import { logout, useDeleteAccount } from '@/redux/user/hooks';
import UserTerms from './user-terms-formatted';
import PrivacyPolicy from './privacy-policy';
import AboutConnectify from './about-connectify';
import { MembershipScreen } from './membership-screen';

const Settings = () => {
    type SidebarContent = 'user-terms' | 'privacy-policy' | 'about' | 'eula' | 'membership' | null;
    const router = useRouter();
    const [isOpenModel, setIsOpenModel] = useState(false);
    const [modalMode, setModalMode] = useState<'post' | 'helpdesk' | 'area-of-interest' | 'change-pin' | 'blocked-chat' | 'delete-account' | 'sponsoredship-plan' >('post');
    const [activeSidebar, setActiveSidebar] = useState<SidebarContent>(null);
    const handleLogout = () => {
        logout()
    };

    const handleUserTerms = () => {
        setActiveSidebar(activeSidebar === 'user-terms' ? null : 'user-terms');
    };
    const handlePrivacyPolicy = () => {
        setActiveSidebar(activeSidebar === 'privacy-policy' ? null : 'privacy-policy');
    };

    const handleAbout = () => {
        setActiveSidebar(activeSidebar === 'about' ? null : 'about');
    };

    const handleEULA = () => {
        setActiveSidebar(activeSidebar === 'eula' ? null : 'eula');
    };

    const handleDeleteAccount = () => {
        setIsOpenModel(true);
        setModalMode('delete-account')
    };

    const handleAIsOpenArea = () => {
        setModalMode('area-of-interest');
        setIsOpenModel(true);
    };
    const handleBlockedChat = () => {
        setModalMode('blocked-chat')
        setIsOpenModel(true);
    }

    const handleIsOpenChangePIN = () => {
        setModalMode('change-pin')
        setIsOpenModel(true)
    }

    const handleMembership = () => {
        setActiveSidebar(activeSidebar === 'membership' ? null : 'membership');

    }

    const handleCloseModal = () => {
        setIsOpenModel(false);
    };
  const handleSponsorshipPlans =()=> {
    setIsOpenModel(true);
    setModalMode('sponsoredship-plan')
  }

    // Settings data structure similar to expo-main
    const settingsData = {
        preferences: {
            name: 'Preferences',

            items: [
                {
                    id: 1,
                    name: 'Interest',
                    icon: InterestIcon,
                    action: handleAIsOpenArea
                },
                // {
                //   id: 2,
                //   name: 'Language',
                //   icon: Globe,
                //   description: 'Change app language',
                //   action: () => console.log('Navigate to language settings')
                // }
            ]
        },
        account: {
            name: 'Account',
            icon: User,
            items: [
                {
                    id: 1,
                    name: 'Membership',
                    icon: MembershipIcon,
                    action: handleMembership
                },
                {
                    id: 2,
                    name: 'Change PIN',
                    icon: ChangePin,

                    action: handleIsOpenChangePIN
                },
                {
                    id: 3,
                    name: 'Blocked Chats',
                    icon: BlockedIcon,

                    action: handleBlockedChat
                },
                {
                    id: 4,
                    name: 'Logout',
                    icon: LogoutIcon,

                    action: handleLogout,
                    danger: true
                },
                {
                    id: 5,
                    name: 'Delete Account',
                    icon: DeleteIcon,

                    action: handleDeleteAccount,
                    danger: true
                }
            ]
        },
        help: {
            name: 'Help',

            items: [
                {
                    id: 1,
                    name: 'User Terms',
                    icon: UsertermsIcon,
                    action: handleUserTerms,
                    hasSidebar: true
                },

                {
                    id: 2,
                    name: 'Privacy Policy',
                    icon: UsertermsIcon,
                    action: handlePrivacyPolicy,
                    hasSidebar: true
                },
                {
                    id: 3,
                    name: 'End-User License Agreement',
                    icon: UsertermsIcon,
                    action: handleEULA,
                    hasSidebar: true
                },
                {
                    id: 4,
                    name: 'About Connectify',
                    icon: AboutIcon,
                    action: handleAbout,
                    hasSidebar: true
                },
            ]
        }
    };

    const renderSidebarContent = () => {
        switch (activeSidebar) {
            case 'user-terms':
                return <UserTerms />;
            case 'privacy-policy':
               return <PrivacyPolicy />;
              case 'about':
            return <AboutConnectify />;
            case 'membership':
            return <MembershipScreen  closeModal={handleCloseModal}
      handleSponsorshipPlans={handleSponsorshipPlans} />;
            case 'eula':
                return (
                    <div className="p-6">
                        <h2 className="text-xl font-semibold mb-4">End-User License Agreement</h2>
                        <p className="text-gray-600">EULA content goes here...</p>
                    </div>
                );
            default:
                return (
                    <div className='h-full overflow-hidden'>
                         <div className="flex items-center justify-between w-full rounded-tr-lg rounded-br-lg h-[46px] bg-[#E9E9E9]">
                            </div>


                   
                    <div className="flex items-center justify-center h-full">
                      <EmptyPagesSVG />
                    </div>
                     </div>
                );
        }
    };


    return (
        <div className="w-full flex h-screen">
            {/* Settings Panel - Fixed Width */}
            <div className='w-[428px] flex-shrink-0'>

         
            <div className="flex w-[inherit] flex-col bg-white ">
                {Object.entries(settingsData).map(([key, section]) => (
                    <Card key={key} className="cursor-pointer border-0 rounded-none">
                        <CardHeader className="bg-[#FAFAFA] py-[11px] mb-[1rem] px-[1rem]">
                            <CardTitle className="flex items-center">
                                <span className="text-[#5A5E5C] text-[1rem] font-medium">
                                    {section.name}
                                </span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {section.items.map((item) => {
                                const showBottomBorder = item.name === 'About Connectify' || item.name === 'Delete Account';
                                const isActive =
                                    (item.name === 'User Terms' && activeSidebar === 'user-terms') ||
                                    (item.name === 'Privacy Policy' && activeSidebar === 'privacy-policy') ||
                                    (item.name === 'About Connectify' && activeSidebar === 'about') ||
                                    (item.name === 'End-User License Agreement' && activeSidebar === 'eula');

                                return (
                                    <div
                                        key={item.id}
                                        onClick={item.action}
                                        className={`flex hover:bg-gray-50 cursor-pointer transition-colors ${showBottomBorder ? 'border-b border-[#F6F6F6] mb-[1rem]' : ''
                                            } ${isActive ? 'bg-[#FAFAFA] ' : ''}`}
                                    >
                                        <div className="flex items-center px-[2rem] mb-[8px] w-full">
                                            <div className="flex items-center gap-[1rem] py-[11px] w-full">
                                                <item.icon className="text-[#F2875D]" />
                                                <div className="flex items-center justify-between w-full">
                                                    <p className="text-[0.875rem] text-[#5A5E5C] ">
                                                        {item.name}
                                                    </p>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </CardContent>
                    </Card>
                ))}
            </div>
            {isOpenModel && (
                 <PostModal isOpen={isOpenModel} onClose={handleCloseModal} mode={modalMode} />
            )}
               </div>

            {/* Dynamic Content Panel - Flexible Width */}
            <div className="flex flex-col bg-white h-screen flex-1 min-h-0">
                {activeSidebar &&(
                    <div className="flex-shrink-0 ">
                        <div className="flex items-center justify-between">
                            <h2 className={`text-[#5A5E5C] ${poppinsMedium.className} text-[0.875rem] font-semibold`}>
                                {activeSidebar === 'user-terms' && (
                                    <span className="flex items-center gap-[12px]">
                                        <UsertermsIcon  />
                                        
                                        User Terms
                                    </span>
                                )}
                                {activeSidebar === 'privacy-policy' && (
                                    <span className="flex items-center gap-[12px]">
                                        <UsertermsIcon  />
                                        Privacy Policy
                                    </span>
                                )}
                                {activeSidebar === 'about' && (
                                    <span className="flex items-center gap-[12px]">
                                     <UsertermsIcon  />
                                        About Connectify
                                    </span>
                                )}
                                {activeSidebar === 'eula' && (
                                    <span className="flex items-center gap-[12px]">
                                     <UsertermsIcon  />
                                        End-User License Agreement
                                    </span>
                                )}
                                    {activeSidebar === 'membership' && (
                                    <span className="flex items-center gap-[12px]">
                                     <MembershipIcon  />
                                        Membership
                                    </span>
                                )}
                            </h2>
                           
                        </div>
                    </div>
                )}
                <div className="flex-1 overflow-auto min-h-0">
                    {renderSidebarContent()}
                </div>
            </div>

        </div>
    );
};

export default Settings;


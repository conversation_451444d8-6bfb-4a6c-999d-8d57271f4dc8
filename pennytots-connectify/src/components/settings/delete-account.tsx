import React, { useState } from 'react'
import { Button } from '../ui/button';
import { poppinsMedium, poppinsRegular, poppinsSemibold } from '@/fonts';
import { Trash2 } from 'lucide-react';
import { useDeleteAccount } from '@/redux/user/hooks';

interface DeleteAccountProps {
   closeModal?: () => void
}
const DeleteAccount: React.FC<DeleteAccountProps> = ({closeModal}) =>  {

  const confirmDeleteAccount = () => {
      useDeleteAccount();
    if(closeModal) {
        closeModal();
    }

  };

  return (
    <div>
        
          
                <div className=" w-full">
            
                  <p className={`${poppinsRegular.className} text-gray-600 mb-6`}>
                    Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.
                  </p>
                  <div className="flex justify-center w-full">

                  
                  <div className="flex w-[50%] justify-between">
                    <Button
                      variant="cancelButton"
                      onClick={closeModal}
                      className={`${poppinsMedium.className}`}
                    >
                      Cancel
                    </Button>
                    <Button
                    variant="deleteButton"
                      onClick={confirmDeleteAccount}
                      className={`${poppinsMedium.className} `}
                    >
                      Delete
                    </Button>
                  </div>
                  </div>
                </div>
         
        
    </div>
  )
}

export default DeleteAccount

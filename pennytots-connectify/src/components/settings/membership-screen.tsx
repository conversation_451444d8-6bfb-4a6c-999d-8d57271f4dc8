'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { poppinsMedium, poppinsRegular, poppinsSemibold } from '@/fonts';
import { RefreshCw } from 'lucide-react';
import { 
  useGetSubscription, 
  useGetFreeSubscription,
  useGetCredit 
} from '@/redux/credit/hooks';
import { SponsorshipPlansModal } from '@/components/ui/sponsorship-plans-modal';
import QuizPage from '@/app/quiz/page';
import { CardHeader, CardTitle } from '../ui/card';
import { PostModal } from '../ui/post-modal';

interface MembershipScreenProps {
  closeModal?: () => void;
  handleSponsorshipPlans?: () => void;
}

export const MembershipScreen: React.FC<MembershipScreenProps> = ({ closeModal, handleSponsorshipPlans }) => {
  const [showSponsorshipPlans, setShowSponsorshipPlans] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error' | null>(null);
    const [modalMode, setModalMode] = useState<'post' |'sponsoredship-plan' >('post');
        const [isOpenModel, setIsOpenModel] = useState(false);
    
  const {
    data: subscription,
    isLoading: isSubscriptionLoading,
    refetch: refetchSubscription,
    error: subscriptionError
  } = useGetSubscription();

  useEffect(() => {
   console.log(subscription, 'subscription');
  }, [subscription]);

  const {
    data: credit,
    isLoading: isCreditLoading,
    refetch: refetchCredit
  } = useGetCredit();

  const {
    mutate: getFreeSubscription,
    isPending: freeSubscriptionLoading
  } = useGetFreeSubscription();

  const handleRefresh = () => {
    refetchSubscription();
    refetchCredit();
  };



  const handleFreePlan = () => {
    // Check if the subscription exists and if daysLeft is not 0
    if (subscription && subscription.daysLeft !== 0) {
      setAlertMessage('You still have days left on your current plan');
      setAlertType('error');
      return;
    }

    getFreeSubscription(undefined, {
      onSuccess: (response: any) => {
        setAlertMessage(response.message || 'Free subscription activated successfully!');
        setAlertType('success');
        refetchSubscription();
      },
      onError: (error: any) => {
        setAlertMessage(
          error.response?.data?.message ||
          error.message ||
          "Looks like you've already received your free subscription in the past 5 days"
        );
        setAlertType('error');
      },
    });
  };
function capitalizeFirstLetter(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

  // Clear alert after 5 seconds
  useEffect(() => {
    if (alertMessage) {
      const timer = setTimeout(() => {
        setAlertMessage('');
        setAlertType(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [alertMessage]);
 const handleCloseModal = () => {
        setIsOpenModel(false);
    };
  const getDaysLeftDisplay = () => {
    if (isSubscriptionLoading) {
      return (
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-[24px] w-[24px] border-b-2 border-[#5A5E5C]"></div>
        </div>
      );
    }

    if (subscription) {
      return (
        <div className="">
          <p className={`${poppinsSemibold.className} text-[1rem] text-[#696969] `}>
            {subscription.daysLeft}
          </p>
          <p className={`${poppinsRegular.className} text-[1rem] text-[#696969]`}>
            {subscription.daysLeft === 1 ? 'day' : 'days'}
          </p>
        </div>
      );
    }

    return (
      <div className="">
        <p className={`${poppinsSemibold.className} text-[1rem] text-[#696969] `}>
          0
        </p>
    
      </div>
    );
  };

  const getCurrentPlanDisplay = () => {
  return subscription?.subscription ? (
    <h2 className={`${poppinsSemibold.className} text-[1rem] text-[#696969]`}>
      {subscription.subscription.subscriptionType.charAt(0).toUpperCase() +
        subscription.subscription.subscriptionType.slice(1)}{' '}
      Plan
    </h2>
  ) : (
    <h2 className={`${poppinsSemibold.className} text-[1rem] text-[#696969]`}>
      Free Plan
    </h2>
  );
};


  return (
    <div className='flex w-full  flex-col'>
    <div className="flex justify-center w-full">

   
    <div className="w-full mt-[3.4375rem] max-w-[475px]">
      {/* Alert Messages */}
      {alertMessage && (
        <div className={`mb-6 p-4 rounded-lg border ${
          alertType === 'success' 
            ? 'bg-green-50 border-green-200 text-green-700' 
            : 'bg-red-50 border-red-200 text-red-700'
        }`}>
          <p className={`${poppinsRegular.className} text-[0.875rem]`}>
            {alertMessage}
          </p>
        </div>
      )}

      {/* Header with Refresh */}
      <div className="flex flex-col gap-[1.25rem]">
        <h1 className={`${poppinsMedium.className} text-[0.875rem] text-[#4F4F4F]`}>
         Current subscription
        </h1>
       <div>
   <div className="">
          {getCurrentPlanDisplay()}
        </div>
</div>

     
      </div>

      {/* Membership Days Left */}
      <div className="text-center flex mt-[1.25rem] justify-between">
        <p className={`${poppinsRegular.className} text-[0.875rem] text-[#696969]`}>
          Membership days left
        </p>
        {getDaysLeftDisplay()}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between mt-[2.5rem]">
        <Button
          onClick={handleSponsorshipPlans}
           variant="cancelButton"
          disabled={isSubscriptionLoading}
          className="py-[11px] px-[2.5rem]"
        >
          <span className={`${poppinsMedium.className} text-[1rem]`}>
            Sponsorship Plans
          </span>
        </Button>

        <Button
          onClick={handleFreePlan}
          variant="deleteButton"
          className="py-[11px] px-[5rem] "
          disabled={freeSubscriptionLoading || isSubscriptionLoading}
        >
          <span className={`${poppinsMedium.className} `}>
           
            Free days
          </span>
        </Button>
      </div>

    

      {/* Sponsorship Plans Modal */}
  
    </div>
     </div>
    <div className='border-t border-[#D9D9D9] w-full  mt-[6.25rem] pt-[2.5rem] flex justify-center'>
           <CardHeader>
            <CardTitle className={`text-[#797C7B] ${poppinsMedium.className} text-[1rem] text-[#5A5E5C]`}>Trivia</CardTitle>
          </CardHeader>
<div className='w-full max-w-[480px] mt-[1.25rem]'>


   
    <QuizPage />
    </div>
     </div>
        </div>
  );
};

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import axios from 'axios';
import { useSelector } from 'react-redux';

// Types
interface User {
  _id: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
  company_position?: string;
  company?: string;
}

interface ChatItem {
  _id: string;
  accountId: User;
  userId?: User;
  lastMessage?: string;
  lastMessageDate?: string;
  unreadMessages?: number;
  blocked?: string[];
}

interface BlockChatsProps {
    closeModal?: () => void
  // Add any props you need
}

const BlockChats: React.FC<BlockChatsProps> = ({ closeModal }) => {
  const [chatList, setChatList] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);
  
  const router = useRouter();
  
  // Replace with your actual Redux selector
  const myId = useSelector((state: any) => state.user.userId);

  useEffect(() => {
    getChats();
  }, []);

  const timeAgo = (date: string) => {
    const now = new Date();
    const messageDate = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - messageDate.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const showModal = (config: any) => {
    // Replace with your modal implementation
    alert(config.message);
  };

  const blockUser = async (accountId: string) => {
    setLoading(true);
    try {
      const response = await axios.patch(`/api/chats/block/${accountId}`, {});
      const message = response.data.message === 'Blocked successfully' 
        ? 'User blocked' 
        : response.data.message;

      showModal({
        modalVisible: true,
        title: 'Alert',
        message: message,
        type: 'success-alert',
      });
      
      getChats();
    } catch (error) {
      console.error('Error blocking user:', error);
      showModal({
        modalVisible: true,
        title: 'Error',
        message: 'Failed to block user',
        type: 'error-alert',
      });
    } finally {
      setLoading(false);
    }
  };

  const unBlockUser = async (accountId: string) => {
    setLoading(true);
    try {
      const response = await axios.patch(`/api/chats/unblock/${accountId}`, {});
      const message = response.data.message === 'Chat has been unblocked successfully'
        ? 'User unblocked'
        : response.data.message;

      showModal({
        modalVisible: true,
        title: 'Alert',
        message: message,
        type: 'success-alert',
      });
      
      getChats();
    } catch (error) {
      console.error('Error unblocking user:', error);
      showModal({
        modalVisible: true,
        title: 'Error',
        message: 'Failed to unblock user',
        type: 'error-alert',
      });
    } finally {
      setLoading(false);
    }
  };

  const getChats = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/chats/my-chats/?blocked=true');
      setChatList(response.data.chats.docs);
    } catch (error) {
      console.error('Error fetching chats:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToChat = (userDetails: User) => {
    router.push({
      pathname: '/chat',
      query: { userId: userDetails._id }
    });
  };

  const toggleDropdown = (chatId: string) => {
    setDropdownOpen(dropdownOpen === chatId ? null : chatId);
  };

  const truncateName = (firstName: string, lastName: string, maxLength: number = 12) => {
    const fullName = `${firstName} ${lastName}`;
    return fullName.length > maxLength ? `${fullName.slice(0, maxLength)}...` : fullName;
  };

  return (
    <div className=" bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className=" px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Blocked Chats</h1>
            <div className="w-8"></div>
          </div>
        </div>
      </div>

      {/* Chat List */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : chatList.length === 0 ? (
          <div className="text-center py-16">
            <p className="text-gray-500">No blocked chats found</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {chatList.map((item) => (
              <div key={item._id} className="py-4 hover:bg-gray-50">
                <div className="flex items-start space-x-4">
                  {/* Avatar */}
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-gray-300 rounded-full overflow-hidden">
                      {item.accountId.profile_picture ? (
                        <Image
                          src={item.accountId.profile_picture}
                          alt="Profile"
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gray-400 text-white font-semibold">
                          {item.accountId.first_name?.[0]?.toUpperCase()}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Chat Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => navigateToChat(item.accountId)}
                          className="text-sm font-semibold text-gray-900 hover:text-blue-600"
                        >
                          {truncateName(item.accountId.first_name, item.accountId.last_name)}
                        </button>
                      </div>

                      {/* Dropdown Menu */}
                      <div className="relative">
                        <button
                          onClick={() => toggleDropdown(item._id)}
                          className="p-2 text-gray-400 hover:text-gray-600"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                          </svg>
                        </button>

                        {dropdownOpen === item._id && (
                          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                            <div className="py-1">
                              <button
                                onClick={() => {
                                  if (item.blocked?.includes(myId)) {
                                    unBlockUser(item.accountId._id);
                                  } else {
                                    blockUser(item.accountId._id);
                                  }
                                  setDropdownOpen(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              >
                                <svg className="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
                                </svg>
                                {item.blocked?.includes(myId) ? 'Unblock User' : 'Block User'}
                              </button>
                              <button
                                onClick={() => {
                                  // Implement report functionality
                                  console.log('Report user');
                                  setDropdownOpen(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              >
                                <svg className="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
                                </svg>
                                Report User
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Company Info */}
                    <p className="text-sm text-gray-500 mt-1">
                      {item.userId?.company_position && item.userId?.company
                        ? `${item.userId.company_position.replace(/(\r\n|\n|\r)/gm, '')} at ${item.userId.company}`
                        : item.userId?.company_position?.replace(/(\r\n|\n|\r)/gm, '')
                        || 'company'}
                    </p>

                    {/* Last Message Time */}
                    {item.lastMessageDate && (
                      <p className="text-xs text-gray-400 mt-1">
                        {timeAgo(item.lastMessageDate)}
                      </p>
                    )}

                    {/* Last Message */}
                    <div className="flex items-center justify-between mt-2">
                      {item.lastMessage && (
                        <p className="text-sm text-gray-600 truncate flex-1">
                          {item.lastMessage}
                        </p>
                      )}
                      
                      {item.unreadMessages && (
                        <span className="ml-2 inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-yellow-500 bg-black rounded-full">
                          {item.unreadMessages}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Click outside to close dropdown */}
      {dropdownOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setDropdownOpen(null)}
        />
      )}
    </div>
  );
};

export default BlockChats;
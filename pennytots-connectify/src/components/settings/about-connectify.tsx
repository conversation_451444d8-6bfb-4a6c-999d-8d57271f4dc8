import { poppinsMedium, poppinsRegular } from '@/fonts'
import React from 'react'

const AboutConnectify = () => {
  return (
    <div>
        <div className="text-[#5A5E5C] pl-[2.5rem] mt-[1.25rem]">
  <h1 className={`${poppinsMedium.className} text-[1rem]`}>About The LUCOSA Network App</h1>
  
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>
    <strong className={`${poppinsMedium.className}`}>The LUCOSA Network</strong> is a free to use mobile directory and messaging app developed by Pennytots Limited (UK), Laten Geuberen Limited (Canada) and iKnowAfrica Limited (Nigeria), on behalf of the LUCOSA Alumni Association. It is intended to strengthen the LUCOSA network through increased communication, member search, knowledge sharing, and collaborations.
  </p>

  <p className={`${poppinsRegular.className} text-[0.875rem]`}>
    <strong className={`${poppinsMedium.className}`}>The LUCOSA Network app</strong> features a search directory, professional profiles, account settings, document sharing capabilities, public posting, private chatting, group chatting, in-app payments, and more.
  </p>
</div>
    </div>
  )
}

export default AboutConnectify

import React, { useState } from 'react';
import { <PERSON>, EyeOff, Key } from 'lucide-react';
import { useChangePin } from '@/redux/change-pin/hooks';
import { toast } from 'react-toastify';
import { poppinsMedium } from '@/fonts';

interface ChangePinProps {
  closeModal?: () => void; 
 
  // Optional close function for modal mode
}

const ChangePin: React.FC<ChangePinProps> = ({ closeModal }) => {

  
  const [currentPIN, setCurrentPIN] = useState('');
  const [newPIN, setNewPIN] = useState('');
  const [showCurrentPin, setShowCurrentPin] = useState(false);
  const [showNewPin, setShowNewPin] = useState(false);
  
  // Custom hook for PIN change functionality
  const { mutate: changePINMutation, isPending, error } = useChangePin();

  const handleChangePIN = async () => {
    if (!currentPIN.trim()) {
      toast.error('Current PIN is required');
      return;
    }

    if (!newPIN.trim()) {
      toast.error('New PIN is required');
      return;
    }

    if (newPIN.length !== 4) {
      toast.error('PIN must be exactly 4 digits');
      return;
    }

    changePINMutation(
      { currentPIN, newPIN },
      {
        onSuccess: (data: any) => {
          toast.success(`${data?.message || 'Your PIN has been changed successfully'}`);
          // Clear form after successful change
          setCurrentPIN('');
          setNewPIN('');
        },
        onError: (err: any) => {
          const errorMessage = err?.data?.message || 'Check current PIN and try again';
        toast.error(errorMessage, 
            {
           onClose: () => {
          closeModal?.(); // ✅ Close the modal after success
        },
            }
        ) ; // Changed from toast.success to toast.error
        },
      }
    );
    
  };

  const handleCurrentPinChange = (value: string) => {
    // Only allow numeric input and max 4 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 4);
    setCurrentPIN(numericValue);
  };

  const handleNewPinChange = (value: string) => {
    // Only allow numeric input and max 4 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 4);
    setNewPIN(numericValue);
  };

  return (
    <div className={`bg-white `}>
  
            
      <div className="flex flex-col justify-center items-center">
        <div className={`w-full`}>
          <div className="flex flex-col ">
            {/* Current PIN Field */}
            <div className="flex flex-col gap-[8px] mt-[1.5rem]">
              <label className={`${poppinsMedium.className} text-[#4F4F4F] text-[0.875rem]`}>
                Previous pin
              </label>
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                </div>
                <input
                  type={showCurrentPin ? 'text' : 'password'}
                  className="w-full p-[1rem] border border-[#D0D5DD] rounded-[6px] focus:ring-1 focus:ring-[#F2875D] focus:border-transparent outline-none transition-all duration-200"
                  onChange={(e) => handleCurrentPinChange(e.target.value)}
                  placeholder=""
                  maxLength={4}
                  value={currentPIN}
                  inputMode="numeric"
                  pattern="[0-9]*"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  onClick={() => setShowCurrentPin(!showCurrentPin)}
                >
                  {showCurrentPin ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </div>

            {/* New PIN Field */}
            <div className="flex flex-col gap-[8px] mt-[1.5rem]">
              <label className={`${poppinsMedium.className} text-[#4F4F4F] text-[0.875rem]`}>
                New pin
              </label>
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                </div>
                <input
                  type={showNewPin ? 'text' : 'password'}
                  className="w-full p-[1rem] border border-[#D0D5DD] rounded-[6px] focus:ring-1 focus:ring-[#F2875D] focus:border-transparent outline-none transition-all duration-200"
                  onChange={(e) => handleNewPinChange(e.target.value)}
                  placeholder=""
                  maxLength={4}
                  value={newPIN}
                  inputMode="numeric"
                  pattern="[0-9]*"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  onClick={() => setShowNewPin(!showNewPin)}
                >
                  {showNewPin ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              {/* PIN strength indicator */}
              {newPIN.length > 0 && (
                <div className="text-xs text-gray-500">
                  {newPIN.length}/4 digits
                  {newPIN.length === 4 && (
                    <span className="text-green-600 ml-2">✓ Complete</span>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <div className={`mt-[1.5rem]`}>
            <button
              onClick={handleChangePIN}
              disabled={isPending || currentPIN.length !== 4 || newPIN.length !== 4}
              className="w-full cursor-pointer bg-[#163E23] hover:bg-b[#163E23] disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-[0.875rem]  rounded-[4px] transition-colors duration-200"
            >
              {isPending ? 'Changing PIN...' : 'Change '}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChangePin;
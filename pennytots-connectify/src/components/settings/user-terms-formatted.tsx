import React from 'react';
import { useRouter } from 'next/navigation';
import Head from 'next/head';
import { poppinsMedium, poppinsRegular } from '@/fonts';

interface UserTermsProps {
  // Add any props you need
}

const UserTerms: React.FC<UserTermsProps> = () => {
  const router = useRouter();

  return (
  <div className="text-[#5A5E5C] p-6">
  <h1 className={`${poppinsMedium.className} text-[1rem]`}>User Agreement</h1>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>This Agreement was last modified on 25th January 2025.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>This User Agreement describes the terms and conditions which you accept by using our Platform or our Services. We have incorporated by reference some linked information.</p>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>In this User Agreement:</h2>
  <ul className="list-disc pl-5">
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Account"</strong> means the account associated with your email address.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Dispute Resolution Process"</strong> means the process to be followed by Users in accordance with the Dispute Resolution Services.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Pennytots"</strong>, <strong className={`${poppinsMedium.className}`}>"We"</strong>, <strong className={`${poppinsMedium.className}`}>"Our"</strong>, <strong className={`${poppinsMedium.className}`}>"Company"</strong> or <strong className={`${poppinsMedium.className}`}>"The Company"</strong> or <strong className={`${poppinsMedium.className}`}>"Us"</strong> means Pennytots Limited (United Kingdom), Laten Geuberen Limited (Canada), Laten Geuberen Enterprises (Nigeria), iKnowAfrica Ventures Limited (USA) or iKnowAfrica Ventures Limited (Nigeria).</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Pennytots Verified"</strong> means Users have been satisfactorily verified under the Know your Customer and Identity Verification Policy.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Inactive Account"</strong> means a User Account that has not been logged into for a 6-month period, or other period determined by us from time to time.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Intellectual Property Rights"</strong> means any and all intellectual property rights, existing worldwide and the subject matter of such rights, including: (a) patents, copyright, rights in circuit layouts (or similar rights), registered designs, registered and unregistered trademarks, and any right to have confidential information kept confidential; and (b) any application or right to apply for registration of any of the rights referred to in paragraph (a), whether or not such rights are registered or capable of being registered and whether existing under any laws, at common law or in equity.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Pennytots Services"</strong> means all services provided by us to you.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"User"</strong>, <strong className={`${poppinsMedium.className}`}>"you"</strong> or <strong className={`${poppinsMedium.className}`}>"your"</strong> means an individual who visits or uses the Platform, including via the API.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"User Contract"</strong> means: (1) this User Agreement; (2) the Code of Conduct as amended from time to time; (3) any other material incorporated by reference from time to time.</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}><strong className={`${poppinsMedium.className}`}>"Platform"</strong> means the Platforms operated by Pennytots and available at: <a href="https://Pennytots.com" className={`text-[#0D99FF] ${poppinsRegular.className} text-[0.875rem]`}>Pennytots.com</a> and any of its regional or other domains or properties, and any related Pennytots service, tool or application, specifically including mobile web, any iOS App and any Android App, or API or other access mechanism.</li>
  </ul>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>1. Overview</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>By accessing the Platform, you agree to the following terms with Pennytots.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We may amend this User Agreement and any linked information from time to time by posting amended terms on the Platform, without notice to you.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>The Platform is an online venue where Users advertise items. Users must register for an Account in order to advertise items. We merely facilitate advertisements.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We may, from time to time, and without notice, change or add to the Platform or the information, products or services described in it. However, we do not undertake to keep the Platform updated. We are not liable to you or anyone else if any error occurs in the information on the Platform or if that information is not current.</p>

<div className="text-[#5A5E5C]">
  <h2 className={`${poppinsMedium.className} text-[1rem]`}>2. Scope</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Before using the Platform, you must read the whole User Agreement, the Platform policies and all linked information.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You must read and accept all of the terms in, and linked to, this User Agreement, the Code of Conduct, the Pennytots Privacy Policy and all Platform policies. By accepting this User Agreement as you access our Platform, you agree that this User Agreement will apply whenever you use the Platform, or when you use the tools we make available to interact with the Platform. Some Platforms may have additional or other terms that we provide to you when you use those services.</p>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>3. Eligibility</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You will not use the Platform if you:</p>
  <ul className="list-disc pl-5">
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>are not able to form legally binding contracts;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>a person barred from receiving and rendering services under the laws of any country or other applicable jurisdiction;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>are suspended from using the Platform; or</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>do not hold a valid email address.</li>
  </ul>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>All free user accounts are associated with individuals. Login credentials should not be shared by users with others. The individual associated with the account will be held responsible for all actions taken by the account, without limitation.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Subject to your local laws, a person over 15 but under 18 can use an adult's account with the permission of the account holder. However, the account holder is responsible for all actions taken by the account, without limitation.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Users may provide a business name or a company name, which is associated with the User's Account. Users acknowledge and agree that where a business name or company name is associated with their Account, this User Agreement is a contract with the User as an individual (not the business or company) and Users remain solely responsible for all activity undertaken in respect of their Account.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We may, at our absolute discretion, refuse to register any person or entity as a User.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You cannot transfer or assign any rights or obligations you have under this agreement without prior written consent.</p>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>4. Using Pennytots</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>While using the Platform, you will not attempt to or otherwise do any of the following:</p>
  <ul className="list-disc pl-5">
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>post content or items in inappropriate categories or areas on our Platforms and services;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>infringe any laws, third party rights or our policies, such as the Code of Conduct;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>circumvent or manipulate our fee structure, the billing process, or fees owed to Pennytots;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>post false, inaccurate, misleading, deceptive, defamatory or offensive content (including personal information);</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>take any action that may undermine the feedback or reputation systems (such as displaying, importing or exporting feedback information or using it for purposes unrelated to the Platform);</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>transfer your Pennytots account (including feedback) and Username to another party without our consent;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>distribute viruses or any other technologies that may harm Pennytots, the Platform, or the interests or property of Pennytots users (including their Intellectual Property Rights, privacy and publicity rights) or is unlawful, threatening, abusive, defamatory, invasive of privacy, vulgar, obscene, profane or which may harass or cause distress or inconvenience to, or incite hatred of, any person;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>download and aggregate listings from our website for display with listings from other websites without our express written permission, "frame", "mirror" or otherwise incorporate any part of the Platform into any other website without our prior written authorisation;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>attempt to modify, translate, adapt, edit, decompile, disassemble, or reverse engineer any software programs used by us in connection with the Platform;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>copy, modify or distribute rights or content from the Platform or Pennytots's copyrights and trademarks; or</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>harvest or otherwise collect information about Users, including email addresses, without their consent.</li>
  </ul>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>5. Intellectual Property Rights Infringement</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>It is our policy to respond to clear notices of alleged intellectual property rights infringement. Our Copyright Infringement Policy is designed to make submitting notices of alleged infringement to us as straightforward as possible while reducing the number of notices that we receive that are fraudulent or difficult to understand or verify. If you believe that your Intellectual Property Rights have been violated, please notify us via email and we will investigate.</p>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>6. Fees and Services</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We charge fees for our services. When you use a service that has a fee, you have an opportunity to review and accept the fees that you will be charged based on our Fees and Charges, which we may change from time to time and will update by placing on our Platform. We may choose to temporarily change the fees for our services for specific users or for promotional events or new services, and such changes are effective when we post a temporary promotional event or new service on the Platforms, or as notified through promotional correspondence.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Unless otherwise stated, all fees are quoted in percentages of currencies utilized by Users.</p>
</div>


<div className="text-[#5A5E5C]">
  <h2 className={`${poppinsMedium.className} text-[1rem]`}>7. Taxes</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You are responsible for paying any taxes, including any goods and services or value added taxes, which may be applicable depending on the jurisdiction of the services provided.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Depending on your residency or location, you may be subject to certain ad valorem or other taxes on certain fees that we charge. These taxes will be added to fees billed to you, if applicable.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You acknowledge that you must comply with your obligations under income tax provisions in your jurisdiction.</p>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>8. Payment Administration Agent</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You acknowledge and agree that we may in our sole discretion, from time to time, appoint our related bodies corporate, affiliates, or any other third party to act as our agent to accept or make payments (including merchant facilities) from or to Users on our behalf.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Such affiliates may be communicated from time to time in this User Agreement.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Such a third party will have the same rights, powers and privileges that we have under this User Agreement and will be entitled to exercise or enforce their rights, powers and privileges as our agent or in their own name. In no event shall we be liable to any User for any loss, damage or liability resulting from the Payment Administration Agent's negligence and/or acts beyond the authority given by Pennytots.</p>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>9. Promotion</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We may display your company or business name, logo, images or other media as part of the Pennytots Services and/or other marketing materials relating to the Platform, except where you have explicitly requested that we do not do this and we have agreed to such a request in writing.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You acknowledge that we may use the public description of your advertisements and the content of your profile information on the Platform for marketing and other related purposes.</p>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>10. Content</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>When you give us content, you grant us a worldwide, perpetual, irrevocable, royalty-free, sublicensable (through multiple tiers) right to exercise any and all copyright, trademark, publicity, and database rights (but no other rights) you have in the content, in any media known now or in the future.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You acknowledge and agree that: (1) we act only as a forum for the online distribution and publication of User content. We make no warranty that User content is made available on the Platform. We have the right (but not the obligation) to take any action deemed appropriate by us with respect to your User content; (2) we have no responsibility or liability for the deletion or failure to store any content, whether or not the content was actually made available on the Platform; and (3) any and all content submitted to the Platform is subject to our approval. We may reject, approve or modify your User content at our sole discretion.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You represent and warrant that your content:</p>
  <ul className="list-disc pl-5">
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not infringe upon or misappropriate any copyright, patent, trademark, trade secret, or other intellectual property right or proprietary right or right of publicity or privacy of any person;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not violate any law or regulation;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not be defamatory or trade libellous;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not be obscene or contain child pornography;</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not contain the development, design, manufacture or production of missiles, or nuclear, chemical or biological weapons</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not contain material linked to terrorist activities</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not include incomplete, false or inaccurate information about User or any other individual; and</li>
    <li className={`${poppinsRegular.className} text-[0.875rem]`}>will not contain any viruses or other computer programming routines that are intended to damage, detrimentally interfere with, surreptitiously intercept or expropriate any system, data or personal information.</li>
  </ul>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>You acknowledge and agree that we may transfer your personal information to a related body corporate and your information may be transferred outside of any country or other applicable jurisdiction. If you wish to withdraw your consent, you acknowledge and agree that we may be unable to provide you with access to the Platform and Pennytots Services and may close your Account.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>Information on the Platform may contain general information about legal, financial, health and other matters. The information is not advice, and should not be treated as such. You must not rely on the information on the Platform as an alternative to professional advice. If you have specific questions about any matter you should consult your professional adviser.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We provide unmonitored access to third party content, including User feedback and articles with original content and opinions (or links to such third party content). We only act as a portal and have no liability based on, or related to, third party content on the Platform, whether arising under the laws of copyright or other intellectual property, defamation, libel, privacy, obscenity, or any other legal discipline.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>The Platform may contain links to other third-party websites. We do not control the websites to which we link from the Platform. We do not endorse the content, products, services, practices, policies or performance of the websites we link to from the Platform. Use of third party content, links to third party content and/or websites is at your risk.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>In relation to deletion or hiding of any information or content, using the Platform to delete, hide or otherwise dispose of information does not imply permanent deletion of content or information. Information may be retained for a period of time to fulfil record keeping, regulatory, compliance, statistical, law enforcement and other obligations.</p>
</div>

  <h2 className={`${poppinsMedium.className} text-[1rem]`}>20. Inactive Accounts</h2>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>User Accounts that have not been logged into for a period of time will incur a maintenance fee per month, until either the account is closed or reactivated, for storage, bandwidth, support and management costs of providing hosting of the User's profile, portfolio storage, listing in directories, promotion of your profile on the Platform and elsewhere, provision of service, file storage, message transmission, general administrative matters and message and other storage costs.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>The length of the period and the amount of the maintenance fee is set out in our schedule of Fees and Charges.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We reserve the right to close an Inactive Account.</p>
  <p className={`${poppinsRegular.className} text-[0.875rem]`}>We reserve the right to close an account with nil or negative funds.</p>
</div>
  );
};

export default UserTerms;

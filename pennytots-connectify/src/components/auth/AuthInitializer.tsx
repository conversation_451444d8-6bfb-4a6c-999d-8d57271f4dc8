'use client';

import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { userToken, IsLoggedIn } from '../../redux/user/reducer';
import { Axios } from '../../api/axios';

/**
 * AuthInitializer component that sets up authentication headers
 * when the app loads and user is authenticated
 */
export function AuthInitializer() {
  const token = useSelector(userToken);
  const isLoggedIn = useSelector(IsLoggedIn);

  useEffect(() => {
    // Set up Axios authorization header when user is logged in and token exists
    if (isLoggedIn && token) {
      Axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      // Clear authorization header if user is not logged in
      delete Axios.defaults.headers.common['Authorization'];
    }
  }, [isLoggedIn, token]);

  // This component doesn't render anything
  return null;
}

'use client';

import React from 'react';
import { getFileTypeInfo, formatFileSize } from '@/lib/file-utils';
import { Badge } from '@/components/ui/badge';

interface FileTagProps {
  filename: string;
  fileSize?: number;
  showSize?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
}

export function FileTag({ 
  filename, 
  fileSize, 
  showSize = false, 
  variant = 'default',
  className = '' 
}: FileTagProps) {
  const fileInfo = getFileTypeInfo(filename);
  
  return (
    <Badge
      variant={variant}
      className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${className}`}
      style={{
        color: fileInfo.color,
        backgroundColor: fileInfo.bgColor,
        border: `1px solid ${fileInfo.color}20`
      }}
    >
      <span className="font-semibold">{fileInfo.extension}</span>
      {showSize && fileSize && (
        <>
          <span className="text-gray-400">•</span>
          <span>{formatFileSize(fileSize)}</span>
        </>
      )}
    </Badge>
  );
}

interface FileDisplayProps {
  file: File;
  onRemove?: () => void;
  showPreview?: boolean;
  className?: string;
}

export function FileDisplay({ 
  file, 
  onRemove, 
  showPreview = false,
  className = '' 
}: FileDisplayProps) {
  const fileInfo = getFileTypeInfo(file.name);
  
  return (
    <div className={`flex items-center gap-3 p-3 bg-gray-50 rounded-lg border ${className}`}>
      {/* File Icon/Preview */}
      <div 
        className="w-10 h-10 rounded-lg flex items-center justify-center text-white font-bold text-sm"
        style={{ backgroundColor: fileInfo.color }}
      >
        {fileInfo.extension}
      </div>
      
      {/* File Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <p className="text-sm font-medium text-gray-900 truncate">
            {file.name}
          </p>
          <FileTag filename={file.name} />
        </div>
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <span>{fileInfo.category}</span>
          <span>•</span>
          <span>{formatFileSize(file.size)}</span>
        </div>
      </div>
      
      {/* Actions */}
      <div className="flex items-center gap-2">
        {showPreview && fileInfo.type === 'image' && (
          <button
            type="button"
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            Preview
          </button>
        )}
        {onRemove && (
          <button
            type="button"
            onClick={onRemove}
            className="text-xs text-red-600 hover:text-red-800"
          >
            Remove
          </button>
        )}
      </div>
    </div>
  );
}

// Compact version for inline display
export function CompactFileDisplay({ 
  file, 
  onRemove,
  className = '' 
}: {
  file: File;
  onRemove?: () => void;
  className?: string;
}) {
  const fileInfo = getFileTypeInfo(file.name);
  
  return (
    <div className={`inline-flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-lg border ${className}`}>
      {/* File Type Badge */}
      <div 
        className="w-6 h-6 rounded flex items-center justify-center text-white font-bold text-xs"
        style={{ backgroundColor: fileInfo.color }}
      >
        {fileInfo.extension.charAt(0)}
      </div>
      
      {/* File Name */}
      <span className="text-sm text-gray-700 max-w-[200px] truncate">
        {file.name}
      </span>
      
      {/* File Tag */}
      <FileTag filename={file.name} fileSize={file.size} showSize />
      
      {/* Remove Button */}
      {onRemove && (
        <button
          type="button"
          onClick={onRemove}
          className="ml-1 text-gray-400 hover:text-red-500 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from './button';
import { poppinsMedium, poppinsRegular } from '@/fonts';
import { ArrowLeft, Check } from 'lucide-react';
import { useBuySubscription } from '@/redux/credit/hooks';
import { useQueryClient } from '@tanstack/react-query';
import { SUBSCRIPTION_PLANS } from '@/api/credit';

interface SponsorshipPlansModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan?: string;
  closeModal?: () => void;
}

interface PlanOption {
  id: string;
  name: string;
  description: string;
  price: string;
  duration: string;
  recognition: string;
  planId: string;
}

// Convert API plans to component format
const SPONSORSHIP_PLANS: PlanOption[] = Object.values(SUBSCRIPTION_PLANS).map((plan, index) => ({
  id: (index + 1).toString(),
  name: plan.name,
  description: plan.description,
  price: `$${plan.price}`,
  duration: `${plan.duration} days`,
  recognition: `${plan.recognition}-day public recognition`,
  planId: plan.id
}));

export const SponsorshipPlansModal: React.FC<SponsorshipPlansModalProps> = ({
  isOpen,
  onClose,
  currentPlan,
  closeModal
}) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const { mutateAsync: buySubscription } = useBuySubscription();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (isOpen) {
      setSelectedPlan(null);
      setError(null);
      setSuccess(null);
    }
  }, [isOpen]);

  const isPlanDisabled = (planId: string) => {
    return currentPlan?.toLowerCase() === planId.toLowerCase();
  };

  const handlePlanSelect = (planId: string) => {
    if (isPlanDisabled(planId)) {
      setError('You are already subscribed to this plan. Please select a different plan to upgrade or downgrade.');
      return;
    }
    setSelectedPlan(planId);
    setError(null);
  };

  const handlePurchase = async () => {
    if (!selectedPlan) {
      setError('Please select a sponsorship plan');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const payload = { subscriptionType: selectedPlan };
      await buySubscription(payload);
      await queryClient.invalidateQueries({ queryKey: ['subscription'] });
      
      setSuccess('Subscription purchased successfully! Your plan will be activated shortly.');
      
      // Close modal after success
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error: any) {
      console.error('Purchase failed:', error);
      setError(error.response?.data?.message || 'Failed to purchase subscription. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className=""
      onClick={onClose}
    >
      <div
        className=""
        onClick={(e) => e.stopPropagation()}
      >
    

        {/* Current Plan Info */}
        {currentPlan && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className={`${poppinsRegular.className} text-[0.875rem] text-green-700`}>
              <strong>Current Plan:</strong> {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)} Sponsorship
            </p>
          </div>
        )}

        {/* Description */}
        <div className="mb-6">
          <p className={`${poppinsRegular.className} text-[0.875rem] text-[#5A5E5C] mb-2`}>
            Sponsors receive recognition for the duration of each plan
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-4 p-3  border border-green-300 text-green-700 rounded-md text-sm">
            {success}
          </div>
        )}

        {/* Plans List */}
        <div className="space-y-4 mb-6">
          {SPONSORSHIP_PLANS.map((plan) => {
            const isDisabled = isPlanDisabled(plan.planId);
            const isSelected = selectedPlan === plan.planId;
            
            return (
              <div
                key={plan.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  isSelected 
                    ? 'border-[#F2875D] bg-green-50' 
                    : isDisabled 
                      ? 'border-gray-200 bg-gray-50 opacity-60 cursor-not-allowed'
                      : 'border-[#F0F2F5] hover:border-[#749B6C] hover:bg-gray-50'
                }`}
                onClick={() => !isDisabled && handlePlanSelect(plan.planId)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    {/* Custom Checkbox */}
                    <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mt-1 ${
                      isSelected 
                        ? 'bg-[#F2875D] border-[#F2875D]' 
                        : 'border-gray-300'
                    }`}>
                      {isSelected && <Check className="w-3 h-3 text-white" />}
                    </div>
                    
                    <div className="flex-1">
                      <h3 className={`${poppinsMedium.className} text-[1rem] text-[#5A5E5C] mb-1`}>
                        {plan.name}
                      </h3>
                      <p className={`${poppinsRegular.className} text-[0.875rem] text-[#797978] mb-2`}>
                        {plan.description}
                      </p>
                      {isDisabled && (
                        <span className={`${poppinsRegular.className} text-[0.75rem] text-green-600 font-medium`}>
                          Current Plan
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className={`${poppinsMedium.className} text-[1rem] text-[#5A5E5C]`}>
                      {plan.price}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
   
          <Button
            variant="primary"
            onClick={handlePurchase}
            className="flex-1"
            disabled={loading || !selectedPlan}
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Processing...
              </div>
            ) : (
              'Create'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from './button';
import { X, ArrowLeft, Download } from 'lucide-react';
import { poppinsSemibold } from '@/fonts';

interface PreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: File | null;
  attachmentType: string;
  onConfirm?: () => void;
}

export const PreviewModal: React.FC<PreviewModalProps> = ({ 
  isOpen, 
  onClose, 
  file, 
  attachmentType,
  onConfirm 
}) => {
  const [fileUrl, setFileUrl] = useState<string>('');
  const [aspectRatio, setAspectRatio] = useState<number>(16 / 9);

  // Prevent page scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Create object URL for file preview
  useEffect(() => {
    if (file) {
      const url = URL.createObjectURL(file);
      setFileUrl(url);

      // Cleanup function to revoke object URL
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [file]);

  const handleDownload = () => {
    if (file && fileUrl) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget;
    if (img.naturalWidth && img.naturalHeight) {
      setAspectRatio(img.naturalWidth / img.naturalHeight);
    }
  };

  if (!isOpen || !file) {
    return null;
  }

  const renderPreviewContent = () => {
    switch (attachmentType) {
      case 'image':
        return (
          <div className="flex-1 flex items-center justify-center ">
            <img
              src={fileUrl}
              alt="Preview"
              className="max-w-full max-h-full object-contain"
              style={{ aspectRatio }}
              onLoad={handleImageLoad}
            />
          </div>
        );

      case 'video':
        return (
          <div className="flex-1 flex items-center justify-center ">
            <video
              src={fileUrl}
              controls
              className="max-w-full max-h-full"
              style={{ aspectRatio }}
              onLoadedMetadata={(e) => {
                const video = e.currentTarget;
                if (video.videoWidth && video.videoHeight) {
                  setAspectRatio(video.videoWidth / video.videoHeight);
                }
              }}
            >
              Your browser does not support the video tag.
            </video>
          </div>
        );

      case 'audio':
        return (
          <div className="flex-1 flex flex-col items-center justify-center p-8">
            <div className="text-6xl mb-8 text-gray-400">🎵</div>
            <h3 className="text-lg font-medium mb-4 text-center">{file.name}</h3>
            <audio
              src={fileUrl}
              controls
              className="w-full max-w-md"
            >
              Your browser does not support the audio tag.
            </audio>
          </div>
        );

      case 'document':
        return (
          <div className="flex-1 flex flex-col items-center justify-center p-8">
            <div className="text-6xl mb-8 text-gray-400">📄</div>
            <h3 className="text-lg font-medium mb-2 text-center">{file.name}</h3>
            <p className="text-sm text-gray-600 mb-4">
              Size: {(file.size / 1024 / 1024).toFixed(2)} MB
            </p>
            <p className="text-sm text-gray-500 text-center">
              Document preview not available. You can download the file to view it.
            </p>
          </div>
        );

      default:
        return (
          <div className="flex-1 flex items-center justify-center p-8">
            <p className="text-gray-500">Preview not available for this file type.</p>
          </div>
        );
    }
  };

  return (
    <div 
      className={`fixed inset-0 bg-black bg-opacity-90 flex flex-col z-50 transition-opacity duration-300 ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between  bg-black bg-opacity-50">
        <div className="flex items-center ga">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white hover:bg-opacity-20"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className={`${poppinsSemibold.className} text-white text-lg`}>
            Preview
          </h2>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDownload}
            className="text-white hover:bg-white hover:bg-opacity-20"
          >
            <Download className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white hover:bg-opacity-20"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 flex items-center justify-center">
        {renderPreviewContent()}
      </div>

      {/* Footer Actions */}
      <div className="flex items-center justify-center ga p-6 bg-black bg-opacity-50">
        <Button
          variant="outline"
          onClick={onClose}
          className="bg-white bg-opacity-20 text-white border-white border-opacity-30 hover:bg-opacity-30"
        >
          Back
        </Button>
        {onConfirm && (
          <Button
            onClick={() => {
              onConfirm();
              onClose();
            }}
            className="bg-[#F2875D] hover:bg-[#E07A52] text-white"
          >
            Use This File
          </Button>
        )}
      </div>
    </div>
  );
};

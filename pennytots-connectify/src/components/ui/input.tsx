import * as React from "react"
import { cn } from "@/lib/utils"
import { poppinsRegular } from "@/fonts"

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          `rounded-[6px] border-[#D0D5DD] border p-[1rem] w-full focus:outline-none focus:ring-1 focus:ring-[#F2875D] placeholder:text-[#98A2B3] text-[14px] ${poppinsRegular.className}`,
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }

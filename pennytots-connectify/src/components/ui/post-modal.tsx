'use client';

import React, { useState, useEffect } from 'react';
import { Button } from './button';
import { SelectInterests } from '@/components/elements/SelectInterests';
import { Textarea } from './textarea';
import { poppinsMedium } from '@/fonts';
import { useQueryClient } from '@tanstack/react-query';
import { PreviewModal } from './preview-modal';
import FloatingAddSVG from '@/assets/svg/floating-add.svg';
import CloseSVG from '@/assets/svg/close-button.svg'
import CameraIcon from '@/assets/svg/camera.svg'
import DocumentIcon from '@/assets/svg/document.svg'
import AudioIcon from '@/assets/svg/audio.svg'
import GalleryIcon from '@/assets/svg/gallery.svg'
import { ArrowLeft, X } from 'lucide-react';
import { useCreateTopic } from '@/redux/topic/hooks';
import { useRouter } from 'next/navigation';
import CreateHelpDeskTicket from '../screens/create-helpdesk-ticket';
import AreaOfInterest from '../screens/area-of-interest';
import ChangePin from '../settings/change-pin';
import BlockedChat from '@/components/settings/blocked-chats'
import DeleteAccount from '../settings/delete-account';
import { MembershipScreen } from '../settings/membership-screen';
import { SponsorshipPlansModal } from './sponsorship-plans-modal';

interface PostModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'post' | 'helpdesk' | 'area-of-interest' | 'change-pin' | 'blocked-chat'  | 'delete-account' | 'sponsoredship-plan' ;
}

export const PostModal: React.FC<PostModalProps> = ({ isOpen, onClose, mode }) => {
  const [content, setContent] = useState('');

  const [interest, setInterest] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [attachmentType, setAttachmentType] = useState<string>('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [processingFile, setProcessingFile] = useState(false);
  const [showMediaOptions, setShowMediaOptions] = useState(false);
  const isPostMode = mode === 'post';
  const isHelpdeskMode = mode === 'helpdesk';
  const isAreaofInterest = mode === 'area-of-interest';
  const isChangePin = mode === 'change-pin';
  const isBlockedChat = mode === 'blocked-chat';
   const isDeleteAccount = mode === 'delete-account';
  const isSponsorshipPlanMode = mode === 'sponsoredship-plan';
  const [showPreview, setShowPreview] = useState(false);

  const createTopicMutation = useCreateTopic();
  const queryClient = useQueryClient();
  const router = useRouter();
  console.log(content, 'content')
  const handleClose = () => {
    setShowMediaOptions(false);
    setShowPreview(false);
    setError('');
    onClose();
  };

  const handlePreview = () => {
    if (selectedFile) {
      setShowPreview(true);
    }
  };

  // Prevent page scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProcessingFile(true);

      // Automatically determine attachment type based on file type
      let detectedType: string;
      if (file.type.startsWith('image/')) {
        detectedType = 'image';
      } else if (file.type.startsWith('video/')) {
        detectedType = 'video';
      } else if (file.type.startsWith('audio/')) {
        detectedType = 'audio';
      } else {
        detectedType = 'document';
      }

      setAttachmentType(detectedType);
      setSelectedFile(file);
      setProcessingFile(false);
    }
  };

  const openShareAttachment = () => {
    // Create a file input element for any file type
    const input = document.createElement('input');
    input.type = 'file';

    // Accept all common file types for the single "Attach file" button
    input.accept = 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.rtf,.zip,.rar';

    input.onchange = (e) => handleFileSelect(e as any);
    input.click();
  };

  const handleSubmitPost = async () => {
    // Validation logic from Expo repo
    if (interest === "" || !interest) {
      setError("You need to select a tag for this post");
      return;
    }

    if (!content || !content.trim()) {
      setError("Don't forget to add a message");
      return;
    }

    if (processingFile) {
      setError("File is processing please wait");
      return;
    }

    setLoading(true);

    const topicData = new FormData();

    // Form data creation logic from Expo repo
    if (selectedFile) {
      topicData.append("content", content);
      topicData.append("tags", JSON.stringify([interest]));
      topicData.append("attachmentType", attachmentType);

      // Use the original filename or a default based on type
      const fileName = selectedFile.name || `file.${attachmentType === 'video' ? 'mp4' : 'jpg'}`;
      topicData.append("file", selectedFile, fileName);
    } else {
      topicData.append("content", content);
      topicData.append("tags", JSON.stringify([interest]));
    }

    try {
      const response = await createTopicMutation.mutateAsync(topicData);


      console.log("topicData", topicData);
      console.log("Created topic response:", response);

      // Reset form and close modal
      setContent("");
      setSelectedFile(null);
      setInterest("");
      setAttachmentType("");
      setError("");
      setShowMediaOptions(false);

      // Invalidate queries to refresh topics list
      queryClient.invalidateQueries({ queryKey: ["topics"] });
      queryClient.invalidateQueries({ queryKey: ["topics-all"] });
      queryClient.invalidateQueries({ queryKey: ["topic"] });

      handleClose();

      // Navigate to the home page to show the new post in the topic list
      router.push('/home');
    } catch (error: any) {
      console.log('Full error object:', error);
      console.log('Error data:', error?.data);

      // Handle error response structure to match Expo implementation
      let errorMessage = error?.data
        ? error.data.message
        : "An error occurred";

      // Handle specific subscription errors
      if (
        errorMessage === "Your subscription has expired" ||
        errorMessage === "Your subscription was not found"
      ) {
        errorMessage = "Your subscription has expired. Please renew to continue posting.";
      }

      // Handle network errors specifically
      if (errorMessage === "Network Error" || errorMessage === "Network error") {
        errorMessage = "Error creating a topic: Network error";
      }

      console.log('Final error message:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
    setAttachmentType('');
    setShowMediaOptions(false);
  };

  return (
    <div
      className={`fixed inset-0 bg-[#15191614] bg-opacity-80 flex items-center justify-center z-50 transition-opacity duration-300 ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}

    >
      <div
        className={`bg-white  px-[24px] py-[2rem] w-full max-w-[509px] overflow-y-auto shadow-2xl transform transition-all duration-300 ${isOpen ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'
          }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between ">
          <div className="flex items-center gap-[1rem]">
            <ArrowLeft color='#5A5E5C' onClick={handleClose}/>
            <h2 className={`${poppinsMedium.className} text-[0.875rem] text-[#5A5E5C]`}>
              {mode === 'post' ? 'Post' : mode === 'helpdesk' ? 'Helpdesk' : mode === 'area-of-interest' ? 'Interests' : mode === 'change-pin' ? 'Change PIN' : mode === 'blocked-chat' ? 'Blocked Chat' : mode === 'sponsoredship-plan' ?'Sponsorship Plan' : 'Delete Account' }
            </h2>
          </div>
   
        </div>

        {/* Content */}
        <div className="px-[2rem] mt-[24px] ">
          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-100 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}
          {isPostMode && (

            <div>
              {/* Content Input */}
              <div className="">


                <Textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Type your message here"
                  className="px-[22px] pt-[17px] pb-[103px]"
                  disabled={loading || processingFile}
                />
              </div>


              {selectedFile && (
                <div className='mt-[1rem] py-[12px] px-[1rem]'>
                  <div className="flex relative items-center gap-[14px]">
                    {attachmentType === 'image' && <GalleryIcon />}
                    {attachmentType === 'video' && <CameraIcon />}
                    {attachmentType === 'audio' && <AudioIcon />}
                    {attachmentType === 'document' && <DocumentIcon />}
                    <span className={` text-[#5A5E5C] text-[12px] ${poppinsMedium.className}`}>{selectedFile.name}</span>
                    <Button

                      onClick={removeFile}
                      className="absolute right-0 bottom-[18px]"
                      disabled={loading}
                    >
                      <CloseSVG />
                    </Button>
                  </div>
                </div>
              )}





              {/* File Upload */}
              <div className="mt-[1rem] flex items-center">
                {/* Single Attach File Button */}
                <Button
                  type="button"
                  // variant="outline"
                  onClick={openShareAttachment}
                  disabled={loading || processingFile}
                  className="w-[73px] items-center flex flex-col gap-[8px]"
                >
                  <FloatingAddSVG />
                  <span className={`${poppinsMedium.className} text-[#B5B5B5] text-[12px]`}>Attach file</span>
                </Button>

                {processingFile && (
                  <div className="flex items-center gap-2 text-sm text-blue-600 ml-4">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    Processing file...
                  </div>
                )}
              </div>

              {/* Interest Selection */}
              <div className="space-y-2 mt-[1rem]">
                <label className={`${poppinsMedium.className} text-[#5A5E5C] text-[14px] mb-[5px]`}>
                  I would like this post to reach
                </label>
                <SelectInterests
                  interest={interest}
                  setInterest={setInterest}
                  multiple={false}
                />
              </div>
              {selectedFile && (
                <div className='mt-[1rem]'>
                  <Button
                    variant="tertiary"

                    onClick={handlePreview}
                    disabled={loading || processingFile}

                  >
                    Preview
                  </Button>
                </div>
              )}
             <div className="pt-[1.5rem]">
        
                    <Button
                      onClick={handleSubmitPost}
                      disabled={loading}
                      variant="primary"
                      className="justify-center w-full flex"
                    >
                      <span className="flex items-center text-white ">
                        {loading && (
                          <svg
                            className="animate-spin h-4 w-4"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                            />
                          </svg>
                        )}
                        {loading ? 'Posting...' : 'Post'}
                      </span>
                    </Button>
        
                  </div>
            </div>
          )}
          {isHelpdeskMode && (
            <div>
            <CreateHelpDeskTicket closeModal={handleClose} />
                        </div>
          )}
          {isAreaofInterest && (
            <div className="w-full">
              <AreaOfInterest closeModal={handleClose}   />
              </div>
          )}
             {isChangePin && (
            <div className="w-full">
              <ChangePin closeModal={handleClose}  />
              </div>
          )}
          {isBlockedChat && (
            <div className="w-full">
              <BlockedChat closeModal={handleClose}  />
              </div>
          )}

             {isDeleteAccount && (
            <div className="w-full">
              <DeleteAccount closeModal={handleClose}  />
              </div>
          )}

          {isSponsorshipPlanMode && (
            <div className="w-full">
              <SponsorshipPlansModal   isOpen={true} 
      onClose={handleClose} 
      closeModal={handleClose}   />
              </div>
          )}


        </div>

      </div>

      {/* Preview Modal */}
      <PreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        file={selectedFile}
        attachmentType={attachmentType}
        onConfirm={() => {
          // Optional: You can add any confirmation logic here
          setShowPreview(false);
        }}
      />
    </div>
  );
};

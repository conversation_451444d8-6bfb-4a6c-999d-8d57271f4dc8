'use client';

import React from 'react';
import { useActivity } from '@/providers/ActivityContext';
import { Button } from './button';

export const ActivityDebug: React.FC = () => {
  const { activityCounts, incrementActivity, modalVisible, sponsoredAd, isAdLoading } = useActivity();

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-xs">
      <h3 className="font-bold text-sm mb-2">Activity Debug</h3>
      
      <div className="space-y-2 text-xs">
        <div>
          <strong>Sponsor Count:</strong> {activityCounts.sponsor}/7
        </div>
        <div>
          <strong>Share Count:</strong> {activityCounts.share}/5
        </div>
        <div>
          <strong>Modal Visible:</strong> {modalVisible.sponsor ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>Ad Loading:</strong> {isAdLoading ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>Has Ad:</strong> {sponsoredAd ? 'Yes' : 'No'}
        </div>
      </div>

      <div className="flex gap-2 mt-3">
        <Button
          size="sm"
          onClick={() => incrementActivity('sponsor')}
          className="text-xs px-2 py-1"
        >
          +Sponsor
        </Button>
        <Button
          size="sm"
          onClick={() => incrementActivity('share')}
          className="text-xs px-2 py-1"
        >
          +Share
        </Button>
      </div>
    </div>
  );
};

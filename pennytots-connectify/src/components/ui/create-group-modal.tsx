'use client';

import React, { useState } from 'react';
import { Upload, Image as ImageIcon, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Modal } from '@/components/ui/modal';
import { useCreateGroup } from '@/redux/group/hooks';
import { group } from '@/api/group';
import { useQuery } from '@tanstack/react-query';
import { poppinsSemibold, poppinsMedium } from '@/fonts';

interface CreateGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateGroupModal({ isOpen, onClose }: CreateGroupModalProps) {
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [showMediaOptions, setShowMediaOptions] = useState(false);
  const [error, setError] = useState('');

  const createGroupMutation = useCreateGroup();
  const { data: tags } = useQuery({
    queryKey: ['group-tags'],
    queryFn: group.getTags,
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      setShowMediaOptions(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validation
    if (!groupName.trim()) {
      setError('Group name is required');
      return;
    }
    if (!groupDescription.trim()) {
      setError('Group description is required');
      return;
    }
    if (!selectedTag) {
      setError('Please select a tag');
      return;
    }

    // Create form data
    const formData = new FormData();
    formData.append('name', groupName.trim());
    formData.append('description', groupDescription.trim());
    formData.append('tag', selectedTag);
    
    if (selectedFile) {
      formData.append('file', selectedFile);
    }

    try {
      await createGroupMutation.mutateAsync(formData);
      
      // Reset form and close modal
      setGroupName('');
      setGroupDescription('');
      setSelectedTag('');
      setSelectedFile(null);
      setPreviewUrl(null);
      setError('');
      onClose();
    } catch (error: any) {
      setError(error?.message || 'Failed to create group');
    }
  };

  const handleClose = () => {
    setGroupName('');
    setGroupDescription('');
    setSelectedTag('');
    setSelectedFile(null);
    setPreviewUrl(null);
    setError('');
    setShowMediaOptions(false);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Create Group"
      size="md"
    >
      <form onSubmit={handleSubmit} className=" space-y-4">
          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {/* Group Image */}
          <div className="space-y-2">
            <label className={`${poppinsMedium.className} text-sm text-[#5A5E5C]`}>
              Group Image (Optional)
            </label>
            
            {previewUrl ? (
              <div className="relative">
                <img
                  src={previewUrl}
                  alt="Group preview"
                  className="w-full h-32 object-cover rounded-lg"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedFile(null);
                    setPreviewUrl(null);
                  }}
                  className="absolute top-2 right-2 p-1 bg-white rounded-full"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowMediaOptions(!showMediaOptions)}
                  className="w-full flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  Add Group Image
                </Button>
                
                {showMediaOptions && (
                  <div className="flex gap-2">
                    <label className="flex-1">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="w-full flex items-center gap-2"
                        onClick={() => (document.querySelector('input[type="file"]') as HTMLInputElement)?.click()}
                      >
                        <ImageIcon className="h-4 w-4" />
                        Gallery
                      </Button>
                    </label>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Group Name */}
          <div className="space-y-2">
            <label className={`${poppinsMedium.className} text-sm text-[#5A5E5C]`}>
              Group Name *
            </label>
            <Input
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="Enter group name"
              maxLength={50}
            />
          </div>

          {/* Group Description */}
          <div className="space-y-2">
            <label className={`${poppinsMedium.className} text-sm text-[#5A5E5C]`}>
              Description *
            </label>
            <Textarea
              value={groupDescription}
              onChange={(e) => setGroupDescription(e.target.value)}
              placeholder="Describe your group"
              rows={3}
              maxLength={200}
            />
          </div>

          {/* Tag Selection */}
          <div className="space-y-2">
            <label className={`${poppinsMedium.className} text-sm text-[#5A5E5C]`}>
              Category *
            </label>
            <Select value={selectedTag} onValueChange={setSelectedTag}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {tags?.data?.map((tag: any) => (
                  <SelectItem key={tag._id} value={tag._id}>
                    {tag.name}
                  </SelectItem>
                )) || [
                  <SelectItem key="general" value="general">General</SelectItem>,
                  <SelectItem key="tech" value="tech">Technology</SelectItem>,
                  <SelectItem key="sports" value="sports">Sports</SelectItem>,
                  <SelectItem key="entertainment" value="entertainment">Entertainment</SelectItem>
                ]}
              </SelectContent>
            </Select>
          </div>

          {/* Submit Button */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createGroupMutation.isPending}
              className="flex-1 bg-[#163E23] hover:bg-[#163E23]/90"
            >
              {createGroupMutation.isPending ? 'Creating...' : 'Create Group'}
            </Button>
          </div>
        </form>
    </Modal>
  );
}

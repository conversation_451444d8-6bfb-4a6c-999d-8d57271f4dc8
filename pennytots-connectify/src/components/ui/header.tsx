'use client';

import React, { useState } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { useRouter } from 'next/navigation';
import { useGetUnreadNotifications } from '@/redux/main/hooks';
import { poppinsSemibold, poppins, poppinsRegular } from '@/fonts';
import Image from 'next/image';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';

interface HeaderProps {
  title?: string;
  showNotification?: boolean;
  activeTab?: 'posts' | 'convos' | 'profile';
  onTabChange?: (tab: 'posts' | 'convos' | 'profile') => void;
  showTabs?: boolean;
  // Add these new props for dynamic tab labels
  tabLabels?: {
    posts?: string;
    convos?: string;
    profile?: string;
  };
}

export function Header({
  title = "Connectify",
  showNotification = true,
  activeTab = 'posts',
  onTabChange,
  showTabs = false,
  tabLabels = {
    posts: 'Posts',
    convos: 'Convos',
    profile: 'Profile'  
  }
}: HeaderProps) {
  const router = useRouter();
  const user = useSelector(userAuthInfo);
  console.log(user, 'user')
  const { data: notifications } = useGetUnreadNotifications(true);

  const unreadCount = notifications?.convos?.length || 0;

  const handleNotificationClick = () => {
    router.push('/notifications');
  };

  // Helper to get user image src
 
  return (
    <div className="bg-white py-3 justify-center flex" >
      <div className="flex items-center w-full max-w-[1384px]  justify-between ">
        {/* Left side - Logo */}
        <div className=''>
          <Image
            alt="connectify logo"
            src="/connectify-logo.svg"
            width={210}
            height={56}
          />
        </div>

        {/* Center - Tab Navigation (only show on home screen) */}
        {showTabs && onTabChange && (
          <div className="flex">
            <Button
              variant="ghost"
              onClick={() => onTabChange('posts')}
              className={`py-2 px-4 rounded-none border-b-2 cursor-pointer transition-colors ${
                activeTab === 'posts'
                  ? 'border-[#F56630]   text-[#F56630]'
                  : 'border-transparent text-[#797C7B] hover:bg-gray-50'
              }`}
            >
              <span className={`${activeTab === 'posts' ? poppinsRegular.className : poppins.className} text-[1rem]`}>
                {tabLabels.posts}
              </span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => onTabChange('convos')}
              className={`py-2 px-4 rounded-none border-b-2 cursor-pointer transition-colors ${
                activeTab === 'convos'
                  ? 'border-[#F56630]   text-[#F56630]'
                  : 'border-transparent text-[#797C7B] hover:bg-gray-50'
              }`}
            >
              <span className={`${activeTab === 'convos' ? poppinsRegular.className : poppins.className} text-[1rem]`}>
                {tabLabels.convos}
              </span>
            </Button>
          </div>
        )}

        {/* Right side - Notifications and User */}
        <div className='flex items-center'>
          {showNotification && (
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleNotificationClick}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <Bell className="h-6 w-6 text-[#5A5E5C]" />
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-[#FE3233] hover:bg-[#FE3233]"
                  >
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Badge>
                )}
              </Button>
            </div>
          )}
          <div className='flex items-center gap-2'>
            {/* User Avatar with Fallback */}
            <Avatar
              size={38}
              type="user"
              headerImage={user?.header_image}
              profilePicture={user?.thumbnail}
              customDefault="thumbnail" 
            />

            {/* User Name */}
            {user && (
              <div className="flex items-center gap-1">
                <span className={`${poppinsSemibold.className} text-[#5A5E5C] text-sm`}>
                  {user.first_name}
                </span>
                <span className={`${poppinsSemibold.className} text-[#5A5E5C] text-sm`}>
                  {user.last_name}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

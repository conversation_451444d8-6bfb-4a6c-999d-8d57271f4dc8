'use client';

import React, { useState } from 'react';
import { X, Send, ArrowLeft } from 'lucide-react';
import { Button } from './button';
import { Textarea } from './textarea';
import { Loading } from './loading';
import { Avatar } from './avatar';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import { timeAgo } from '@/lib/time-utils';
import EmojiPicker from './emoji-picker';
import { useSelector } from 'react-redux';
import { userId, userAuthInfo } from '@/redux/user/reducer';
import { useRouter } from 'next/navigation';
import SelectAttachment from '@/components/ui/select-attachment';
import { user } from '@/api/user';


interface Comment {
  _id: string;
  comment: string;
  userId: {
    _id: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
    company?: string;
    company_position?: string;
    subscriptionType?: string;
  };
  createdAt: string;
  subComments?: Comment[];
  likes?: number;
  isLiked?: boolean;
  subCommentsCount?: number;
  image?: string;
  document?: string;
  audio?: string;
  video?: string;
  attachmentSize?: string;
}

interface ReplyModalProps {
  isOpen: boolean;
  onClose: () => void;
  comment: Comment;
  onSubmit: (replyText: string) => Promise<void>;
}

export const ReplyModal: React.FC<ReplyModalProps> = ({
  isOpen,
  onClose,
  comment,
  onSubmit
}) => {
  const [replyText, setReplyText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentUser = useSelector(userAuthInfo);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [attachmentType, setAttachmentType] = useState<string>('');
  const router = useRouter();

  const handleSubmit = async () => {
    if (!replyText.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onSubmit(replyText.trim());
      setReplyText('');
      onClose();
    } catch (error) {
      console.error('Error submitting reply:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setReplyText(prev => prev + emoji);
  };
    const handleBack = () => {
    router.back();
  };
   const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-[#15191614] flex items-center justify-center z-50 p-4">
      <div className="bg-white px-[2.5rem] py-[2rem] rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
         <div className="flex items-center gap-[1rem]">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="p-2"
            >
              <ArrowLeft className="h-5 w-5" color='#5A5E5C'/>
            </Button>
            <h1 className={`${poppinsMedium.className} text-[#5A5E5C] text-[1rem] `}>
              Sub-comment
            </h1>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Original Comment */}
        {/* <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex gap-3">
            <div className="w-12 h-12 flex-shrink-0">
              <Avatar
                source={comment.userId.profile_picture}
                size={48}
                type="user"
              />
            </div>
            <div className="flex-1">
              <div className="flex flex-col">
                <h3 className={`text-[#5A5E5C] text-[16px] ${poppinsSemibold.className}`}>
                  {comment.userId.first_name} {comment.userId.last_name}
                </h3>
                <p className={`text-[#696969] text-[12px] ${poppinsMedium.className}`}>
                  {comment.userId.company_position && comment.userId.company
                    ? `${comment.userId.company_position?.replace(/(\r\n|\n|\r)/gm, "") || "position"} at ${comment.userId.company}`
                    : "Position at Company"}
                </p>
                <p className={`text-[#696969] text-[12px] ${poppinsMedium.className}`}>
                  {comment.userId.subscriptionType
                    ? `${comment.userId.subscriptionType.charAt(0).toUpperCase() + comment.userId.subscriptionType.slice(1)} Member`
                    : 'Free Member'}
                </p>
                <p className={`text-[#696969] text-[12px] mt-1 ${poppinsMedium.className}`}>
                  {timeAgo(comment.createdAt)}
                </p>
              </div>
              <div className="mt-2">
                <p className={`text-[#636363] text-[14px] leading-[20px] ${poppinsMedium.className}`}>
                  {comment.comment}
                </p>
              </div>
            </div>
          </div>
        </div> */}

        {/* Current User Reply Section */}
        <div className="p-4">
          <div className="flex gap-[10px] w-full max-w-[446px]">
            <div className="w-12 h-12 flex-shrink-0">
              <Avatar
                profilePicture={currentUser?.profile_picture || currentUser?.thumbnail}
                source={currentUser?.profile_picture || currentUser?.thumbnail}
                size={40}
                type="user"
              />
            </div>
            <div className="flex-1">
              <div className="flex flex-col mb-3">
                <h3 className={`text-[#5A5E5C] text-[1rem] ${poppinsSemibold.className}`}>
                  {currentUser?.first_name} {currentUser?.last_name}
                </h3>
                <p className={`text-[#696969] text-[14px] ${poppinsMedium.className}`}>
                  {currentUser?.company_position && currentUser?.company
                    ? `${currentUser.company_position?.replace(/(\r\n|\n|\r)/gm, "") || "position"} at ${currentUser.company}`
                    : "Position at Company"}
                </p>
                <p className={`text-[#696969] text-[14px] ${poppinsMedium.className}`}>
                  {currentUser?.subscriptionType
                    ? `${currentUser.subscriptionType.charAt(0).toUpperCase() + currentUser.subscriptionType.slice(1)} Member`
                    : 'Free Member'}
                </p>
              </div>

          
            
            </div>
          </div>
            <div className="mt-[1.5rem]">
                <Textarea
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  placeholder="Post your reply"
                  className={`pt-[17px] px-[25px] text-[#B5B5B5] text-[14px] pb-[50px]  rounded-[1rem] border border-[#FCFBF7] ${poppinsMedium.className}`}

                  rows={4}
                />
                </div>
                <div className="flex items-center justify-between mt-[8px] py-[8px]">
                  <div className="  flex items-center gap-[8px]">
                                 <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                                 <SelectAttachment onFileSelect={handleFileSelect}   />
                               </div>
              </div>
              
                   
                    
              <div className="flex mt-[1rem]">
                <Button
                  onClick={handleSubmit}
                  disabled={!replyText.trim() || isSubmitting}
                  variant="primary"
                >
                  
                  {isSubmitting ? 'Posting...' : 'Post Reply'}
                </Button>
              </div>
        </div>
      </div>
    </div>
   
  );
};

export default ReplyModal;

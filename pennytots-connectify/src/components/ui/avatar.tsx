import React, { FunctionComponent, useState } from 'react';
import Image from 'next/image';
import DefaultProfileSVG from '@/assets/svg/connectify-default-thumbnail.svg';
import DefaultGroupSVG from '@/assets/svg/connectify-default-thumbnail.svg';
import DefaultThumbnail from '@/assets/svg/default-thumbnail-web.svg';


type AvatarProps = {
  size?: number;
  source?: string;
  type?: 'user' | 'group';
  profilePicture?: string; // Additional fallback for user profile picture
  headerImage?: string; // Header image as primary source
    customDefault?: 'thumbnail'; // only currently supports one custom fallback

};

const Avatar: FunctionComponent<AvatarProps> = ({
  source,
  type = 'user',
  size = 38,
  profilePicture,
  headerImage,
  customDefault,
}) => {
  const [imageError, setImageError] = useState(false);

  const styles = {
    image: {
      width: size,
      height: size,
      borderRadius: size / 2,
      objectFit: 'cover' as const,
      display: 'block',
    },
    svgContainer: {
      width: size,
      height: size,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },
  };

  // Get the best available image source with fallback logic
  const getImageSrc = () => {
    if (imageError) return null;
    return headerImage || source || profilePicture || null;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const imageSrc = getImageSrc();

  if (customDefault === 'thumbnail') {
  return (
    <div style={styles.svgContainer}>
      <DefaultThumbnail width={size} height={size} />
    </div>
  );
}

  if (imageSrc) {
    return (
      <Image
        src={imageSrc}
        alt="Avatar"
        width={size}
        height={size}
        style={styles.image}
        onError={handleImageError}
      />
    );
  } else if (type === 'user') {
    return (
      <div style={styles.svgContainer}>
        <DefaultProfileSVG width={size} height={size} /> 
      </div>
    );
  } else {
    return (
      <div style={styles.svgContainer}>
        <DefaultGroupSVG width={size} height={size} />
      </div>
    );
  }
};

export { Avatar };
export default Avatar;

import { poppinsRegular } from "@/fonts";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";

interface SelectDropdownProps {
  data: string[];
  onSelect: (selectedItem: string, index: number) => void;
  defaultButtonText: string;
  buttonTextAfterSelection: (selectedItem: string, index: number) => string;
  search?: boolean;
}

const SelectDropdown: React.FC<SelectDropdownProps> = ({
  data,
  onSelect,
  defaultButtonText,
  buttonTextAfterSelection,
  search = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredData = search
    ? data.filter(item => item.toLowerCase().includes(searchTerm.toLowerCase()))
    : data;

  const handleSelect = (item: string, index: number) => {
    setSelectedItem(item);
    onSelect(item, index);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex border border-[#D0D5DD] rounded-[6px] p-[1rem] items-center justify-between text-left"
      >
        <span className={`tesxt-[#98A2B3] text-[14px] ${poppinsRegular.className}`}>
          {selectedItem ? buttonTextAfterSelection(selectedItem, 0) : defaultButtonText}
        </span>
        {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 mt-1">
          {search && (
            <div className="p-2 border-b">
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>
          )}
          <div className="max-h-48 overflow-y-auto">
            {filteredData.map((item, index) => (
              <button
                key={index}
                onClick={() => handleSelect(item, index)}
                className="w-full p-3 text-left hover:bg-gray-50 font-semibold"
              >
                {item}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectDropdown;
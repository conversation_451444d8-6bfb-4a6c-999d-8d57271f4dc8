import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { poppinsMedium, poppinsRegular } from "@/fonts"

const buttonVariants = cva(
  "transition-all duration-200 ease-in-out ",
  {
    variants: {
      variant: {
        primary: `bg-[#163E23] cursor-pointer rounded-sm items-center px-4 py-3 w-full text-white text-sm hover:bg-[#163E23]/90 active:bg-[#163E23]/95 ${poppinsMedium.className}`,
        tertiary: `cursor-pointer bg-transparent border border-accent rounded-sm items-center p-md w-full text-primary text-sm hover:bg-accent/10  ${poppinsMedium.className}`,
        default: "cursor-pointer bg-primary text-primary-foreground hover:bg-primary/90 ",
        destructive: "cursor-pointer bg-destructive text-destructive-foreground hover:bg-destructive/90 ",
        outline: "cursor-pointer border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "cursor-pointer bg-secondary hover:bg-secondary/80 ",
        ghost: "cursor-pointer hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 cursor-pointer hover:underline",
        formButton: "px-[1rem] py-[14px]",
        backButton: `cursor-pointer border border-input flex w-full items-center justify-between text-muted-foreground text-sm border-input bg-transparent rounded-xs py-md px-lg hover:bg-accent/10  ${poppinsMedium.className}`,
        deleteButton: `bg-[#163E23] cursor-pointer flex text-white justify-center items-center rounded-[8px] ${poppinsRegular.className} text-[12px]`,
        cancelButton: `bg-transparent border cursor-pointer border-[#F2875D]  flex justify-center items-center rounded-[8px] ${poppinsRegular.className} text-[12px]`
      },
      size: {
        default: "px-lg py-md",
        sm: "h-9 px-3 rounded-md text-sm",
        lg: "h-11 px-8 rounded-md text-base",
        icon: "h-10 w-10 p-0",
        xs: "px-sm py-xs text-xs",
        xl: "px-xl py-lg text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />
  },
)
Button.displayName = "Button"

export { Button, buttonVariants }

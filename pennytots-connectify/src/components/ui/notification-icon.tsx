'use client';

import React from 'react';
import { Bell } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface NotificationIconProps {
  count: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function NotificationIcon({ count, size = 'md', className = '' }: NotificationIconProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const badgeSizeClasses = {
    sm: 'h-4 w-4 text-xs',
    md: 'h-5 w-5 text-xs',
    lg: 'h-6 w-6 text-sm'
  };

  return (
    <div className={`relative ${className}`}>
      <Bell className={`${sizeClasses[size]} text-[#5A5E5C]`} />
      {count > 0 && (
        <Badge 
          variant="destructive" 
          className={`absolute -top-1 -right-1 ${badgeSizeClasses[size]} flex items-center justify-center p-0 bg-[#FE3233] hover:bg-[#FE3233]`}
        >
          {count > 99 ? '99+' : count}
        </Badge>
      )}
    </div>
  );
}

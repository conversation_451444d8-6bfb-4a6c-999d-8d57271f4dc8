'use client';

import React, { useRef } from 'react';
import { Paperclip, X } from 'lucide-react';
import { Button } from './button';

interface SelectAttachmentProps {
  onFileSelect: (file: File) => void;
  className?: string;
  disabled?: boolean;
}

export const SelectAttachment: React.FC<SelectAttachmentProps> = ({ 
  onFileSelect, 
  className = '',
  disabled = false 
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileSelect(file);
    }
    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleButtonClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={className}>
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
        disabled={disabled}
      />
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick}
        disabled={disabled}
        className="p-2 hover:bg-gray-100 rounded-full"
      >
        <Paperclip className="h-5 w-5 text-gray-600" />
      </Button>
    </div>
  );
};

export default SelectAttachment;

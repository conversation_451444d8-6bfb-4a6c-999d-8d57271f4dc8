'use client';

import React from 'react';
import { useActivity } from '@/providers/ActivityContext';
import { Button } from './button';
import { X } from 'lucide-react';

export const SponsoredModal: React.FC = () => {
  const { modalVisible, resetModalVisibility } = useActivity();

  if (!modalVisible.sponsor) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-4 relative">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => resetModalVisibility('sponsor')}
          className="absolute top-2 right-2 p-1"
        >
          <X className="h-4 w-4" />
        </Button>
        
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Sponsored Content</h3>
          <p className="text-gray-600 mb-4">
            You'll be redirected to view a sponsored advertisement in a moment.
          </p>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

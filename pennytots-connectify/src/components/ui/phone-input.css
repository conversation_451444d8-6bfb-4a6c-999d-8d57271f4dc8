.react-international-phone-input {
  width: 100% !important;
  border-radius: 0.375rem !important; /* 6px */
  border: 1px solid var(--color-input, #D0D5DD) !important;
  font-size: 0.875rem !important; /* 14px */
  padding: 17px 1rem !important; /* 18px 16px */
  transition: border-color 0.2s ease-in-out !important;
}

.react-international-phone-input:hover {
  border-color: var(--color-ring, #F2875D) !important;
}
.react-international-phone-input-container .react-international-phone-input{
  padding: 11px 1rem;
  height: 0%;
}
.react-international-phone-input:focus-within {
  outline: none !important;
  border-color: var(--color-ring, #F2875D) !important;
  box-shadow: 0 0 0 2px rgba(242, 135, 93, 0.2) !important;
}

.react-international-phone-country-selector-button__button-content {
  border-radius: 0.125rem !important; /* 2px */
  border: 1px solid var(--color-input, #D0D5DD) !important;
  font-size: 0.875rem !important; /* 14px */
  height: inherit;
  width: 100%;
  gap: 0.25rem !important; /* 4px */
  transition: all 0.2s ease-in-out !important;
}
.react-international-phone-input-container .react-international-phone-country-selector-button{
  height: 49px;
  width: 70px;
}

.react-international-phone-country-selector-button__button-content:hover {
  border-color: var(--color-ring, #F2875D) !important;
  background-color: rgba(242, 135, 93, 0.05) !important;
}

.react-international-phone-flag-emoji {
  width: 1.125rem !important; /* 18px */
  height: 0.875rem !important; /* 14px */
  flex-shrink: 0 !important;
}
.react-international-phone-country-selector-button__button-content{
  padding: 11px 8px;
  width: 100%;
}
/* Responsive adjustments */

  .react-international-phone-input {
    font-size: 0.8125rem !important; /* 13px */
  }



  .react-international-phone-flag-emoji {
    width: 1rem !important; /* 16px */
    height: 0.75rem !important; /* 12px */
  }

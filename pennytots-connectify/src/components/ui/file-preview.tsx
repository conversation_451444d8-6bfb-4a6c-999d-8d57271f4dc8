'use client';

import React from 'react';
import { X, FileText, Image as ImageIcon, Video, Music } from 'lucide-react';
import { Button } from './button';
import Image from 'next/image';

interface FilePreviewProps {
  file: File;
  onRemove: () => void;
  className?: string;
}

export const FilePreview: React.FC<FilePreviewProps> = ({ file, onRemove, className = '' }) => {
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon className="h-4 w-4" />;
    } else if (fileType.startsWith('video/')) {
      return <Video className="h-4 w-4" />;
    } else if (fileType.startsWith('audio/')) {
      return <Music className="h-4 w-4" />;
    } else {
      return <FileText className="h-4 w-4" />;
    }
  };

  const getFileTypeTag = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return fileType.split('/')[1].toUpperCase();
    } else if (fileType.startsWith('video/')) {
      return fileType.split('/')[1].toUpperCase();
    } else if (fileType.startsWith('audio/')) {
      return fileType.split('/')[1].toUpperCase();
    } else {
      // For other files, try to get extension from name
      const extension = file.name.split('.').pop();
      return extension ? extension.toUpperCase() : 'FILE';
    }
  };

  const isImage = file.type.startsWith('image/');
  const fileUrl = URL.createObjectURL(file);

  return (
    <div className={`relative bg-gray-50 border border-gray-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-start gap-3">
        {/* File Preview */}
        <div className="flex-shrink-0">
          {isImage ? (
            <div className="relative w-12 h-12 rounded overflow-hidden">
              <Image
                src={fileUrl}
                alt={file.name}
                fill
                className="object-cover"
                onLoad={() => URL.revokeObjectURL(fileUrl)}
              />
            </div>
          ) : (
            <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
              {getFileIcon(file.type)}
            </div>
          )}
        </div>

        {/* File Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
              {getFileTypeTag(file.type)}
            </span>
          </div>
          <p className="text-sm font-medium text-gray-900 truncate">
            {file.name}
          </p>
          <p className="text-xs text-gray-500">
            {(file.size / 1024 / 1024).toFixed(2)} MB
          </p>
        </div>

        {/* Remove Button */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onRemove}
          className="p-1 hover:bg-gray-200 rounded-full"
        >
          <X className="h-4 w-4 text-gray-500" />
        </Button>
      </div>
    </div>
  );
};

export default FilePreview;

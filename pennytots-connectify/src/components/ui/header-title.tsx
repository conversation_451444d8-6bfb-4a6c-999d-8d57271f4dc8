import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { poppinsMedium } from '@/fonts';

interface HeaderTitleProps {
  title: string;
  onBack: () => void;
}

const HeaderTitle: React.FC<HeaderTitleProps> = ({ title, onBack }) => {
  return (
    <div className="flex items-center gap-[1rem]">
      <button onClick={onBack} className="w-[14px] h-[17px]">
        <ArrowLeft color='#5A5E5C' />
      </button>
      <h1 className={`${poppinsMedium.className} text-[#5A5E5C] text-[0.875rem]`}>{title}</h1>
    </div>
  );
};

export default HeaderTitle;

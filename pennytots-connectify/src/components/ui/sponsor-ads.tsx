'use client';

import React from 'react';
import { useActivity } from '@/providers/ActivityContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { poppinsSemibold, poppinsMedium } from '@/fonts';
import SponsoredAdImage from '@/assets/images/sponsoredAds.png';


export const SponsorAds: React.FC = () => {
  const { sponsoredAd, isAdLoading } = useActivity();
  const router = useRouter();

  // Show a default ad if no sponsored ad is available
  const defaultAd = {
    title: "Discover Amazing Deals",
    description: "Check out our latest offers and promotions. Don't miss out on exclusive deals!",
    image: "https://placehold.co/400x200/F58634/FFF?text=Sponsored+Ad",
    company: "PennyTots"
  };

  const adToShow = sponsoredAd || defaultAd;

  // Don't render only if still loading
  if (isAdLoading) {
    return (
      <Card className="mb-6 border border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-20 mb-3"></div>
            <div className="h-48 bg-gray-200 rounded mb-3"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleAdClick = () => {
    router.push('/sponsor');
  };

  return (
    <Card className="mt-[12px]">
      <CardContent className="w-full max-w-[354px]">
        <div className="flex items-start justify-between mb-3">
          <span className={`${poppinsSemibold.className} text-[#797C7B] text-[12px]`}>
            #Sponsored
          </span>
        </div>
        
        <div className="flex flex-col space-y-3">
          {/* Ad Image */}
          {adToShow.image && (
            <div className="flex justify-center">
              <Image
                src={SponsoredAdImage}
                alt="Sponsored Advertisement"
                width={281}
                height={375}
                onError={() => {
                  console.error('Failed to load sponsored ad image');
                }}
              />
            </div>
          )}

          {/* Ad Content */}
          {/* <div className="space-y-2">
            {adToShow.title && (
              <h3 className={`${poppinsSemibold.className} text-[#5A5E5C] text-[16px]`}>
                {adToShow.title}
              </h3>
            )}

            {adToShow.description && (
              <p className={`${poppinsMedium.className} text-[#797C7B] text-[14px] line-clamp-2`}>
                {adToShow.description}
              </p>
            )}

            {adToShow.company && (
              <p className={`${poppinsMedium.className} text-[#B5B5B5] text-[12px]`}>
                by {adToShow.company}
              </p>
            )}
          </div> */}
          
          {/* CTA Button */}
          {/* <Button
            onClick={handleAdClick}
            variant="outline"
            className="w-full mt-3 border-[#F58634] text-[#F58634] hover:text-white"
          >
            <span className={`${poppinsMedium.className} text-[14px]`}>
              Learn More
            </span>
          </Button> */}
        </div>
      </CardContent>
    </Card>
  );
};

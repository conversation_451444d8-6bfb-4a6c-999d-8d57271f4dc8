'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ProfileClickHandler, { ProfileChatLink, ProfileViewLink } from '@/components/profile/ProfileClickHandler';
import { ProfileChatLink as ProfileLinkChat } from '@/components/profile/ProfileLink';
import { Avatar } from '@/components/ui/avatar';
import ChatSearch from '@/components/chat/ChatSearch';
import { ChatListSkeleton, MessageSkeleton, ConnectionStatus, UploadProgress, TypingIndicator } from '@/components/chat/ChatLoadingStates';
import { ChatErrorBoundary } from '@/components/chat/ChatErrorBoundary';
import { ProfileNotifications, useProfileNotifications } from '@/components/profile/ProfileNotifications';
import ProfileClickDebug from '@/components/debug/ProfileClickDebug';
import { poppinsRegular, poppinsMedium } from '@/fonts';

// Sample user data for testing
const sampleUsers = [
  {
    _id: '507f1f77bcf86cd799439011',
    first_name: 'John',
    last_name: 'Doe',
    profile_picture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  },
  {
    _id: '507f1f77bcf86cd799439012',
    first_name: 'Jane',
    last_name: 'Smith',
    profile_picture: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
  },
  {
    _id: '507f1f77bcf86cd799439013',
    first_name: 'Mike',
    last_name: 'Johnson',
    profile_picture: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  },
  {
    _id: '507f1f77bcf86cd799439014',
    first_name: 'Sarah',
    last_name: 'Wilson',
    profile_picture: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
  }
];

const ChatDemo: React.FC = () => {
  const [lastAction, setLastAction] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected' | 'error'>('connected');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showUpload, setShowUpload] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);

  // Toast notifications
  const { addNotification, notifications, dismissNotification } = useProfileNotifications();

  const handleChatStart = (chatData: any, userName: string) => {
    setLastAction(`Started chat with ${userName}`);
    addNotification({
      type: 'success',
      title: 'Chat Started',
      message: `Successfully started chat with ${userName}`
    });
    console.log('Chat started:', chatData);
  };

  const handleProfileView = (userId: string, userName: string) => {
    setLastAction(`Viewed profile of ${userName}`);
    addNotification({
      type: 'info',
      title: 'Profile Viewed',
      message: `Viewing profile of ${userName}`
    });
    console.log('Profile viewed:', userId);
  };

  const simulateUpload = () => {
    setShowUpload(true);
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setShowUpload(false);
          addNotification({
            type: 'success',
            title: 'Upload Complete',
            message: 'File uploaded successfully'
          });
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const simulateTyping = () => {
    setTypingUsers(['John Doe']);
    setTimeout(() => setTypingUsers([]), 3000);
  };

  const simulateError = () => {
    addNotification({
      type: 'error',
      title: 'Connection Error',
      message: 'Failed to connect to chat service'
    });
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Toast Notifications */}
      <ProfileNotifications
        notifications={notifications}
        onDismiss={dismissNotification}
        position="top-right"
      />

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className={poppinsMedium.className}>
            Enhanced Chat Functionality Demo
          </CardTitle>
          <p className={`${poppinsRegular.className} text-gray-600`}>
            Comprehensive demonstration of all chat features including profile clicks, search, notifications, and more.
          </p>
        </CardHeader>
        <CardContent>
          {lastAction && (
            <div className="mb-4 p-3 bg-green-100 border border-green-300 rounded">
              <p className={`${poppinsRegular.className} text-green-800`}>
                Last Action: {lastAction}
              </p>
            </div>
          )}

          {/* Demo Controls */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Button onClick={simulateUpload} variant="outline" size="sm">
              Simulate Upload
            </Button>
            <Button onClick={simulateTyping} variant="outline" size="sm">
              Simulate Typing
            </Button>
            <Button onClick={simulateError} variant="outline" size="sm">
              Simulate Error
            </Button>
            <Button
              onClick={() => setConnectionStatus(prev =>
                prev === 'connected' ? 'disconnected' : 'connected'
              )}
              variant="outline"
              size="sm"
            >
              Toggle Connection
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="debug" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="debug">Debug</TabsTrigger>
          <TabsTrigger value="profile-clicks">Profile Clicks</TabsTrigger>
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="loading-states">Loading States</TabsTrigger>
          <TabsTrigger value="error-handling">Error Handling</TabsTrigger>
          <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
        </TabsList>

        <TabsContent value="debug">
          <ProfileClickDebug />
        </TabsContent>

        <TabsContent value="profile-clicks" className="space-y-6">

        {/* Profile Click Handler Examples */}
        <Card>
          <CardHeader>
            <CardTitle className={poppinsMedium.className}>
              ProfileClickHandler Component
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {sampleUsers.map((user) => (
              <div key={user._id} className="flex items-center justify-between p-3 border rounded">
                <div className="flex items-center gap-3">
                  <ProfileClickHandler
                    user={user}
                    showAvatar={true}
                    avatarSize={40}
                    defaultAction="chat"
                    onChatStart={(chatData) => handleChatStart(chatData, `${user.first_name} ${user.last_name}`)}
                  >
                    <Avatar
                      source={user.profile_picture}
                      size={40}
                      profilePicture={user.profile_picture}
                    />
                  </ProfileClickHandler>

                  <ProfileClickHandler
                    user={user}
                    defaultAction="chat"
                    className="hover:text-blue-600"
                    onChatStart={(chatData) => handleChatStart(chatData, `${user.first_name} ${user.last_name}`)}
                  >
                    <span className={poppinsMedium.className}>
                      {user.first_name} {user.last_name}
                    </span>
                  </ProfileClickHandler>
                </div>

                <ProfileClickHandler
                  user={user}
                  showDropdown={true}
                  onChatStart={(chatData) => handleChatStart(chatData, `${user.first_name} ${user.last_name}`)}
                  onProfileView={(userId) => handleProfileView(userId, `${user.first_name} ${user.last_name}`)}
                />
              </div>
            ))}
          </CardContent>
        </Card>
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className={poppinsMedium.className}>
              Chat Search & Filtering
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChatSearch
              onSearch={(query, filters) => {
                console.log('Search:', query, filters);
                setLastAction(`Searched for: "${query}" with filters`);
              }}
              results={[
                {
                  chatId: '1',
                  type: 'chat',
                  title: 'John Doe',
                  subtitle: 'Hey, how are you doing?',
                  timestamp: new Date(),
                  participants: [{ id: '1', name: 'John Doe' }],
                  isUnread: true
                },
                {
                  chatId: '2',
                  messageId: 'msg1',
                  type: 'message',
                  title: 'Jane Smith',
                  subtitle: 'Check out this document',
                  snippet: 'I found this interesting document that might help with our project...',
                  timestamp: new Date(Date.now() - 3600000),
                  participants: [{ id: '2', name: 'Jane Smith' }],
                  hasAttachments: true,
                  messageType: 'document'
                }
              ]}
              onResultClick={(result) => {
                setLastAction(`Clicked search result: ${result.title}`);
              }}
            />
          </CardContent>
        </Card>
        </TabsContent>

        <TabsContent value="loading-states" className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className={poppinsMedium.className}>
                Connection Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ConnectionStatus
                status={connectionStatus}
                onRetry={() => {
                  setConnectionStatus('connecting');
                  setTimeout(() => setConnectionStatus('connected'), 2000);
                }}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className={poppinsMedium.className}>
                Upload Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              {showUpload ? (
                <UploadProgress
                  fileName="document.pdf"
                  progress={uploadProgress}
                  onCancel={() => setShowUpload(false)}
                />
              ) : (
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  No active uploads
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className={poppinsMedium.className}>
                Typing Indicator
              </CardTitle>
            </CardHeader>
            <CardContent>
              <TypingIndicator users={typingUsers} />
              {typingUsers.length === 0 && (
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  No one is typing
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className={poppinsMedium.className}>
                Loading Skeletons
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <MessageSkeleton />
                <MessageSkeleton isOwnMessage />
              </div>
            </CardContent>
          </Card>
        </div>
        </TabsContent>

        <TabsContent value="error-handling" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className={poppinsMedium.className}>
              Error Boundary Demo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChatErrorBoundary>
              <div className="p-4 border rounded">
                <p className={`${poppinsRegular.className} mb-4`}>
                  This content is wrapped in a ChatErrorBoundary. Any errors here will be caught gracefully.
                </p>
                <Button
                  onClick={() => {
                    throw new Error('Demo error for testing error boundary');
                  }}
                  variant="destructive"
                  size="sm"
                >
                  Trigger Error
                </Button>
              </div>
            </ChatErrorBoundary>
          </CardContent>
        </Card>
        </TabsContent>

        <TabsContent value="accessibility" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className={poppinsMedium.className}>
              Accessibility Features
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded">
              <h4 className={`${poppinsMedium.className} text-sm mb-2`}>Keyboard Shortcuts</h4>
              <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
                <li><kbd className="px-2 py-1 bg-gray-100 rounded text-xs">?</kbd> - Show keyboard shortcuts</li>
                <li><kbd className="px-2 py-1 bg-gray-100 rounded text-xs">/</kbd> - Focus message input</li>
                <li><kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Esc</kbd> - Cancel/close dialogs</li>
                <li><kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl + ↑/↓</kbd> - Navigate messages</li>
              </ul>
            </div>

            <div className="p-4 bg-green-50 border border-green-200 rounded">
              <h4 className={`${poppinsMedium.className} text-sm mb-2`}>Screen Reader Support</h4>
              <ul className={`${poppinsRegular.className} text-sm space-y-1`}>
                <li>• ARIA labels and roles for all interactive elements</li>
                <li>• Live regions for message announcements</li>
                <li>• Proper heading hierarchy</li>
                <li>• Focus management for keyboard navigation</li>
              </ul>
            </div>
          </CardContent>
        </Card>
        </TabsContent>
      </Tabs>

    </div>
  );
};

export default ChatDemo;

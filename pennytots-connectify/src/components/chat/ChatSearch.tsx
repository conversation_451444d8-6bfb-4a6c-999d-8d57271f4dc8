'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  User,
  MessageCircle,
  Image,
  FileText,
  Video,
  Mic
} from 'lucide-react';
import { poppinsRegular, poppinsMedium } from '@/fonts';

export interface ChatSearchFilters {
  dateRange?: {
    start: Date;
    end: Date;
  };
  messageTypes?: string[];
  participants?: string[];
  hasAttachments?: boolean;
  isUnread?: boolean;
  isMuted?: boolean;
}

export interface ChatSearchResult {
  chatId: string;
  messageId?: string;
  type: 'chat' | 'message';
  title: string;
  subtitle: string;
  timestamp: Date;
  snippet?: string;
  participants: Array<{
    id: string;
    name: string;
    avatar?: string;
  }>;
  messageType?: string;
  hasAttachments?: boolean;
  isUnread?: boolean;
}

interface ChatSearchProps {
  onSearch: (query: string, filters: ChatSearchFilters) => void;
  results: ChatSearchResult[];
  isLoading?: boolean;
  placeholder?: string;
  showFilters?: boolean;
  onResultClick?: (result: ChatSearchResult) => void;
}

export const ChatSearch: React.FC<ChatSearchProps> = ({
  onSearch,
  results,
  isLoading = false,
  placeholder = "Search chats and messages...",
  showFilters = true,
  onResultClick
}) => {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<ChatSearchFilters>({});
  const [showResults, setShowResults] = useState(false);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (query.trim() || Object.keys(filters).length > 0) {
        onSearch(query, filters);
        setShowResults(true);
      } else {
        setShowResults(false);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [query, filters, onSearch]);

  const handleFilterChange = (key: keyof ChatSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const clearSearch = () => {
    setQuery('');
    setFilters({});
    setShowResults(false);
  };

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.dateRange) count++;
    if (filters.messageTypes?.length) count++;
    if (filters.participants?.length) count++;
    if (filters.hasAttachments) count++;
    if (filters.isUnread) count++;
    if (filters.isMuted) count++;
    return count;
  }, [filters]);

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-4 w-4" />;
      case 'video':
        return <Video className="h-4 w-4" />;
      case 'audio':
        return <Mic className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      default:
        return <MessageCircle className="h-4 w-4" />;
    }
  };

  const highlightText = (text: string, searchQuery: string) => {
    if (!searchQuery.trim()) return text;
    
    const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  return (
    <div className="relative">
      {/* Search Input */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={placeholder}
            className="pl-10 pr-10"
          />
          {(query || activeFiltersCount > 0) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Filters */}
        {showFilters && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="relative">
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {activeFiltersCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs"
                  >
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuCheckboxItem
                checked={filters.hasAttachments || false}
                onCheckedChange={(checked) => 
                  handleFilterChange('hasAttachments', checked || undefined)
                }
              >
                Has Attachments
              </DropdownMenuCheckboxItem>
              
              <DropdownMenuCheckboxItem
                checked={filters.isUnread || false}
                onCheckedChange={(checked) => 
                  handleFilterChange('isUnread', checked || undefined)
                }
              >
                Unread Messages
              </DropdownMenuCheckboxItem>
              
              <DropdownMenuCheckboxItem
                checked={filters.isMuted || false}
                onCheckedChange={(checked) => 
                  handleFilterChange('isMuted', checked || undefined)
                }
              >
                Muted Chats
              </DropdownMenuCheckboxItem>

              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={clearFilters}>
                Clear All Filters
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {filters.hasAttachments && (
            <Badge variant="secondary" className="text-xs">
              Has Attachments
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('hasAttachments', undefined)}
                className="ml-1 h-3 w-3 p-0"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          
          {filters.isUnread && (
            <Badge variant="secondary" className="text-xs">
              Unread
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('isUnread', undefined)}
                className="ml-1 h-3 w-3 p-0"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
          
          {filters.isMuted && (
            <Badge variant="secondary" className="text-xs">
              Muted
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterChange('isMuted', undefined)}
                className="ml-1 h-3 w-3 p-0"
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Search Results */}
      {showResults && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-50 max-h-96 overflow-y-auto">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto" />
                <p className={`${poppinsRegular.className} text-sm text-gray-500 mt-2`}>
                  Searching...
                </p>
              </div>
            ) : results.length === 0 ? (
              <div className="p-4 text-center">
                <Search className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className={`${poppinsRegular.className} text-sm text-gray-500`}>
                  No results found
                </p>
              </div>
            ) : (
              <div className="divide-y">
                {results.map((result, index) => (
                  <div
                    key={`${result.chatId}-${result.messageId || index}`}
                    className="p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => onResultClick?.(result)}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {result.type === 'message' && result.messageType ? 
                          getMessageTypeIcon(result.messageType) : 
                          <MessageCircle className="h-4 w-4 text-gray-400" />
                        }
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`${poppinsMedium.className} text-sm text-gray-900 truncate`}>
                            {highlightText(result.title, query)}
                          </h4>
                          <span className="text-xs text-gray-500 ml-2">
                            {result.timestamp.toLocaleDateString()}
                          </span>
                        </div>
                        
                        <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate`}>
                          {highlightText(result.subtitle, query)}
                        </p>
                        
                        {result.snippet && (
                          <p className={`${poppinsRegular.className} text-xs text-gray-500 mt-1 line-clamp-2`}>
                            {highlightText(result.snippet, query)}
                          </p>
                        )}
                        
                        <div className="flex items-center gap-2 mt-2">
                          {result.participants.slice(0, 3).map((participant) => (
                            <div key={participant.id} className="flex items-center gap-1">
                              <div className="w-4 h-4 rounded-full bg-gray-300 flex items-center justify-center">
                                {participant.avatar ? (
                                  <img
                                    src={participant.avatar}
                                    alt=""
                                    className="w-full h-full rounded-full object-cover"
                                  />
                                ) : (
                                  <span className="text-xs font-bold text-gray-600">
                                    {participant.name.charAt(0)}
                                  </span>
                                )}
                              </div>
                              <span className="text-xs text-gray-500">
                                {participant.name}
                              </span>
                            </div>
                          ))}
                          
                          {result.participants.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{result.participants.length - 3} more
                            </span>
                          )}
                          
                          {result.hasAttachments && (
                            <Badge variant="outline" className="text-xs">
                              📎
                            </Badge>
                          )}
                          
                          {result.isUnread && (
                            <Badge variant="destructive" className="text-xs">
                              New
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ChatSearch;

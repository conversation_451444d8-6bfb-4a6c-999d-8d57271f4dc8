'use client';

import React, { useState } from 'react';
import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { poppinsRegular, poppinsMedium } from '@/fonts';
import { MoreVertical, Reply, Copy, Trash2, Flag } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { ImageViewer } from '@/components/ui/image-viewer';

interface MessageBubbleProps {
  message: {
    _id: string;
    message: string;
    senderId: string;
    createdAt: string | Date;
    user: {
      _id: string;
      first_name?: string;
      last_name?: string;
      profile_picture?: string;
    };
    type?: string;
    attachment?: string;
    attachmentType?: string;
    quotedReply?: any;
    pending?: boolean;
  };
  isOwnMessage: boolean;
  showAvatar?: boolean;
  onReply?: (message: any) => void;
  onDelete?: (messageId: string) => void;
  onReport?: (messageId: string) => void;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwnMessage,
  showAvatar = true,
  onReply,
  onDelete,
  onReport
}) => {
  const [showImageViewer, setShowImageViewer] = useState(false);

  const formatTime = (date: string | Date) => {
    const messageDate = new Date(date);
    return messageDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.message);
  };

  const renderAttachment = () => {
    if (!message.attachment) return null;

    switch (message.type) {
      case 'image':
        return (
          <div className="mt-2">
            <img
              src={message.attachment}
              alt="Attachment"
              className="max-w-xs rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => setShowImageViewer(true)}
            />
          </div>
        );
      case 'video':
        return (
          <div className="mt-2">
            <video
              src={message.attachment}
              controls
              className="max-w-xs rounded-lg"
            />
          </div>
        );
      case 'audio':
        return (
          <div className="mt-2">
            <audio
              src={message.attachment}
              controls
              className="max-w-xs"
            />
          </div>
        );
      case 'document':
        return (
          <div className="mt-2 p-3 bg-gray-100 rounded-lg max-w-xs">
            <a
              href={message.attachment}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              📄 View Document
            </a>
          </div>
        );
      default:
        return null;
    }
  };

  const renderQuotedMessage = () => {
    if (!message.quotedReply) return null;

    return (
      <div className="mb-2 p-2 bg-gray-100 border-l-4 border-blue-500 rounded">
        <p className={`text-xs text-gray-600 ${poppinsMedium.className}`}>
          {message.quotedReply.user?.first_name} {message.quotedReply.user?.last_name}
        </p>
        <p className={`text-sm text-gray-800 ${poppinsRegular.className}`}>
          {message.quotedReply.message}
        </p>
      </div>
    );
  };

  return (
    <div className={`flex mb-4 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
      {/* Avatar for other users */}
      {!isOwnMessage && showAvatar && (
        <div className="mr-3">
          <Avatar
            source={message.user.profile_picture}
            size={32}
            profilePicture={message.user.profile_picture}
          />
        </div>
      )}

      <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-1' : 'order-2'}`}>
        {/* Message bubble */}
        <div
          className={`relative px-4 py-2 rounded-lg ${
            isOwnMessage
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 text-gray-800'
          } ${message.pending ? 'opacity-70' : ''}`}
        >
          {/* Quoted message */}
          {renderQuotedMessage()}

          {/* Message text */}
          {message.message && (
            <p className={`${poppinsRegular.className} text-sm`}>
              {message.message}
            </p>
          )}

          {/* Attachment */}
          {renderAttachment()}

          {/* Message actions */}
          <div className="absolute top-1 right-1">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onReply && (
                  <DropdownMenuItem onClick={() => onReply(message)}>
                    <Reply className="h-4 w-4 mr-2" />
                    Reply
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={handleCopyMessage}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </DropdownMenuItem>
                {isOwnMessage && onDelete && (
                  <DropdownMenuItem 
                    onClick={() => onDelete(message._id)}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                )}
                {!isOwnMessage && onReport && (
                  <DropdownMenuItem 
                    onClick={() => onReport(message._id)}
                    className="text-red-600"
                  >
                    <Flag className="h-4 w-4 mr-2" />
                    Report
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Timestamp */}
        <div className={`mt-1 text-xs text-gray-500 ${isOwnMessage ? 'text-right' : 'text-left'}`}>
          {formatTime(message.createdAt)}
          {message.pending && (
            <span className="ml-1 text-gray-400">Sending...</span>
          )}
        </div>
      </div>

      {/* Avatar for own messages */}
      {isOwnMessage && showAvatar && (
        <div className="ml-3 order-2">
          <Avatar
            source={message.user.profile_picture}
            size={32}
            profilePicture={message.user.profile_picture}
          />
        </div>
      )}

      {/* Image Viewer */}
      <ImageViewer
        isOpen={showImageViewer}
        onClose={() => setShowImageViewer(false)}
        imageUrl={message.attachment || ''}
        alt="Message attachment"
      />
    </div>
  );
};

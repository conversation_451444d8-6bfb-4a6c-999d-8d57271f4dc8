'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Phone, Video, MoreVertical, Search, X, ChevronUp, ChevronDown, Send, Smile, Paperclip } from 'lucide-react';
import { poppinsSemibold, poppinsRegular, poppinsMedium, poppinsItalic } from '@/fonts';
import { chat as chatAPI } from '@/api/chat';
import { useSelector } from 'react-redux';
import { userAuthInfo, userToken } from '@/redux/user/reducer';
import socketService from '@/services/socket';
import SendIcon from '@/assets/svg/send2.svg';
import Image from 'next/image';
import Avatar from '../ui/avatar';
import EmojiPicker from '@/components/ui/emoji-picker';

import SelectAttachment from '@/components/ui/select-attachment';
import { ImageViewer } from '@/components/ui/image-viewer';
import FilePreview from '@/components/ui/file-preview';
import { Axios } from '@/api/axios';


interface SimpleChatRoomProps {
  chat: any;
  onBack: () => void;
  userDetails?: any;
}

const SimpleChatRoom: React.FC<SimpleChatRoomProps> = ({
  chat,
  onBack,
  userDetails
}) => {
  const [messages, setMessages] = useState<Array<{
    id: string;
    text: string;
    timestamp: Date;
    isOwn: boolean;
    pending?: boolean;
    attachment?: {
      name: string;
      size: number;
      type: string;
    };
    attachmentType?: string;
    name?: string;
    size?: number;
    type?: string;
  }>>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [actualChatId, setActualChatId] = useState<string | null>(null);
  
  // Search states
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<number[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [highlightedMessageId, setHighlightedMessageId] = useState<string | null>(null);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const currentUser = useSelector(userAuthInfo);
  const token = useSelector(userToken);

  // Search functionality
  const performSearch = (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      setHighlightedMessageId(null);
      return;
    }

    const results: number[] = [];
    messages.forEach((message, index) => {
      if (message.text.toLowerCase().includes(query.toLowerCase())) {
        results.push(index);
      }
    });

    setSearchResults(results);
    setCurrentSearchIndex(results.length > 0 ? 0 : -1);
    
    // Highlight first result
    if (results.length > 0) {
      setHighlightedMessageId(messages[results[0]].id);
      scrollToMessage(results[0]);
    } else {
      setHighlightedMessageId(null);
    }
  };

  const scrollToMessage = (messageIndex: number) => {
    const messageElement = document.getElementById(`message-${messages[messageIndex].id}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const navigateSearch = (direction: 'next' | 'prev') => {
    if (searchResults.length === 0) return;

    let newIndex: number;
    if (direction === 'next') {
      newIndex = currentSearchIndex >= searchResults.length - 1 ? 0 : currentSearchIndex + 1;
    } else {
      newIndex = currentSearchIndex <= 0 ? searchResults.length - 1 : currentSearchIndex - 1;
    }

    setCurrentSearchIndex(newIndex);
    setHighlightedMessageId(messages[searchResults[newIndex]].id);
    scrollToMessage(searchResults[newIndex]);
  };

  const closeSearch = () => {
    setIsSearchOpen(false);
    setSearchQuery('');
    setSearchResults([]);
    setCurrentSearchIndex(-1);
    setHighlightedMessageId(null);
  };

  // Handle search input
  useEffect(() => {
    if (searchQuery) {
      performSearch(searchQuery);
    } else {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      setHighlightedMessageId(null);
    }
  }, [searchQuery, messages]);

  // Highlight text function
  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    
    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    return parts.map((part, index) => 
      part.toLowerCase() === query.toLowerCase() 
        ? <span key={index} className="bg-yellow-300 text-black font-semibold">{part}</span>
        : part
    );
  };

  // Load existing messages when component mounts
  useEffect(() => {
    const loadMessages = async () => {
      if (!userDetails?._id) return;

      try {
        setIsLoading(true);

        // Try to get existing chat messages
        const chatData = await chatAPI.getUserChats({
          userId: userDetails._id,
          pageNumber: 1,
          limit: 50
        });

        if (chatData && chatData.chats && chatData.chats.docs && chatData.chats.docs.length > 0) {
          // Convert backend messages to our format
          const backendMessages = chatData.chats.docs.map((msg: any) => ({
            id: msg._id,
            text: msg.message,
            timestamp: new Date(msg.createdAt),
            isOwn: msg.senderId === currentUser?._id
          }));
          setMessages(backendMessages);

          // Set the actual chat ID for future messages
          if (chatData.chats.docs[0].chatId) {
            setActualChatId(chatData.chats.docs[0].chatId);
          }
        }
      } catch (error) {
        console.log('No existing chat found, starting fresh');
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, [userDetails?._id, currentUser?._id]);

  // Initialize socket connection
  useEffect(() => {
    if (token && currentUser && actualChatId) {
      const socket = socketService.connect(token);

      if (socket) {
        // Join the chat room
        socketService.joinChat(actualChatId, currentUser._id, (response) => {
          if (response.success) {
            console.log('Joined chat successfully');
          }
        });

        // Listen for new messages
        socket.on('chat-messages', (messageData: any) => {
          const newMessage = {
            id: messageData._id,
            text: messageData.message,
            timestamp: new Date(messageData.createdAt),
            isOwn: messageData.senderId === currentUser._id
          };
          setMessages(prev => [...prev, newMessage]);
        });
      }
    }

    return () => {
      if (actualChatId) {
        socketService.disconnect();
      }
    };
  }, [token, currentUser, actualChatId]);

  const handleSendMessage = async () => {
    console.log('=== SEND MESSAGE STARTED ===');
    console.log('Input value:', inputValue);
    console.log('Selected file:', selectedFile);
    console.log('Current user:', currentUser);
    console.log('User details:', userDetails);

    if ((!inputValue.trim() && !selectedFile) || !currentUser || !userDetails) {
      console.log('❌ Send message aborted - missing required data');
      return;
    }

    const messageText = inputValue.trim();
    const tempId = `temp-${Date.now()}`;

    console.log('Message text after trim:', messageText);
    console.log('Generated temp ID:', tempId);

    // Add message to UI immediately
    const tempMessage = {
      id: tempId,
      text: messageText,
      timestamp: new Date(),
      isOwn: true,
      pending: true,
      attachment: selectedFile ? {
        name: selectedFile.name,
        size: selectedFile.size,
        type: selectedFile.type
      } : undefined
    };
    
    console.log('📝 Temp message created:', tempMessage);
    setMessages(prev => [...prev, tempMessage]);
    setInputValue('');
    
    // setSelectedFile(null);

    try {
      let attachmentData = null;
      
      // If there's a file, upload it first
      if (selectedFile) {
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        // Upload file to your backend first
        const uploadResponse = await Axios.post('/chats/upload-image-attachment', formData, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (uploadResponse.status === 200) {
          attachmentData = uploadResponse.data;
        }
      }

      // Initialize socket connection if not connected
      if (token && currentUser) {
        console.log('🔌 Initializing socket connection...');
        console.log('Token exists:', !!token);
        console.log('Current user:', currentUser);
        
        const socket = socketService.connect(token);

        if (socket) {
          console.log('✅ Socket connected successfully');
          
          // Prepare message payload for backend
          const messagePayload = {
            accountId: userDetails._id,
            message: messageText,
            token: token,
            type: 'message',
            attachment: attachmentData ? {
              ...attachmentData,
              name: selectedFile?.name,
              size: selectedFile?.size,
              type: selectedFile?.type
            } : undefined
          };

          console.log('📤 SENDING MESSAGE VIA SOCKET:');
          console.log('Full payload:', JSON.stringify(messagePayload, null, 2));
          console.log('Payload size:', JSON.stringify(messagePayload).length, 'characters');

          // Send via socket - this will create the chat if it doesn't exist
          socketService.sendPrivateMessage(messagePayload, (response) => {
            console.log('📥 BACKEND RESPONSE RECEIVED:');
            console.log('Raw response:', response);
            console.log('Response type:', typeof response);
            console.log('Response keys:', Object.keys(response));
            console.log('Success status:', response.success);
            console.log('Response message:', response.message);
            console.log('Chat ID from response:', response.chatId);

            if (response.success) {
              console.log('✅ Message sent successfully');
              console.log('Backend message data:', response.message);
              
              // Update the temp message with real data from backend
              setMessages(prev => prev.map(msg => {
                if (msg.id === tempId) {
                  const updatedMsg = {
                    ...msg,
                    id: response.message?._id || `backend-${Date.now()}`,
                    pending: false
                  };
                  console.log('📝 Updated message:', updatedMsg);
                  return updatedMsg;
                }
                return msg;
              }));

              // Set the actual chat ID for future messages
              if (response.chatId) {
                console.log('💾 Setting actual chat ID:', response.chatId);
                setActualChatId(response.chatId);
              }
            } else {
              console.log('❌ Backend returned failure');
              console.log('Error message:', response.message);
              
              // Remove failed message and show error
              setMessages(prev => prev.filter(msg => msg.id !== tempId));
              console.error('Failed to send message:', response.message);
              alert('Failed to send message: ' + response.message);
            }
          });
        } else {
          console.log('❌ Socket connection failed');
          // Socket connection failed
          setMessages(prev => prev.filter(msg => msg.id !== tempId));
          console.error('Socket connection failed');
          alert('Unable to connect to chat server. Please try again.');
        }
      } else {
        console.log('❌ Missing authentication');
        console.log('Token exists:', !!token);
        console.log('Current user exists:', !!currentUser);
        
        // No token or user
        setMessages(prev => prev.filter(msg => msg.id !== tempId));

        console.error('No authentication token or user');
        alert('Please log in to send messages.');
      }
    } catch (error) {
      console.log('❌ EXCEPTION CAUGHT:');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);
      
      // Remove failed message
      setMessages(prev => prev.filter(msg => msg.id !== tempId));
      alert('Error sending message. Please try again.');
    }
    
    console.log('=== SEND MESSAGE COMPLETED ===');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setInputValue(prev => prev + emoji);
  };

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);

  }
  // Get user display name
  const getUserName = () => {
    if (userDetails) {
      return `${userDetails.firstName || userDetails.first_name || ''} ${userDetails.lastName || userDetails.last_name || ''}`.trim();
    }
    if (chat?.accountId) {
      return `${chat.accountId.firstName || chat.accountId.first_name || ''} ${chat.accountId.lastName || chat.accountId.last_name || ''}`.trim();
    }
    return 'Unknown User';
  };

  const getProfilePicture = () => {
    return userDetails?.profilePicture || userDetails?.profile_picture || 
           chat?.accountId?.profilePicture || chat?.accountId?.profile_picture || '';
  };

  return (
    <div className="flex flex-col" style={{height: '41.99vw'}}>
      {/* Header */}
      <div className="flex items-center justify-between px-[1rem] rounded-tr-lg rounded-br-lg py-[5px] bg-[#E9E9E9]">
        <div className="flex items-center ">
          {/* Profile Picture */}
          <div className='gap-[12px] flex items-center'>
            <div className="w-[36px] h-[36px] rounded-full  flex items-center justify-center overflow-hidden">
              <Avatar
                size={36}
                type="user"
                profilePicture={getProfilePicture()}
                customDefault="thumbnail"
              />
            </div>
            
            <div>
              <h3 className={`${poppinsMedium.className} text-[#696969] text-[14px]`}>
                {getUserName()}
              </h3>
            </div>
          </div>
        </div>

        {/* Search Button */}
        <div className="flex items-center">
          <Button 
            onClick={() => setIsSearchOpen(!isSearchOpen)}
            className="hover:bg-gray-200"
          >
            <Search className="h-[18px] w-[18px]" color='#979797'/>
          </Button>
          <Button>
            <MoreVertical className="h-[18px] w-[18px]" color='#4F4F4F'/>
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      {isSearchOpen && (
        <div className="bg-white border-b border-gray-200 px-[1rem] py-2">
          <div className="flex items-center gap-2">
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search messages..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
              {searchResults.length > 0 && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">
                  {currentSearchIndex + 1} of {searchResults.length}
                </div>
              )}
            </div>
            
            {/* Navigation buttons */}
            {searchResults.length > 0 && (
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateSearch('prev')}
                  className="p-1 h-8 w-8"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateSearch('next')}
                  className="p-1 h-8 w-8"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
            )}
            
            {/* Close button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={closeSearch}
              className="p-1 h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Search results info */}
          {searchQuery && (
            <div className="mt-2 text-xs text-gray-500">
              {searchResults.length > 0 
                ? `Found ${searchResults.length} message${searchResults.length === 1 ? '' : 's'}`
                : 'No messages found'
              }
            </div>
          )}
        </div>
      )}

      {/* Messages Area */}
      <div className="flex-1 flex w-full justify-center overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-full w-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className={`${poppinsRegular.className} text-gray-500`}>
                Loading messages...
              </p>
            </div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex h-full">
            <div className="text-center">
              <p className={`${poppinsRegular.className} text-gray-500 mb-2`}>
                No messages yet. Start the conversation!
              </p>
              <p className={`${poppinsRegular.className} text-sm text-gray-400`}>
                Type a message below to begin chatting with {getUserName()}
              </p>
            </div>
          </div>
        ) : (
          <div className="mt-[1.5625rem] w-[80%] flex flex-col space-y-4">
            {messages.map((message, index) => (
              <div
                key={message.id}
                id={`message-${message.id}`}
                className={`flex flex-col ${message.isOwn ? 'items-end' : 'items-start'} ${
                  highlightedMessageId === message.id ? 'bg-yellow-100 p-2 rounded-lg' : ''
                }`}
              >
                <div className='inline-flex'>
                  <div
                    className={`rounded-tr-[2rem] flex items-end p-[1rem] rounded-br-[2rem] rounded-bl-[2rem] ${
                      message.isOwn
                        ? `bg-[#F9FAFB] text-[#5A5E5C] text-[12px] ${poppinsMedium.className} ${message.pending ? 'opacity-70' : ''}`
                        : 'bg-[#E8E8E8] text-[#5A5E5C] text-[12px] '
                    } ${poppinsMedium.className}`}
                  >
                    <div className={poppinsRegular.className}>
                      {message.text && (
                        <p>{searchQuery ? highlightText(message.text, searchQuery) : message.text}</p>
                      )}
                      
                      {/* Render attachment if present */}
                      {message.attachment && (
                        <div className="mt-2 p-2 bg-gray-100 rounded">
                          <div className="flex items-center gap-2">
                            <Paperclip className="h-4 w-4" />
                            <span className="text-sm">{message.attachment.name}</span>
                            <span className="text-xs text-gray-500">
                              ({(message.attachment.size / 1024 / 1024).toFixed(2)} MB)
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between ">
                  <p className={`${
                    message.isOwn ? 'text-[#696969]' : 'text-[#696969] '
                  } ${poppinsItalic.className} text-[12px]`}>
                    {message.timestamp.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                  {message.pending && (
                    <div className="ml-2">
                      <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent"></div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Input Area - Always Visible */}
      <div className="bg-white pl-[2.38vw] pr-[2.64vw] w-full">
        {/* Selected File Preview */}
        {selectedFile && (
          <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Paperclip className="h-4 w-4 text-blue-600" />
                <span className={`${poppinsRegular.className} text-sm text-blue-800`}>
                  {selectedFile.name}
                </span>
                <span className={`${poppinsRegular.className} text-xs text-blue-600`}>
                  ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedFile(null)}
                className="p-1 text-blue-600 hover:text-blue-800"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Input Area */}
     <div className=" w-full items-center gap-[1rem] flex">
            <form 
              onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }}
              className="flex items-center w-full"
            >
     <EmojiPicker onEmojiSelect={handleEmojiSelect} />
    
              <div className="relative w-full">
             
              <input
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyPress}
                placeholder={`Message ${chat.accountId.firstName || chat.accountId.first_name}...`}
                className="border w-full border-[#F2F2F2] bg-[#FBFAF7] rounded-lg p-[12px] focus:outline-none"
              />
                  <div className="absolute top-2 right-5">
                            <SelectAttachment onFileSelect={handleFileSelect} />
    
                </div>
               </div>
            </form>
            <div>
    
           
            
            <Button
              onClick={() => handleSendMessage()}
              disabled={!inputValue.trim()}
              className="p-[12px] rounded-[10px] bg-[#163E23] "
            >
              <SendIcon  width={15} height={15}/>
            </Button>
             </div>
          </div>
      </div>
    </div>
  );
};

export default SimpleChatRoom;
"use client"

import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Send, Users, MoreVertical, UserPlus, Settings, Smile, Paperclip, X, Phone, Video, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import { useSelector } from 'react-redux';
import { userAuthInfo, userToken } from '@/redux/user/reducer';
import { socketService } from '@/services/socket';
import { group as groupAPI } from '@/api/group';
import { Loading } from '@/components/ui/loading';
import EmojiPicker from '@/components/ui/emoji-picker';
import SelectAttachment from '@/components/ui/select-attachment';

interface GroupMessage {
  _id: string;
  message: string;
  senderId: string;
  createdAt: string | Date;
  user?: {
    _id: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
  type?: string;
  attachment?: string;
  attachmentType?: string;
  system?: boolean;
  pending?: boolean;
}

interface GroupChatRoomProps {
  group: {
    _id: string;
    name: string;
    description?: string;
    image?: string;
    participants: any[];
    admins?: string[];
    memberCount?: number;
  };
  onBack: () => void;
  isLoading?: boolean;
}

export const GroupChatRoom: React.FC<GroupChatRoomProps> = ({
  group,
  onBack,
  isLoading: groupLoading = false
}) => {
  const [messages, setMessages] = useState<GroupMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [inputValue, setInputValue] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentUser = useSelector(userAuthInfo);
  const token = useSelector(userToken);

  // Initialize socket connection for group
  useEffect(() => {
    if (token && currentUser && group._id) {
      const socket = socketService.connect(token);

      if (socket) {
        // Join the group chat room using the correct socket method
        socketService.joinGroup(group._id, (response) => {
          if (response.success) {
            console.log('Joined group successfully:', response.message);
          } else {
            console.error('Failed to join group:', response.message);
          }
        });

        // Listen for new group messages
        socketService.onGroupMessage((messageData: any) => {
          const newMessage: GroupMessage = {
            _id: messageData._id,
            message: messageData.message,
            senderId: messageData.senderId?._id || messageData.senderId,
            createdAt: messageData.createdAt,
            user: messageData.senderId ? {
              _id: messageData.senderId._id || messageData.senderId,
              first_name: messageData.senderId.first_name,
              last_name: messageData.senderId.last_name,
              profile_picture: messageData.senderId.profile_picture
            } : undefined,
            type: messageData.type || 'message',
            system: messageData.system || false
          };
          setMessages(prev => [newMessage, ...prev]);
          scrollToBottom();
        });
      }

      return () => {
        // Clean up socket listeners
        socketService.offGroupMessage();
      };
    }
  }, [token, currentUser, group._id]);

  // Load group messages
  useEffect(() => {
    const loadMessages = async () => {
      if (!group._id) return;

      try {
        setLoading(true);
        
        // Fetch group messages from API
        const response = await groupAPI.getGroupChats({
          groupId: group._id,
          page: 1,
          limit: 50
        });

        if (response && response.messages) {
          const formattedMessages = response.messages.docs.map((msg: any) => ({
            _id: msg._id,
            message: msg.message,
            senderId: msg.senderId?._id || msg.senderId,
            createdAt: msg.createdAt,
            user: msg.senderId ? {
              _id: msg.senderId._id || msg.senderId,
              first_name: msg.senderId.first_name,
              last_name: msg.senderId.last_name,
              profile_picture: msg.senderId.profile_picture
            } : undefined,
            type: msg.type || 'message',
            system: msg.system || false
          }));
          
          setMessages(formattedMessages.reverse());
        }
      } catch (error) {
        console.error('Error loading group messages:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [group._id]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle emoji selection
  const handleEmojiSelect = (emoji: string) => {
    setInputValue(prev => prev + emoji);
  };

  // Handle file selection
  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  // Remove selected file
  const removeSelectedFile = () => {
    setSelectedFile(null);
  };

  const handleSendMessage = async () => {
    if ((!inputValue.trim() && !selectedFile) || !currentUser || !group._id) return;

    const messageText = inputValue.trim();
    const tempId = `temp-${Date.now()}`;

    // Add message to UI immediately
    const tempMessage: GroupMessage = {
      _id: tempId,
      message: messageText,
      senderId: currentUser._id,
      createdAt: new Date(),
      user: {
        _id: currentUser._id,
        first_name: currentUser.first_name,
        last_name: currentUser.last_name,
        profile_picture: currentUser.profile_picture
      },
      pending: true,
      attachment: selectedFile?.name,
      attachmentType: selectedFile?.type
    };

    setMessages(prev => [tempMessage, ...prev]);
    setInputValue('');
    setSelectedFile(null);

    try {
      // Initialize socket connection if not connected
      if (token && currentUser) {
        const socket = socketService.connect(token);

        if (socket) {
          // Send group message via socket using the correct format
          socketService.sendGroupMessage({
            groupId: group._id,
            message: messageText,
            token: token,
            type: 'message'
          }, (response) => {
            if (response.success) {
              // Remove the pending message (it will be replaced by the real one from socket)
              setMessages(prev => prev.filter(msg => msg._id !== tempId));
            } else {
              // Remove failed message and show error
              setMessages(prev => prev.filter(msg => msg._id !== tempId));
              console.error('Failed to send group message:', response.message);
            }
          });
        }
      }
    } catch (error) {
      // Remove failed message and show error
      setMessages(prev => prev.filter(msg => msg._id !== tempId));
      console.error('Failed to send group message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: string | Date) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatMessage = (message: GroupMessage) => {
    const isOwnMessage = message.senderId === currentUser?._id;
    const isSystemMessage = message.system;

    if (isSystemMessage) {
      return (
        <div key={message._id} className="flex justify-center my-2">
          <div className="bg-gray-100 text-gray-600 text-sm px-3 py-1 rounded-full">
            {message.message}
          </div>
        </div>
      );
    }

    return (
      <div key={message._id} className={`flex mb-4 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
        {!isOwnMessage && (
          <Avatar
            source={message.user?.profile_picture}
            size={32}
            profilePicture={message.user?.profile_picture}
          />
        )}
        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
          isOwnMessage 
            ? 'bg-[#F2875D] text-white ml-3' 
            : 'bg-gray-100 text-gray-900 mr-3'
        }`}>
          {!isOwnMessage && (
            <div className="text-xs font-medium mb-1">
              {message.user?.first_name} {message.user?.last_name}
            </div>
          )}
          <div className="text-sm">{message.message}</div>
          <div className={`text-xs mt-1 ${isOwnMessage ? 'text-orange-100' : 'text-gray-500'}`}>
            {formatTime(message.createdAt)}
            {message.pending && (
              <span className="ml-1">Sending...</span>
            )}
          </div>
        </div>
        {isOwnMessage && (
          <Avatar
            source={currentUser?.profile_picture}
            size={32}
            profilePicture={currentUser?.profile_picture}
          />
        )}
      </div>
    );
  };

  if (loading || groupLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loading size="lg" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header - Matching Private Chat Style */}
      <div className="flex items-center justify-between bg-[#E9E9E9] rounded-[10px] py-[5px] px-[1.25rem] mb-4">
        <div className="flex items-center">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center ml-3">
            {group.image ? (
              <img src={group.image} alt="" className="w-full h-full rounded-full object-cover" />
            ) : (
              <Users className="w-5 h-5 text-white" />
            )}
          </div>
          <div className="ml-3">
            <h3 className={`${poppinsMedium.className} text-[14px] text-[#696969]`}>
              {group.name}
            </h3>
            <p className={`${poppinsRegular.className} text-xs text-gray-500`}>
              {group.memberCount || group.participants?.length || 0} members
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Video className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Search className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-4 pb-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <Users className="w-12 h-12 text-gray-300 mb-4" />
            <p className={`${poppinsRegular.className} text-gray-500`}>
              No messages yet. Start the conversation!
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {messages.map(formatMessage)}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-600 text-sm px-3 py-2 rounded-lg">
                  Someone is typing...
                </div>
              </div>
            )}
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input - Matching Private Chat Style */}
      <div className="border-t bg-white p-4">
        {/* Selected File Preview */}
        {selectedFile && (
          <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Paperclip className="h-4 w-4 text-blue-600" />
                <span className={`${poppinsRegular.className} text-sm text-blue-800`}>
                  {selectedFile.name}
                </span>
                <span className={`${poppinsRegular.className} text-xs text-blue-600`}>
                  ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={removeSelectedFile}
                className="p-1 text-blue-600 hover:text-blue-800"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="flex items-end gap-2">
          {/* Emoji Picker */}
          <EmojiPicker onEmojiSelect={handleEmojiSelect} />

          {/* File Attachment */}
          <SelectAttachment onFileSelect={handleFileSelect} />

          {/* Text Input */}
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className={`w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${poppinsRegular.className}`}
              rows={1}
              style={{ minHeight: '44px', maxHeight: '120px' }}
            />
          </div>

          {/* Send Button */}
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() && !selectedFile}
            className="p-3 bg-[#F2875D] hover:bg-[#E07A52] text-white rounded-lg"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GroupChatRoom;

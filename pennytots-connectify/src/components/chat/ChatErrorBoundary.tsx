'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert<PERSON>riangle, RefreshCw, MessageCircle } from 'lucide-react';
import { poppinsSemibold, poppinsRegular } from '@/fonts';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

export class ChatErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Chat Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to external service if needed
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // Here you could send the error to an external logging service
    // like Sentry, LogRocket, etc.
    console.log('Logging error to service:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    });
  };

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));
    } else {
      // Max retries reached, could redirect or show different UI
      console.warn('Max retries reached for chat error boundary');
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private renderErrorDetails = () => {
    if (process.env.NODE_ENV !== 'development') {
      return null;
    }

    return (
      <details className="mt-4">
        <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
          Error Details (Development Only)
        </summary>
        <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono">
          <div className="mb-2">
            <strong>Error:</strong> {this.state.error?.message}
          </div>
          <div className="mb-2">
            <strong>Stack:</strong>
            <pre className="whitespace-pre-wrap">{this.state.error?.stack}</pre>
          </div>
          {this.state.errorInfo && (
            <div>
              <strong>Component Stack:</strong>
              <pre className="whitespace-pre-wrap">{this.state.errorInfo.componentStack}</pre>
            </div>
          )}
        </div>
      </details>
    );
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <AlertTriangle className="h-12 w-12 text-red-500" />
              </div>
              <CardTitle className={`${poppinsSemibold.className} text-lg text-gray-900`}>
                Chat Error
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className={`${poppinsRegular.className} text-gray-600`}>
                Something went wrong with the chat functionality. This might be a temporary issue.
              </p>
              
              <div className="flex flex-col gap-2">
                {this.state.retryCount < this.maxRetries ? (
                  <Button 
                    onClick={this.handleRetry}
                    className="w-full"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again ({this.maxRetries - this.state.retryCount} attempts left)
                  </Button>
                ) : (
                  <Button 
                    onClick={this.handleReload}
                    className="w-full"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reload Page
                  </Button>
                )}
                
                <Button 
                  variant="outline"
                  onClick={() => window.history.back()}
                  className="w-full"
                >
                  Go Back
                </Button>
              </div>

              <div className="text-xs text-gray-500">
                <p>Retry count: {this.state.retryCount}/{this.maxRetries}</p>
                <p>Error ID: {Date.now()}</p>
              </div>

              {this.renderErrorDetails()}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundary
export const withChatErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ChatErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ChatErrorBoundary>
  );

  WrappedComponent.displayName = `withChatErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Specific error boundary for chat rooms
export const ChatRoomErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ChatErrorBoundary
      fallback={
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className={`${poppinsRegular.className} text-gray-600`}>
              Unable to load chat room
            </p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        </div>
      }
    >
      {children}
    </ChatErrorBoundary>
  );
};

// Error boundary for chat list
export const ChatListErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ChatErrorBoundary
      fallback={
        <div className="p-4 text-center">
          <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className={`${poppinsRegular.className} text-gray-600 mb-2`}>
            Unable to load chat list
          </p>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.location.reload()}
          >
            Refresh
          </Button>
        </div>
      }
    >
      {children}
    </ChatErrorBoundary>
  );
};

export default ChatErrorBoundary;

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Users, MessageCircle, Plus } from 'lucide-react';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import { chat as chatAPI } from '@/api/chat';
import { groups as groupsAPI } from '@/api/groups';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';

interface Chat {
  _id: string;
  accountId: {
    _id: string;
    first_name?: string;
    last_name?: string;
    firstName?: string;
    lastName?: string;
    profile_picture?: string;
    profilePicture?: string;
  };
  lastMessage?: {
    message: string;
    createdAt: string;
  };
  unreadCount?: number;
  muted?: boolean;
  blocked?: boolean;
}

interface Group {
  _id: string;
  name: string;
  description?: string;
  profilePicture?: string;
  participants: string[];
  lastMessage?: {
    message: string;
    createdAt: string;
  };
  unreadCount?: number;
}

interface MessagesSidebarProps {
  onChatSelect: (chat: Chat) => void;
  onGroupSelect: (group: Group) => void;
  selectedChatId?: string;
  selectedGroupId?: string;
}

export const MessagesSidebar: React.FC<MessagesSidebarProps> = ({
  onChatSelect,
  onGroupSelect,
  selectedChatId,
  selectedGroupId
}) => {
  const [privateChats, setPrivateChats] = useState<Chat[]>([]);
  const [groupChats, setGroupChats] = useState<Group[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('private');
  
  const currentUser = useSelector(userAuthInfo);

  // Load chats and groups
  useEffect(() => {
    const loadChatsAndGroups = async () => {
      try {
        setIsLoading(true);
        
        // Load private chats
        const chatsData = await chatAPI.getChats();
        if (chatsData && Array.isArray(chatsData)) {
          setPrivateChats(chatsData);
        }
        
        // Load group chats
        const groupsData = await groupsAPI.getMyGroups();
        if (groupsData && Array.isArray(groupsData)) {
          setGroupChats(groupsData);
        }
      } catch (error) {
        console.error('Error loading chats and groups:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadChatsAndGroups();
  }, []);

  // Filter chats based on search query
  const filteredPrivateChats = privateChats.filter(chat => {
    const name = `${chat.accountId.first_name || chat.accountId.firstName || ''} ${chat.accountId.last_name || chat.accountId.lastName || ''}`.toLowerCase();
    return name.includes(searchQuery.toLowerCase());
  });

  const filteredGroupChats = groupChats.filter(group => {
    return group.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const ChatItem: React.FC<{ chat: Chat; isSelected: boolean; onClick: () => void }> = ({ 
    chat, 
    isSelected, 
    onClick 
  }) => {
    const name = `${chat.accountId.first_name || chat.accountId.firstName || ''} ${chat.accountId.last_name || chat.accountId.lastName || ''}`.trim();
    const profilePicture = chat.accountId.profile_picture || chat.accountId.profilePicture;
    
    return (
      <div
        onClick={onClick}
        className={`p-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 ${
          isSelected ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
        }`}
      >
        <div className="flex items-center gap-3">
          <div className="relative">
            <Avatar
              source={profilePicture}
              size={48}
              profilePicture={profilePicture}
            />
            {chat.unreadCount && chat.unreadCount > 0 && (
              <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full">
                {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
              </Badge>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className={`${poppinsMedium.className} text-sm text-gray-900 truncate`}>
                {name || 'Unknown User'}
              </h4>
              {chat.lastMessage && (
                <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                  {formatTime(chat.lastMessage.createdAt)}
                </span>
              )}
            </div>
            
            {chat.lastMessage && (
              <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate mt-1`}>
                {chat.lastMessage.message}
              </p>
            )}
            
            {chat.muted && (
              <span className="text-xs text-gray-400 mt-1">🔇 Muted</span>
            )}
          </div>
        </div>
      </div>
    );
  };

  const GroupItem: React.FC<{ group: Group; isSelected: boolean; onClick: () => void }> = ({ 
    group, 
    isSelected, 
    onClick 
  }) => {
    return (
      <div
        onClick={onClick}
        className={`p-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 ${
          isSelected ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
        }`}
      >
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
              {group.profilePicture ? (
                <img
                  src={group.profilePicture}
                  alt=""
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <Users className="w-6 h-6 text-white" />
              )}
            </div>
            {group.unreadCount && group.unreadCount > 0 && (
              <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full">
                {group.unreadCount > 99 ? '99+' : group.unreadCount}
              </Badge>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className={`${poppinsMedium.className} text-sm text-gray-900 truncate`}>
                {group.name}
              </h4>
              {group.lastMessage && (
                <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                  {formatTime(group.lastMessage.createdAt)}
                </span>
              )}
            </div>
            
            <div className="flex items-center justify-between mt-1">
              {group.lastMessage ? (
                <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate`}>
                  {group.lastMessage.message}
                </p>
              ) : (
                <p className={`${poppinsRegular.className} text-sm text-gray-400`}>
                  No messages yet
                </p>
              )}
              
              <span className={`${poppinsRegular.className} text-xs text-gray-400`}>
                {group.participants.length} members
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="w-80 bg-white border-r border-gray-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className={`${poppinsRegular.className} text-gray-500`}>Loading chats...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className={`${poppinsSemibold.className} text-lg text-gray-900 mb-3`}>
          Messages
        </h2>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 m-4 mb-0">
          <TabsTrigger value="private" className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4" />
            Private ({filteredPrivateChats.length})
          </TabsTrigger>
          <TabsTrigger value="groups" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Groups ({filteredGroupChats.length})
          </TabsTrigger>
        </TabsList>

        {/* Private Chats */}
        <TabsContent value="private" className="flex-1 overflow-y-auto mt-0">
          {filteredPrivateChats.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  {searchQuery ? 'No chats found' : 'No private chats yet'}
                </p>
              </div>
            </div>
          ) : (
            <div>
              {filteredPrivateChats.map((chat) => (
                <ChatItem
                  key={chat._id}
                  chat={chat}
                  isSelected={selectedChatId === chat._id}
                  onClick={() => onChatSelect(chat)}
                />
              ))}
            </div>
          )}
        </TabsContent>

        {/* Group Chats */}
        <TabsContent value="groups" className="flex-1 overflow-y-auto mt-0">
          {filteredGroupChats.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Users className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  {searchQuery ? 'No groups found' : 'No group chats yet'}
                </p>
              </div>
            </div>
          ) : (
            <div>
              {filteredGroupChats.map((group) => (
                <GroupItem
                  key={group._id}
                  group={group}
                  isSelected={selectedGroupId === group._id}
                  onClick={() => onGroupSelect(group)}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MessagesSidebar;

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { poppinsRegular } from '@/fonts';
import { 
  Send, 
  Paperclip, 
  Smile, 
  Image as ImageIcon, 
  Camera, 
  FileText, 
  Mic,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

interface MessageInputProps {
  onSendMessage: (message: string, attachment?: File, attachmentType?: string) => void;
  onTyping?: () => void;
  onStoppedTyping?: () => void;
  disabled?: boolean;
  placeholder?: string;
  quotedMessage?: any;
  onClearQuote?: () => void;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onTyping,
  onStoppedTyping,
  disabled = false,
  placeholder = "Type a message...",
  quotedMessage,
  onClearQuote
}) => {
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [attachmentType, setAttachmentType] = useState<string>('');
  const [isTyping, setIsTyping] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle typing indicators
  useEffect(() => {
    if (message.trim() && !isTyping) {
      setIsTyping(true);
      onTyping?.();
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        onStoppedTyping?.();
      }
    }, 1000);

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [message, isTyping, onTyping, onStoppedTyping]);

  const handleSend = () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage && !selectedFile) return;

    onSendMessage(trimmedMessage, selectedFile || undefined, attachmentType || undefined);
    setMessage('');
    setSelectedFile(null);
    setAttachmentType('');
    setIsTyping(false);
    onStoppedTyping?.();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileSelect = (type: string) => {
    setAttachmentType(type);
    setShowAttachmentMenu(false);
    
    // Create file input with appropriate accept attribute
    const input = document.createElement('input');
    input.type = 'file';
    
    switch (type) {
      case 'image':
        input.accept = 'image/*';
        break;
      case 'video':
        input.accept = 'video/*';
        break;
      case 'audio':
        input.accept = 'audio/*';
        break;
      case 'document':
        input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';
        break;
      default:
        input.accept = '*/*';
    }

    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setSelectedFile(file);
      }
    };

    input.click();
  };

  const handleEmojiSelect = (emoji: any) => {
    const newMessage = message + emoji.native;
    setMessage(newMessage);
    setShowEmojiPicker(false);
    
    // Focus back to textarea
    textareaRef.current?.focus();
  };

  const removeAttachment = () => {
    setSelectedFile(null);
    setAttachmentType('');
  };

  const renderQuotedMessage = () => {
    if (!quotedMessage) return null;

    return (
      <div className="p-3 bg-gray-100 border-l-4 border-blue-500 mb-2 rounded">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <p className={`text-xs text-gray-600 ${poppinsRegular.className} font-medium`}>
              Replying to {quotedMessage.user?.first_name} {quotedMessage.user?.last_name}
            </p>
            <p className={`text-sm text-gray-800 ${poppinsRegular.className} mt-1`}>
              {quotedMessage.message}
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearQuote}
            className="p-1 h-6 w-6"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  };

  const renderAttachmentPreview = () => {
    if (!selectedFile) return null;

    return (
      <div className="p-3 bg-gray-50 border rounded mb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {attachmentType === 'image' && <ImageIcon className="h-4 w-4 text-blue-500" />}
            {attachmentType === 'video' && <Camera className="h-4 w-4 text-green-500" />}
            {attachmentType === 'audio' && <Mic className="h-4 w-4 text-purple-500" />}
            {attachmentType === 'document' && <FileText className="h-4 w-4 text-red-500" />}
            <span className={`text-sm ${poppinsRegular.className}`}>
              {selectedFile.name}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={removeAttachment}
            className="p-1 h-6 w-6"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="border-t bg-white p-4">
      {renderQuotedMessage()}
      {renderAttachmentPreview()}
      
      <div className="flex items-end gap-2">
        {/* Attachment Menu */}
        <DropdownMenu open={showAttachmentMenu} onOpenChange={setShowAttachmentMenu}>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="p-2">
              <Paperclip className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={() => handleFileSelect('image')}>
              <ImageIcon className="h-4 w-4 mr-2" />
              Image
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleFileSelect('video')}>
              <Camera className="h-4 w-4 mr-2" />
              Video
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleFileSelect('audio')}>
              <Mic className="h-4 w-4 mr-2" />
              Audio
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleFileSelect('document')}>
              <FileText className="h-4 w-4 mr-2" />
              Document
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Message Input */}
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            className={`${poppinsRegular.className} resize-none min-h-[40px] max-h-[120px] pr-10`}
            rows={1}
          />
          
          {/* Emoji Picker Button */}
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1"
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
          >
            <Smile className="h-4 w-4" />
          </Button>
        </div>

        {/* Send Button */}
        <Button
          onClick={handleSend}
          disabled={disabled || (!message.trim() && !selectedFile)}
          className="p-2"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <div className="absolute bottom-full right-4 mb-2 z-50">
          <Picker
            data={data}
            onEmojiSelect={handleEmojiSelect}
            theme="light"
            previewPosition="none"
            skinTonePosition="none"
          />
        </div>
      )}
    </div>
  );
};

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Search, Users, MessageCircle, ArrowLeft } from 'lucide-react';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import { chat as chatAPI } from '@/api/chat';
import { groups as groupsAPI } from '@/api/groups';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';
import { SimpleChatRoom } from './SimpleChatRoom';
import { PrivateChatRoom } from './PrivateChatRoom';

interface GroupChat {
  _id: string;
  name: string;
  description?: string;
  profilePicture?: string;
  participants: any[];
  lastMessage?: {
    message: string;
    createdAt: string;
  };
  unreadCount?: number;
}

interface PrivateChat {
  _id: string;
  accountId: {
    _id: string;
    first_name?: string;
    last_name?: string;
    firstName?: string;
    lastName?: string;
    profile_picture?: string;
    profilePicture?: string;
  };
  lastMessage?: {
    message: string;
    createdAt: string;
  };
  unreadCount?: number;
  muted?: boolean;
  blocked?: boolean;
}

interface MessagesLayoutProps {
  onBack: () => void;
}

export const MessagesLayout: React.FC<MessagesLayoutProps> = ({ onBack }) => {
  const [groupChats, setGroupChats] = useState<GroupChat[]>([]);
  const [privateChats, setPrivateChats] = useState<PrivateChat[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<GroupChat | null>(null);
  const [selectedPrivateChat, setSelectedPrivateChat] = useState<PrivateChat | null>(null);
  const [groupSearchQuery, setGroupSearchQuery] = useState('');
  const [privateSearchQuery, setPrivateSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  
  const currentUser = useSelector(userAuthInfo);

  // Load group chats and private chats
  useEffect(() => {
    const loadChats = async () => {
      try {
        setIsLoading(true);
        
        // Load group chats (mock data for now)
        const mockGroups: GroupChat[] = [
          {
            _id: 'group-1',
            name: 'Tech Discussions',
            description: 'General tech talk',
            participants: ['user1', 'user2', 'user3'],
            lastMessage: {
              message: 'What do you think about the new framework?',
              createdAt: new Date().toISOString()
            },
            unreadCount: 3
          },
          {
            _id: 'group-2',
            name: 'Project Team',
            description: 'Project coordination',
            participants: ['user1', 'user4', 'user5'],
            lastMessage: {
              message: 'Meeting at 3 PM today',
              createdAt: new Date(Date.now() - 3600000).toISOString()
            },
            unreadCount: 1
          },
          {
            _id: 'group-3',
            name: 'Random Chat',
            description: 'Casual conversations',
            participants: ['user1', 'user2', 'user6'],
            lastMessage: {
              message: 'Anyone free for lunch?',
              createdAt: new Date(Date.now() - 7200000).toISOString()
            }
          }
        ];
        setGroupChats(mockGroups);
        
        // Load private chats from API
        try {
          const chatsData = await chatAPI.getChats();
          if (chatsData && Array.isArray(chatsData)) {
            setPrivateChats(chatsData);
          }
        } catch (error) {
          console.log('No private chats found or error loading:', error);
          // Set mock private chats for demo
          const mockPrivateChats: PrivateChat[] = [
            {
              _id: 'private-1',
              accountId: {
                _id: 'user-1',
                first_name: 'John',
                last_name: 'Doe',
                profile_picture: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
              },
              lastMessage: {
                message: 'Hey, how are you?',
                createdAt: new Date().toISOString()
              },
              unreadCount: 2
            },
            {
              _id: 'private-2',
              accountId: {
                _id: 'user-2',
                first_name: 'Jane',
                last_name: 'Smith',
                profile_picture: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face'
              },
              lastMessage: {
                message: 'Thanks for the help!',
                createdAt: new Date(Date.now() - 1800000).toISOString()
              }
            }
          ];
          setPrivateChats(mockPrivateChats);
        }
      } catch (error) {
        console.error('Error loading chats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadChats();
  }, []);

  // Filter chats based on search
  const filteredGroupChats = groupChats.filter(group =>
    group.name.toLowerCase().includes(groupSearchQuery.toLowerCase())
  );

  const filteredPrivateChats = privateChats.filter(chat => {
    const name = `${chat.accountId.first_name || chat.accountId.firstName || ''} ${chat.accountId.last_name || chat.accountId.lastName || ''}`.toLowerCase();
    return name.includes(privateSearchQuery.toLowerCase());
  });

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const GroupChatItem: React.FC<{ group: GroupChat; isSelected: boolean; onClick: () => void }> = ({ 
    group, 
    isSelected, 
    onClick 
  }) => (
    <div
      onClick={onClick}
      className={`p-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 ${
        isSelected ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
      }`}
    >
      <div className="flex items-center gap-3">
        <div className="relative">
          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
            {group.profilePicture ? (
              <img src={group.profilePicture} alt="" className="w-full h-full rounded-full object-cover" />
            ) : (
              <Users className="w-6 h-6 text-white" />
            )}
          </div>
          {group.unreadCount && group.unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full">
              {group.unreadCount > 99 ? '99+' : group.unreadCount}
            </Badge>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className={`${poppinsMedium.className} text-sm text-gray-900 truncate`}>
              {group.name}
            </h4>
            {group.lastMessage && (
              <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                {formatTime(group.lastMessage.createdAt)}
              </span>
            )}
          </div>
          
          <div className="flex items-center justify-between mt-1">
            {group.lastMessage ? (
              <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate`}>
                {group.lastMessage.message}
              </p>
            ) : (
              <p className={`${poppinsRegular.className} text-sm text-gray-400`}>
                No messages yet
              </p>
            )}
            
            <span className={`${poppinsRegular.className} text-xs text-gray-400`}>
              {group.participants.length} members
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const PrivateChatItem: React.FC<{ chat: PrivateChat; isSelected: boolean; onClick: () => void }> = ({ 
    chat, 
    isSelected, 
    onClick 
  }) => {
    const name = `${chat.accountId.first_name || chat.accountId.firstName || ''} ${chat.accountId.last_name || chat.accountId.lastName || ''}`.trim();
    const profilePicture = chat.accountId.profile_picture || chat.accountId.profilePicture;
    
    return (
      <div
        onClick={onClick}
        className={`p-3 cursor-pointer hover:bg-gray-50 border-b border-gray-100 ${
          isSelected ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
        }`}
      >
        <div className="flex items-center gap-3">
          <div className="relative">
            <Avatar source={profilePicture} size={48} profilePicture={profilePicture} />
            {chat.unreadCount && chat.unreadCount > 0 && (
              <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full">
                {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
              </Badge>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className={`${poppinsMedium.className} text-sm text-gray-900 truncate`}>
                {name || 'Unknown User'}
              </h4>
              {chat.lastMessage && (
                <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                  {formatTime(chat.lastMessage.createdAt)}
                </span>
              )}
            </div>
            
            {chat.lastMessage && (
              <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate mt-1`}>
                {chat.lastMessage.message}
              </p>
            )}
            
            {chat.muted && (
              <span className="text-xs text-gray-400 mt-1">🔇 Muted</span>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className={`${poppinsRegular.className} text-gray-500`}>Loading chats...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Group Chats Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center gap-3 mb-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h2 className={`${poppinsSemibold.className} text-lg text-gray-900`}>
              Group Chats
            </h2>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search groups..."
              value={groupSearchQuery}
              onChange={(e) => setGroupSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {filteredGroupChats.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Users className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  {groupSearchQuery ? 'No groups found' : 'No group chats yet'}
                </p>
              </div>
            </div>
          ) : (
            <div>
              {filteredGroupChats.map((group) => (
                <GroupChatItem
                  key={group._id}
                  group={group}
                  isSelected={selectedGroup?._id === group._id}
                  onClick={() => setSelectedGroup(group)}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Private Chats Panel */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className={`${poppinsSemibold.className} text-lg text-gray-900 mb-3`}>
            Private Chats
            {selectedGroup && (
              <span className={`${poppinsRegular.className} text-sm text-gray-500 block`}>
                in {selectedGroup.name}
              </span>
            )}
          </h2>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search private chats..."
              value={privateSearchQuery}
              onChange={(e) => setPrivateSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {!selectedGroup ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  Select a group to see private chats
                </p>
              </div>
            </div>
          ) : filteredPrivateChats.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                <p className={`${poppinsRegular.className} text-gray-500`}>
                  {privateSearchQuery ? 'No private chats found' : 'No private chats yet'}
                </p>
              </div>
            </div>
          ) : (
            <div>
              {filteredPrivateChats.map((chat) => (
                <PrivateChatItem
                  key={chat._id}
                  chat={chat}
                  isSelected={selectedPrivateChat?._id === chat._id}
                  onClick={() => setSelectedPrivateChat(chat)}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1">
        {selectedPrivateChat ? (
          <SimpleChatRoom
            chat={{
              _id: selectedPrivateChat._id,
              accountId: selectedPrivateChat.accountId,
              messages: [],
              isNewChat: false
            }}
            onBack={() => setSelectedPrivateChat(null)}
            userDetails={selectedPrivateChat.accountId}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className={`${poppinsMedium.className} text-lg text-gray-600 mb-2`}>
                Select a private chat to start messaging
              </h3>
              <p className={`${poppinsRegular.className} text-gray-500`}>
                Choose a group first, then select a private chat to begin the conversation
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessagesLayout;

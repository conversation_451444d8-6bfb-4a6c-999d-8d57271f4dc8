'use client';

import React from 'react';
import { Loader2, MessageCircle, Send, Upload, Wifi, WifiOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { poppinsRegular, poppinsMedium } from '@/fonts';

// Generic loading spinner
export const LoadingSpinner: React.FC<{ 
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  );
};

// Chat list loading skeleton
export const ChatListSkeleton: React.FC = () => {
  return (
    <div className="space-y-3 p-4">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="flex items-center space-x-3 p-3 rounded-lg">
          <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
          <div className="h-3 bg-gray-200 rounded animate-pulse w-12" />
        </div>
      ))}
    </div>
  );
};

// Message loading skeleton
export const MessageSkeleton: React.FC<{ isOwnMessage?: boolean }> = ({ 
  isOwnMessage = false 
}) => {
  return (
    <div className={`flex mb-4 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
      {!isOwnMessage && (
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse mr-3" />
      )}
      <div className="space-y-2 max-w-xs">
        <div className={`h-10 bg-gray-200 rounded-lg animate-pulse ${
          isOwnMessage ? 'bg-blue-200' : 'bg-gray-200'
        }`} />
        <div className="h-3 bg-gray-200 rounded animate-pulse w-16" />
      </div>
      {isOwnMessage && (
        <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse ml-3" />
      )}
    </div>
  );
};

// Chat room loading state
export const ChatRoomLoading: React.FC = () => {
  return (
    <div className="flex flex-col h-full">
      {/* Header skeleton */}
      <div className="flex items-center p-4 border-b">
        <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse mr-3" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse w-32" />
          <div className="h-3 bg-gray-200 rounded animate-pulse w-20" />
        </div>
      </div>

      {/* Messages skeleton */}
      <div className="flex-1 p-4 space-y-4">
        <MessageSkeleton />
        <MessageSkeleton isOwnMessage />
        <MessageSkeleton />
        <MessageSkeleton isOwnMessage />
      </div>

      {/* Input skeleton */}
      <div className="p-4 border-t">
        <div className="h-10 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  );
};

// Connection status indicator
export const ConnectionStatus: React.FC<{
  status: 'connected' | 'connecting' | 'disconnected' | 'error';
  onRetry?: () => void;
}> = ({ status, onRetry }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          icon: <Wifi className="h-4 w-4 text-green-500" />,
          text: 'Connected',
          color: 'text-green-600',
          bgColor: 'bg-green-50 border-green-200'
        };
      case 'connecting':
        return {
          icon: <LoadingSpinner size="sm" className="text-blue-500" />,
          text: 'Connecting...',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 border-blue-200'
        };
      case 'disconnected':
        return {
          icon: <WifiOff className="h-4 w-4 text-yellow-500" />,
          text: 'Disconnected',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50 border-yellow-200'
        };
      case 'error':
        return {
          icon: <WifiOff className="h-4 w-4 text-red-500" />,
          text: 'Connection Error',
          color: 'text-red-600',
          bgColor: 'bg-red-50 border-red-200'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`flex items-center justify-between p-2 border rounded-md ${config.bgColor}`}>
      <div className="flex items-center gap-2">
        {config.icon}
        <span className={`text-sm ${config.color} ${poppinsRegular.className}`}>
          {config.text}
        </span>
      </div>
      {(status === 'disconnected' || status === 'error') && onRetry && (
        <Button
          variant="outline"
          size="sm"
          onClick={onRetry}
          className="h-6 px-2 text-xs"
        >
          Retry
        </Button>
      )}
    </div>
  );
};

// Upload progress indicator
export const UploadProgress: React.FC<{
  fileName: string;
  progress: number;
  onCancel?: () => void;
}> = ({ fileName, progress, onCancel }) => {
  return (
    <Card className="mb-2">
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Upload className="h-4 w-4 text-blue-500" />
            <span className={`text-sm ${poppinsRegular.className} truncate`}>
              {fileName}
            </span>
          </div>
          {onCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          )}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
        <div className="flex justify-between mt-1">
          <span className="text-xs text-gray-500">Uploading...</span>
          <span className="text-xs text-gray-500">{progress}%</span>
        </div>
      </CardContent>
    </Card>
  );
};

// Typing indicator
export const TypingIndicator: React.FC<{
  users: string[];
  maxDisplay?: number;
}> = ({ users, maxDisplay = 2 }) => {
  if (users.length === 0) return null;

  const displayUsers = users.slice(0, maxDisplay);
  const remainingCount = users.length - maxDisplay;

  const getText = () => {
    if (users.length === 1) {
      return `${displayUsers[0]} is typing...`;
    } else if (users.length === 2) {
      return `${displayUsers[0]} and ${displayUsers[1]} are typing...`;
    } else {
      return `${displayUsers.join(', ')}${remainingCount > 0 ? ` and ${remainingCount} others` : ''} are typing...`;
    }
  };

  return (
    <div className="flex items-center gap-2 p-2 text-sm text-gray-500">
      <div className="flex gap-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
      </div>
      <span className={poppinsRegular.className}>
        {getText()}
      </span>
    </div>
  );
};

// Empty state for chat list
export const EmptyChatList: React.FC<{
  onStartChat?: () => void;
}> = ({ onStartChat }) => {
  return (
    <div className="flex flex-col items-center justify-center h-64 p-8 text-center">
      <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
      <h3 className={`${poppinsMedium.className} text-lg text-gray-600 mb-2`}>
        No conversations yet
      </h3>
      <p className={`${poppinsRegular.className} text-gray-500 mb-4`}>
        Start a conversation by clicking on a user's profile
      </p>
      {onStartChat && (
        <Button onClick={onStartChat} variant="outline">
          Start a Chat
        </Button>
      )}
    </div>
  );
};

// Retry component for failed operations
export const RetryComponent: React.FC<{
  message: string;
  onRetry: () => void;
  isLoading?: boolean;
}> = ({ message, onRetry, isLoading = false }) => {
  return (
    <div className="flex flex-col items-center justify-center p-4 text-center">
      <p className={`${poppinsRegular.className} text-gray-600 mb-3`}>
        {message}
      </p>
      <Button 
        onClick={onRetry} 
        disabled={isLoading}
        variant="outline"
        size="sm"
      >
        {isLoading ? (
          <>
            <LoadingSpinner size="sm" className="mr-2" />
            Retrying...
          </>
        ) : (
          'Try Again'
        )}
      </Button>
    </div>
  );
};

'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { MessageBubble } from './MessageBubble';
import { MessageInput } from './MessageInput';
import { TypingIndicator } from './ChatLoadingStates';
import useChatKeyboardShortcuts from '@/hooks/useChatKeyboardShortcuts';
import { poppinsRegular, poppinsMedium } from '@/fonts';
import { 
  ArrowLeft, 
  MoreVertical, 
  Phone, 
  Video, 
  Info,
  VolumeX,
  Volume2,
  Keyboard
} from 'lucide-react';

interface AccessibleChatRoomProps {
  chat: any;
  messages: any[];
  onSendMessage: (message: string, attachment?: File, attachmentType?: string) => void;
  onBack: () => void;
  onTyping?: () => void;
  onStoppedTyping?: () => void;
  typingUsers?: string[];
  userDetails?: any;
  isLoading?: boolean;
}

export const AccessibleChatRoom: React.FC<AccessibleChatRoomProps> = ({
  chat,
  messages,
  onSendMessage,
  onBack,
  onTyping,
  onStoppedTyping,
  typingUsers = [],
  userDetails,
  isLoading = false
}) => {
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const [announcements, setAnnouncements] = useState<string[]>([]);
  const [soundEnabled, setSoundEnabled] = useState(true);

  // Keyboard shortcuts
  const {
    shortcuts,
    showHelpDialog,
    setShowHelpDialog,
    formatShortcut
  } = useChatKeyboardShortcuts({
    inputRef: messageInputRef,
    containerRef: messagesContainerRef,
    shortcuts: [
      {
        key: 'ArrowUp',
        ctrlKey: true,
        description: 'Go to previous message',
        action: () => {
          // Focus previous message for screen readers
          focusMessage('previous');
        }
      },
      {
        key: 'ArrowDown',
        ctrlKey: true,
        description: 'Go to next message',
        action: () => {
          // Focus next message for screen readers
          focusMessage('next');
        }
      },
      {
        key: 'Home',
        ctrlKey: true,
        description: 'Go to first message',
        action: () => {
          focusMessage('first');
        }
      },
      {
        key: 'End',
        ctrlKey: true,
        description: 'Go to last message',
        action: () => {
          focusMessage('last');
        }
      },
      {
        key: 'r',
        ctrlKey: true,
        description: 'Reply to selected message',
        action: () => {
          // Implement reply functionality
          console.log('Reply to message');
        }
      }
    ]
  });

  // Focus management for messages
  const focusMessage = (direction: 'previous' | 'next' | 'first' | 'last') => {
    const messageElements = messagesContainerRef.current?.querySelectorAll('[data-message-id]');
    if (!messageElements || messageElements.length === 0) return;

    const currentFocused = document.activeElement;
    let targetIndex = 0;

    if (currentFocused && currentFocused.hasAttribute('data-message-id')) {
      const currentIndex = Array.from(messageElements).indexOf(currentFocused as Element);
      
      switch (direction) {
        case 'previous':
          targetIndex = Math.max(0, currentIndex - 1);
          break;
        case 'next':
          targetIndex = Math.min(messageElements.length - 1, currentIndex + 1);
          break;
        case 'first':
          targetIndex = 0;
          break;
        case 'last':
          targetIndex = messageElements.length - 1;
          break;
      }
    } else {
      // If no message is focused, focus based on direction
      targetIndex = direction === 'first' || direction === 'previous' ? 0 : messageElements.length - 1;
    }

    const targetElement = messageElements[targetIndex] as HTMLElement;
    if (targetElement) {
      targetElement.focus();
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  // Announce new messages for screen readers
  useEffect(() => {
    if (messages.length > 0) {
      const latestMessage = messages[0];
      if (!latestMessage.pending) {
        const announcement = `New message from ${latestMessage.user.first_name}: ${latestMessage.message}`;
        setAnnouncements(prev => [...prev, announcement]);
        
        // Play sound notification if enabled
        if (soundEnabled) {
          playNotificationSound();
        }
      }
    }
  }, [messages, soundEnabled]);

  // Clear announcements after they've been read
  useEffect(() => {
    if (announcements.length > 0) {
      const timer = setTimeout(() => {
        setAnnouncements([]);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [announcements]);

  const playNotificationSound = () => {
    try {
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = 0.3;
      audio.play().catch(() => {
        // Ignore errors if sound can't be played
      });
    } catch (error) {
      // Ignore errors
    }
  };

  const getUserDisplayName = () => {
    if (!userDetails) return 'Unknown User';
    return `${userDetails.firstName || userDetails.first_name || ''} ${userDetails.lastName || userDetails.last_name || ''}`.trim();
  };

  return (
    <div 
      className="flex flex-col h-full"
      role="main"
      aria-label={`Chat with ${getUserDisplayName()}`}
    >
      {/* Screen reader announcements */}
      <div 
        aria-live="polite" 
        aria-atomic="true" 
        className="sr-only"
      >
        {announcements.map((announcement, index) => (
          <div key={index}>{announcement}</div>
        ))}
      </div>

      {/* Header */}
      <header className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            aria-label="Go back to chat list"
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          
          <div className="flex items-center gap-3">
            <div 
              className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center"
              aria-hidden="true"
            >
              {userDetails?.profilePicture ? (
                <img
                  src={userDetails.profilePicture}
                  alt=""
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <span className="text-sm font-bold text-gray-600">
                  {getUserDisplayName().charAt(0)}
                </span>
              )}
            </div>
            <div>
              <h1 className={`${poppinsMedium.className} text-lg text-gray-900`}>
                {getUserDisplayName()}
              </h1>
              <p className={`${poppinsRegular.className} text-sm text-gray-500`}>
                {typingUsers.length > 0 ? 'Typing...' : 'Online'}
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSoundEnabled(!soundEnabled)}
            aria-label={soundEnabled ? 'Disable sound notifications' : 'Enable sound notifications'}
            className="p-2"
          >
            {soundEnabled ? (
              <Volume2 className="h-5 w-5" />
            ) : (
              <VolumeX className="h-5 w-5" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowHelpDialog(true)}
            aria-label="Show keyboard shortcuts"
            className="p-2"
          >
            <Keyboard className="h-5 w-5" />
          </Button>
        </div>
      </header>

      {/* Messages */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
        role="log"
        aria-label="Chat messages"
        aria-live="polite"
        tabIndex={0}
      >
        {isLoading ? (
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message, index) => (
            <div
              key={message._id}
              data-message-id={message._id}
              tabIndex={0}
              role="article"
              aria-label={`Message from ${message.user.first_name} at ${new Date(message.createdAt).toLocaleTimeString()}`}
              className="focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
            >
              <MessageBubble
                message={message}
                isOwnMessage={message.senderId === chat.accountId._id}
              />
            </div>
          ))
        )}
        
        {typingUsers.length > 0 && (
          <TypingIndicator users={typingUsers} />
        )}
      </div>

      {/* Message Input */}
      <div className="border-t bg-white">
        <MessageInput
          onSendMessage={onSendMessage}
          onTyping={onTyping}
          onStoppedTyping={onStoppedTyping}
          placeholder={`Message ${getUserDisplayName()}...`}
          chatId={chat._id}
        />
      </div>

      {/* Keyboard Shortcuts Dialog */}
      <Dialog open={showHelpDialog} onOpenChange={setShowHelpDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Keyboard Shortcuts</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            {shortcuts
              .filter(shortcut => !shortcut.disabled)
              .map((shortcut, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className={`${poppinsRegular.className} text-sm`}>
                    {shortcut.description}
                  </span>
                  <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">
                    {formatShortcut(shortcut)}
                  </kbd>
                </div>
              ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AccessibleChatRoom;

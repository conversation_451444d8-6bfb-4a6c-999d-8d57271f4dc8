'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { ArrowLeft, MoreVertical, Phone, Video } from 'lucide-react';
import { poppinsSemibold, poppinsRegular } from '@/fonts';
import { MessageBubble } from './MessageBubble';
import { MessageInput } from './MessageInput';
import socketService from '@/services/socket';
import { useSelector } from 'react-redux';
import { userAuthInfo, userToken } from '@/redux/user/reducer';
import { chat as chatAPI } from '@/api/chat';
import { v4 as uuidv4 } from 'uuid';

interface ChatItem {
  _id: string;
  accountId: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  lastMessage?: {
    message: string;
    createdAt: string;
  };
  unreadCount?: number;
  muted?: boolean;
  blocked?: boolean;
}

interface Message {
  _id: string;
  message: string;
  senderId: string;
  createdAt: string | Date;
  user: {
    _id: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
  type?: string;
  attachment?: string;
  attachmentType?: string;
  quotedReply?: any;
  pending?: boolean;
}

interface PrivateChatRoomProps {
  chat: ChatItem;
  onBack: () => void;
}

export const PrivateChatRoom: React.FC<PrivateChatRoomProps> = ({
  chat,
  onBack
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [quotedMessage, setQuotedMessage] = useState<Message | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentUser = useSelector(userAuthInfo);
  const token = useSelector(userToken);

  // Initialize socket connection
  useEffect(() => {
    if (token) {
      const socket = socketService.connect(token);
      
      // Join the chat
      socketService.joinChat(chat._id, currentUser._id, (response) => {
        if (!response.success) {
          console.error('Failed to join chat:', response.message);
        }
      });

      // Listen for new messages
      socketService.onPrivateMessage((newMessage) => {
        setMessages(prev => [formatMessage(newMessage), ...prev]);
        scrollToBottom();
      });

      // Listen for typing indicators
      socketService.onUserTyping((data) => {
        if (data.userId !== currentUser._id) {
          setIsTyping(true);
        }
      });

      socketService.onUserStoppedTyping((data) => {
        if (data.userId !== currentUser._id) {
          setIsTyping(false);
        }
      });

      return () => {
        socketService.offPrivateMessage();
        socketService.offUserTyping();
        socketService.offUserStoppedTyping();
      };
    }
  }, [chat._id, currentUser._id, token]);

  // Load initial messages
  useEffect(() => {
    loadMessages();
  }, []);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await chatAPI.getUserChats({
        userId: chat.accountId._id,
        pageNumber: 1,
        limit: 20
      });

      if (response.success && response.chats?.docs) {
        const formattedMessages = response.chats.docs.map(formatMessage);
        setMessages(formattedMessages);
        setHasMoreMessages(response.chats.hasNextPage);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatMessage = (msg: any): Message => {
    return {
      _id: msg._id,
      message: msg.message || '',
      senderId: msg.senderId,
      createdAt: msg.createdAt,
      user: {
        _id: msg.senderId,
        first_name: msg.senderId === currentUser._id ? currentUser.first_name : chat.accountId.firstName,
        last_name: msg.senderId === currentUser._id ? currentUser.last_name : chat.accountId.lastName,
        profile_picture: msg.senderId === currentUser._id ? currentUser.profile_picture : chat.accountId.profilePicture,
      },
      type: msg.type,
      attachment: msg.attachment,
      attachmentType: msg.attachmentType,
      quotedReply: msg.quotedReply,
      pending: msg.pending || false,
    };
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (messageText: string, attachment?: File, attachmentType?: string) => {
    if (!messageText.trim() && !attachment) return;

    const localId = uuidv4();
    const now = new Date();

    // Create local message
    const localMessage: Message = {
      _id: localId,
      message: messageText,
      senderId: currentUser._id,
      createdAt: now,
      user: {
        _id: currentUser._id,
        first_name: currentUser.first_name,
        last_name: currentUser.last_name,
        profile_picture: currentUser.profile_picture,
      },
      pending: true,
      ...(quotedMessage && { quotedReply: quotedMessage }),
    };

    // Add to messages immediately
    setMessages(prev => [localMessage, ...prev]);
    setQuotedMessage(null);

    // Prepare message for server
    const messageToSend = {
      chatId: chat._id,
      accountId: chat.accountId._id,
      message: messageText,
      type: attachmentType || 'message',
      token: token,
      localId,
      clientCreatedAt: now,
      ...(quotedMessage && { quotedData: quotedMessage }),
    };

    // Send via socket
    socketService.sendPrivateMessage(messageToSend, (response) => {
      if (!response.success) {
        // Remove the pending message on error
        setMessages(prev => prev.filter(msg => msg._id !== localId));
        console.error('Failed to send message:', response.message);
      } else {
        // Update the local message with server response
        setMessages(prev => 
          prev.map(msg => 
            msg._id === localId 
              ? { ...msg, _id: response.message._id, pending: false }
              : msg
          )
        );
      }
    });

    scrollToBottom();
  };

  const handleReply = (message: Message) => {
    setQuotedMessage(message);
  };

  const handleTyping = () => {
    socketService.emitTyping(chat._id, false);
  };

  const handleStoppedTyping = () => {
    socketService.emitStoppedTyping(chat._id, false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F2875D]"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Avatar
            source={chat.accountId.profilePicture}
            size={40}
            profilePicture={chat.accountId.profilePicture}
          />
          <div>
            <h3 className={`${poppinsSemibold.className} text-lg`}>
              {chat.accountId.firstName} {chat.accountId.lastName}
            </h3>
            {isTyping && (
              <p className={`${poppinsRegular.className} text-sm text-green-500`}>
                Typing...
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Video className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className={`${poppinsRegular.className} text-gray-500`}>
              No messages yet. Start the conversation!
            </p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageBubble
                key={message._id}
                message={message}
                isOwnMessage={message.senderId === currentUser._id}
                onReply={handleReply}
              />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        onTyping={handleTyping}
        onStoppedTyping={handleStoppedTyping}
        quotedMessage={quotedMessage}
        onClearQuote={() => setQuotedMessage(null)}
      />
    </div>
  );
};

import React from 'react';
import Avatar from '../ui/avatar';
import { poppinsMedium, poppinsRegular } from '@/fonts';
import { formatDistanceToNow } from 'date-fns';

interface ITicket {
  profilePicture: string;
  firstName: string;
  lastName: string;
  title: string;
  message: string;
  createdAt: string;
  ticketId: string;
  category: string;
}

const TicketCard: React.FC<{ ticket: ITicket, selected: boolean }> = ({ ticket, selected }) => {


  const timeAgo = (dateString: string) => {
    const now = new Date();
    const past = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  return (
    <div
      className={`p-[0.875rem] cursor-pointer transition-colors ${selected ? 'bg-[#E9E9E9]' : 'bg-white'
        }`}
    >
      <div className="flex gap-[10px]">
        <div>
          <Avatar
            size={36}
            type="user"
            profilePicture={ticket?.profilePicture}
            customDefault="thumbnail"
          />
        </div>
        <div className="flex w-full justify-between">
          <div className="flex flex-col ">
            <p className={`text-[#1F1E1A] text-[14px] ${poppinsMedium.className}`}>{ticket.title}</p>
            <h4 className={`${poppinsRegular.className} text-[#696969] text-[12px]`}>Category: <span className={`${poppinsMedium}`}>{ticket.category}</span></h4>
          </div>

          <span className={`${poppinsRegular.className} text-[#696969] text-[12px]`}>
            {timeAgo(ticket.createdAt)}
          </span>



        </div>
      </div>
    </div>
  );
};

export default TicketCard;

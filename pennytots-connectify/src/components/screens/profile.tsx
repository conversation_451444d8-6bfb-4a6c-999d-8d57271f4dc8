'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';
import { user } from '@/api/user';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loading } from '@/components/ui/loading';
import Image from 'next/image';

import { poppinsMedium, poppinsRegular, poppinsSemibold } from '@/fonts';
import BriefcaseIcon from '@/assets/svg/briefcase.svg';
import BioIcon from '@/assets/svg/bio.svg';

import LinkIcon from '@/assets/svg/link.svg';
import LocationIcon from '@/assets/svg/location.svg';
import { useFetchTopic } from '@/redux/topic/hooks';
import { useFetchGroups } from '@/redux/group/hooks';
import CommentIcon from "@/assets/svg/comment.svg";
import ThumbSVG from '../svgReactComponents/ThumbSVG';
import { Button } from '../ui/button';
import { ImageViewer } from '@/components/ui/image-viewer';
import EditProfileForm from './edit-profile';



interface ProfileData {
  _id: string;
  first_name: string;
  last_name: string;
  bio?: string;
  profile_picture?: string;
  header_image?: string;
  website?: string;
  facebook?: string;
  linkedin?: string;
  company?: string;
  company_position?: string;
  city?: string;
  state?: string;
  age?: number;
  gender?: string;
  graduation_status?: string;
  subscriptionType?: string;
  interests?: Array<{_id: string; name: string}>;
  followers_count?: number;
  following_count?: number;
  posts_count?: number;
  groups_count?: number;
}



const ProfileScreen = () => {
  const router = useRouter();
  const currentUser = useSelector(userAuthInfo);
  console.log('Current user:', currentUser);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [activeTab, setActiveTab] = useState<'posts' | 'groups'>('posts');
  const [loading, setLoading] = useState(true);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>('');
    const [isEditing, setIsEditing] = useState(false);

  // Use the current user as userInfo for the hooks
  const userInfo = currentUser;
  const { data: userTopics, isLoading: isLoadingTopics } = useFetchTopic(userInfo);
  const { data: userGroups, isLoading: isLoadingGroups } = useFetchGroups(userInfo);
  console.log('User topics:', userTopics);

  useEffect(() => {
    fetchProfileData();
  }, []);

  useEffect(() => {
    console.log('Profile data state updated:', profileData);
  }, [profileData]);

  const handleEditClick =()=> {
    setIsEditing(true);
  }

   const handleBackClick = () => {
    setIsEditing(false);
  };

  const handleProfileUpdate = (updatedData: any) => {
    // Handle the profile update
    console.log('Profile updated:', updatedData);
    setIsEditing(false); // Go back to profile view after successful update
  };

  if (isEditing) {
    return (
      <EditProfileForm 
        onBack={handleBackClick}
        onUpdate={handleProfileUpdate}
      />
    );
  }


  const fetchProfileData = async () => {
    try {
      setLoading(true);

      // Fetch user profile
      const profileResponse = await user.profile();
      console.log(profileResponse, 'profileResponse');
      console.log('graduation_status from API:', profileResponse?.graduation_status);
      setProfileData(profileResponse);
    } catch (error) {
      console.error('Error fetching profile data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <p>Profile not found</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full items-center justify-center " >
    <div className="w-full"  >
      {/* Header Image */}
      <div className="relative mb-[24px] rounded-tl-[12px] rounded-tr-[12px] rounded-bl-[39px] rounded-br-[12px] overflow-hidden">
        {profileData.header_image ? (
          <Image
            src={profileData.header_image}
            alt="Header"
            width={485}
            height={207}
            className="w-full h-full object-cover"
          />
        ) : (
          <img
            src="/default-header-img.png"
            alt="Default Header"
            className="w-full h-full object-cover"
          />
        )}

        {/* Profile Picture Overlay */}
        <div className="w-[80px] h-[80px] bottom-0 left-0 absolute rounded-full bg-gray-300 flex items-center justify-center overflow-hidden border-4 border-white">
          {profileData.profile_picture ? (
            <img
              src={profileData.profile_picture}
              alt="Profile image"
              className="w-full h-full object-cover"
            />
          ) : (
            <img
              src="/connectify-default-thumbnail.svg"
              alt="Default Profile"
              className="w-full h-full object-cover"
            />
          )}
        </div>
      </div>
     

      {/* Profile Info */}
      <Card>
        <CardContent className="">
          <div className="flex flex-col ">
         
             {/* Profile Details */}
                   <div className="flex items-center justify-between w-full">
              <div className="">
                <h1 className={`text-[#5A5E5C] text-[1rem] ${poppinsSemibold.className}`}>
                  {profileData.first_name} {profileData.last_name}
                </h1>
                {profileData?.graduation_status && (
                  <p className={`${poppinsRegular.className} text-[#696969] text-[11px]`}>{profileData.graduation_status}</p>
                )}
                 {profileData?.subscriptionType && (
                  <p className={`${poppinsRegular.className} text-[#696969] text-[11px]`}>{profileData.subscriptionType}</p>
                )}
              </div>
               <Button
          type="button"
          className={`border cursor-pointer border-[#F56630] rounded-[12px] py-[4px] px-[1rem] text-[12px] text-[#48463E] ${poppinsSemibold.className} hover:bg-[#F56630] hover:text-white transition-colors duration-200`}
          onClick={() => handleEditClick()}
        >
          Edit Profile
        </Button>
</div>

           
            <div className="flex  w-full flex-col mt-[1rem]">
    

<div className='flex gap-[8px] flex-col mb-[9px]'>


              {/* Additional Info */}
              <div className="flex flex-col gap-[8px]">
                {profileData.company && profileData.company_position && (
                  <div className="flex items-center gap-[8px]">
                   <BriefcaseIcon className="" />
                    <span className={`${poppinsRegular.className} text-[#48463E] text-[14px]`}>{profileData.company_position} at {profileData.company}</span>
                  </div>
                )}
                {profileData.bio && profileData.bio && (
                  <div className="flex items-center gap-[8px]">
                    <BioIcon className="" />
            
                    <span className={`${poppinsMedium.className} text-[#4F4F4F] text-[14px]`}>{profileData.bio}, {profileData.bio}</span>
                  </div>
                )}
                {(profileData.city || profileData.state) && (
                  <div className="flex items-center gap-[8px]">
                    <LocationIcon className="" />
                    <span className={`${poppinsRegular.className} text-[#696969] text-[14px]`}>{profileData.city}{profileData.city && profileData.state && ', '}{profileData.state}</span>
                  </div>
                )}
               
              </div>

           
              <div className="flex items-center gap-[4px]">
                                <LinkIcon className="w-5 h-5" />
                <span className={`${poppinsRegular.className} text-[#696969] text-[14px]`}>Public links</span>

              </div>
</div>

   {/* Social Links */}
              <div className="flex flex-col gap-[6px]">
            {profileData.website && (
              <span className="inline-flex items-center gap-1">
                <a
                  href={profileData.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800"
                >
                  {profileData.website}
                </a>
              </span>
            )}
                {profileData.facebook && (
                  <a
                    href={profileData.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <span className="inline-flex items-center gap-1">
                      {profileData.facebook}
                    </span>
                  </a>
                )}
                {profileData.linkedin && (
                  <a
                    href={profileData.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {profileData.linkedin}
                  </a>
                )}
              </div>

            
            </div>
            </div>
       
        </CardContent>
      </Card>

      {/* Tabs */}
      <div className="flex items-center justify-center mt-[24px]">
        <nav className="flex">
          <button
            onClick={() => setActiveTab('posts')}
            className={`p-[1rem] border-b-2 font-medium text-[#F56630] ${
              activeTab === 'posts'
                ? 'border-[#F56630] text-[#F56630]'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Posts ({userTopics?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('groups')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'groups'
                ? 'border-[#F56630] text-[#F56630]'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Groups ({userGroups?.length || 0})
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="">
        {activeTab === 'posts' && (
          <div>
            {isLoadingTopics ? (
              <div className="flex justify-center py-8">
                <Loading />
              </div>
            ) : userTopics && userTopics.length > 0 ? (
              <div className="bg-white">
                {userTopics.map((post: any) => (
                  <div key={post._id} className="w-full flex flex-col border-b-2 border-[#F2F2F2]">
                    <div className="w-full flex flex-row  bg-white py-[1rem] gap-[20px]">
                      {/* Avatar Section */}
                      <div className="">
                        <div className="">
                          <div className="w-[48px] h-[48px] rounded-full overflow-hidden bg-gray-300 flex items-center justify-center">
                            {post.userId?.profile_picture ? (
                              <img
                                src={post.userId.profile_picture}
                                alt={`${post.userId.first_name} ${post.userId.last_name}`}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <img
                                src="/connectify-default-thumbnail.svg"
                                alt="Default Profile"
                                className="w-full h-full object-cover"
                              />
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Content Section */}
                      <div className="w-full flex flex-col">
                        {/* Header */}
                        <div className="flex flex-row items-center justify-between">
                          <div className="flex flex-row">
                            <div className="justify-center">
                              <h3 className={`text-[#5A5E5C] text-[1rem] ${poppinsSemibold.className}`}>
                                {`${post?.userId?.first_name} ${post?.userId?.last_name}`.slice(0, 35)}
                                {(post?.userId?.first_name?.length + post?.userId?.last_name?.length > 35) ? '...' : ''}
                              </h3>
                            </div>
                          </div>
                        </div>

                        {/* Position at Company */}
                        <div className="w-full">
                          <p className={`text-[#696969] text-[14px] ${poppinsMedium.className}`}>
                            {post?.userId?.company_position && post?.userId?.company
                              ? `${post.userId.company_position?.replace(/(\r\n|\n|\r)/gm, "") || "position"} at ${post.userId.company}`
                              : "Position at Company"}
                          </p>
                        </div>

                        {/* User Info */}
                        <div className="w-full">
                          <p className={`text-[#696969] text-[14px] ${poppinsMedium.className}`}>
                            {post?.userId?.subscriptionType
                              ? `${post.userId.subscriptionType.charAt(0).toUpperCase() + post.userId.subscriptionType.slice(1)} Member`
                              : 'Free Member'}
                          </p>
                        </div>

                        {/* Time */}
                        <div className="flex items-start">
                          <p className={`text-[#696969] text-[14px] mt-[4px] ${poppinsMedium.className}`}>
                            {new Date(post.createdAt).toLocaleDateString()}
                          </p>
                        </div>

                        {/* Post Content */}
                        <div className="mt-[8px]">
                          <p className={`text-[#48463E] text-[16px] ${poppinsRegular.className}`}>
                            {post.content}
                          </p>
                          {post.image && (
                            <div className="mt-[8px]">
                              <img
                                src={post.image}
                                alt="Post image"
                                className="rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"
                                onClick={() => {
                                  setSelectedImage(post.image!);
                                  setShowImageViewer(true);
                                }}
                              />
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="flex mt-[24px] items-center justify-between w-full">
                            <span className='flex items-center gap-[8px]'>   <CommentIcon
                                          width={24}
                                          height={24}
                                          className="comment-icon"
                                          alt="comment svg"
                                        />{post.comments || 0}</span>


                          <span className='flex items-center gap-[8px]'> <ThumbSVG color={post.like ? "#FE3233" : "#5A5E5C"} />
              <span className={` ${poppinsMedium.className} text-[14px]  ${post.like ? "text-[#FE3233]" : "text-[#5A5E5C]"
                }  `}> {post.like || 0} Like{(post.like || 0) !== 1 ? "s" : ""} </span>
          
</span>
                        
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <div className="w-12 h-12  text-gray-400 mb-4">📝</div>
                  <p className="text-gray-500">No posts yet</p>
                  <p className="text-sm text-gray-400 mt-2">Share your first post to get started!</p>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {activeTab === 'groups' && (
          <div>
            {isLoadingGroups ? (
              <div className="flex justify-center">
                <Loading />
              </div>
            ) : userGroups && userGroups.length > 0 ? (
              <div className="bg-white">
                {userGroups.map((group: any) => (
                  <div key={group._id} className="w-full flex flex-col border-b-2 border-[#F2F2F2]">
                    <div className="w-full flex flex-row  bg-white py-[1rem] gap-[20px]">
                      {/* Group Image Section */}
                      <div className="flex">
                        <div className="">
                          <div className="w-[48px] h-[48px] rounded-lg overflow-hidden bg-gray-300 flex items-center justify-center">
                            {group.image ? (
                              <img
                                src={group.image}
                                alt={group.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-300 flex items-center justify-center text-2xl text-gray-600">
                                👥
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Content Section */}
                      <div className="w-full flex flex-col">
                        {/* Group Name */}
                        <div className="flex flex-row items-center justify-between">
                          <div className="flex flex-row">
                            <div className="justify-center">
                              <h3 className={`text-[#5A5E5C] text-[1rem] ${poppinsSemibold.className}`}>
                                {group.name}
                              </h3>
                            </div>
                          </div>
                        </div>

                        {/* Group Description */}
                        {group.description && (
                          <div className="w-full">
                            <p className={`text-[#696969] text-[14px] ${poppinsMedium.className}`}>
                              {group.description}
                            </p>
                          </div>
                        )}

                        {/* Group Info */}
                        <div className="w-full">
                          <p className={`text-[#696969] text-[14px] ${poppinsMedium.className}`}>
                            👥 {group.members_count || 0} members
                            {group.isPrivate && (
                              <Badge variant="secondary" className="ml-2 text-xs">
                                Private
                              </Badge>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <div className="w-12 h-12  text-gray-400 mb-4">👥</div>
                  <p className="text-gray-500">No groups joined yet</p>
                  <p className="text-sm text-gray-400 mt-2">Join groups to connect with like-minded people!</p>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>

    {/* Edit Profile Modal */}
    

    {/* Image Viewer */}
    <ImageViewer
      isOpen={showImageViewer}
      onClose={() => setShowImageViewer(false)}
      imageUrl={selectedImage}
      alt="Post image"
    />
    </div>
  );
};
export default ProfileScreen;

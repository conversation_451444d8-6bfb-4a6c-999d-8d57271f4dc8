"use client"

import React, { useState, useEffect } from 'react';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import {
  Search,
  MessageCircle,
  MoreVertical,
  UserX,
  VolumeX,
  Volume2,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Avatar from '../ui/avatar';
import { useChatActions } from '@/redux/chat/hooks';
import { chat as chatAPI } from '@/api/chat';
import { useSelector } from 'react-redux';
import { userAuthInfo, userId as currentUserId } from '@/redux/user/reducer';


interface OpenChatItemProps {
  chat: any;
  onBack: () => void;
  userDetails?: any;
  onChatSelect?: (chatUser: any) => void;
}
interface ChatItem {
  _id: string;
  createdAt: string; // ✅ Add this line
  accountId: {
    _id: string;
    firstName?: string;
    lastName?: string;
    first_name?: string;
    last_name?: string;
    profilePicture?: string;
    profile_picture?: string;
    company_position?: string;
    createdAt: string;
  };
  lastMessage?: string; // ❌ This was previously an object — we’ll fix it below

  unreadCount?: number;
  muted?: boolean;
  blocked?: boolean;
}

export const OpenChatItem: React.FC<OpenChatItemProps> = ({
  chat,
  onBack,
  userDetails,
  onChatSelect
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredChats, setFilteredChats] = useState<ChatItem[]>([]);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);

  const currentUser = useSelector(userAuthInfo);
  const myId = useSelector(currentUserId);

  const {
    chatList,
    isLoading,
    error,
    refreshMyChat,
    blockUser,
    unBlockUser,
    muteNotification
  } = useChatActions();

  useEffect(() => {
    if (chatList) {
      const filtered = chatList.filter((chat: ChatItem) => {
        const firstName = chat.accountId.firstName || chat.accountId.first_name || '';
        const lastName = chat.accountId.lastName || chat.accountId.last_name || '';
        const fullName = `${firstName} ${lastName}`.toLowerCase();
        return fullName.includes(searchQuery.toLowerCase());
      });
      setFilteredChats(filtered);
    }
  }, [chatList, searchQuery]);
  // Function to handle chat selection and load messages
  const handleChatPress = async (chat: ChatItem) => {
    try {
      setIsLoadingMessages(true);
      setSelectedChatId(chat._id);

      console.log('Opening chat with user:', chat.accountId._id);

      // Get or create chat messages using the backend API
      const chatData = await chatAPI.getUserChats({
        userId: chat.accountId._id,
        pageNumber: 1,
        limit: 50
      });

      console.log('Chat data received:', chatData);

      if (chatData && chatData.chats) {
        setChatMessages(chatData.chats.docs || []);
      }

      // Call the parent callback to update the chat interface
      if (onChatSelect) {
        onChatSelect({
          ...chat.accountId,
          chatId: chatData?.chats?.chatDetails?._id,
          messages: chatData?.chats?.docs || []
        });
      }
    } catch (error) {
      console.error('Error loading chat messages:', error);
      // Even if there's an error, we can still show the chat interface
      // The chat will be created when the first message is sent
      setChatMessages([]);

      // Still call the callback for new chats
      if (onChatSelect) {
        onChatSelect({
          ...chat.accountId,
          chatId: null,
          messages: []
        });
      }
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const handleBlockUser = async (accountId: string) => {
    try {
      await blockUser(accountId);
      refreshMyChat();
    } catch (error) {
      console.error('Error blocking user:', error);
    }
  };

  const handleUnblockUser = async (accountId: string) => {
    try {
      await unBlockUser(accountId);
      refreshMyChat();
    } catch (error) {
      console.error('Error unblocking user:', error);
    }
  };

  const handleMuteToggle = async (chat: ChatItem) => {
    try {
      await muteNotification(chat);
      refreshMyChat();
    } catch (error) {
      console.error('Error toggling mute:', error);
    }
  };
    const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  return (
    <div className=" bg-white border-x border-x-[#D9D9D9] border-t-0 border-b-0
flex flex-col h-full pl-[1rem] pr-[23px] w-full max-w-[28.43vw]">
      {/* Header */}
      <div className="">
        <div className="flex mb-[11px]">
          {/* <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button> */}
          <h2 className={`${poppinsRegular.className} text-[1rem] text-[#797C7B]`}>
            Chats
          </h2>
        </div>

        {/* Search */}
        {/* <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div> */}
      </div>

      {/* Chat List */}
      <div className="flex flex-col gap-[8px] overflow-y-auto">
        {filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64">
            <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
            <p className={`${poppinsRegular.className} text-gray-500`}>
              {searchQuery ? 'No chats found' : 'No conversations yet'}
            </p>
          </div>
        ) : (
          filteredChats.map((chat) => (
            <ChatItem
              key={chat._id}
              chat={chat}
              isSelected={selectedChatId === chat._id}
              onPress={() => handleChatPress(chat)}
              onBlock={() => handleBlockUser(chat.accountId._id)}
              onUnblock={() => handleUnblockUser(chat.accountId._id)}
              onMuteToggle={() => handleMuteToggle(chat)}
              isActive={selectedChatId === chat.accountId._id}
              formatTime={formatTime}
            />
          ))
        )}
      </div>
    </div>
  )


}
interface ChatItemProps {
  chat: ChatItem;
  isSelected: boolean;
  onPress: () => void;
  onBlock: () => void;
  onUnblock: () => void;
  onMuteToggle: () => void;
  formatTime: (date: string) => string;
  isActive: boolean
}

const ChatItem: React.FC<ChatItemProps> = ({
  chat,
  isSelected,
  onPress,
  onBlock,
  onUnblock,
  onMuteToggle,
  formatTime,
  isActive
}) => {
  const [showMenu, setShowMenu] = useState(false);


  return (
    <div
    onClick={onPress}
    className={` ${isSelected || isActive ? 'bg-[#F9FAFB]' : 'bg-transparent'} p-[1rem] hover:bg-[#F9FAFB] cursor-pointer `}
    >
      <div
     
        className={`flex w-full justify-between 
         
        `}
      >
    
        <div className="relative flex gap-[8px]">
          <Avatar
            size={36}
            type="user"
            profilePicture={chat.accountId.profilePicture || chat.accountId.profile_picture}
            customDefault="thumbnail" 
          />
          {chat.unreadCount && chat.unreadCount > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
            </div>
          )}
               <div className=" flex flex-col">
          <div className="flex flex-col">
            <h3 className={`${poppinsMedium.className} text-[14px] text-[#1F1E1A]`}>
              {(chat.accountId.firstName || chat.accountId.first_name || 'Unknown')} {(chat.accountId.lastName || chat.accountId.last_name || 'User')}
            </h3>
            {chat.accountId.company_position && (
            <span className={`${poppinsRegular.className} text-[#696969] text-[10px]`}>
              {chat.accountId.company_position}
            </span>
          )}
           {chat.lastMessage ? (
            <p className={`${poppinsMedium.className} text-[#696969] text-[14px] mt-[4px]`}>
              {typeof chat.lastMessage === 'string' ? chat.lastMessage : chat.lastMessage}
            </p>
          ) : (
            <p className={`${poppinsMedium.className}  text-[#4F4F4F] text-[14px]`}>
              Nothing to see here
            </p>
          )}


            <div className="flex items-center ">
              {chat.muted && <VolumeX className="h-4 w-4 text-gray-400" />}
              <span className={`${poppinsRegular.className} text-[#696969] text-[10px]`}>
                {formatTime(chat.createdAt)}
              </span>
            </div>
          </div>

          
         
        </div>

        </div>

        {/* Chat Info */}
   
        {/* Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            setShowMenu(!showMenu);
          }}
          className="ml-2 p-1"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>

      {/* Dropdown Menu */}
      {showMenu && (
        <div className="absolute right-4 top-16 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[150px]">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onMuteToggle();
              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            {chat.muted ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            {chat.muted ? 'Unmute' : 'Mute'}
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              chat.blocked ? onUnblock() : onBlock();
              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
          >
            <UserX className="h-4 w-4" />
            {chat.blocked ? 'Unblock' : 'Block'}
          </button>
            
      
        </div>
      )}

    </div>
  );
};

export default OpenChatItem;
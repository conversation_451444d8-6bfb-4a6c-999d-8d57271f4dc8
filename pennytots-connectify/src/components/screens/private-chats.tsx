'use client';

import React, { useState, useEffect } from 'react';
import { Search, MessageCircle, MoreVertical, UserX, VolumeX, Volume2, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { poppinsSemibold, poppinsRegular } from '@/fonts';
import { useChatActions } from '@/redux/chat/hooks';
import { useRouter } from 'next/navigation';
import { PrivateChatRoom } from '@/components/chat/PrivateChatRoom';
import { getSafeUserInitials } from '@/lib/chat-utils';

interface ChatItem {
  _id: string;
  accountId: {
    _id: string;
    firstName?: string;
    lastName?: string;
    first_name?: string;
    last_name?: string;
    profilePicture?: string;
    profile_picture?: string;
  };
  lastMessage?: {
    message: string;
    createdAt: string;
  };
  unreadCount?: number;
  muted?: boolean;
  blocked?: boolean;
}

const PrivateChats = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredChats, setFilteredChats] = useState<ChatItem[]>([]);
  const [showBlockedChats, setShowBlockedChats] = useState(false);
  const [selectedChat, setSelectedChat] = useState<ChatItem | null>(null);

  const router = useRouter();
  const {
    chatList,
    isLoading,
    error,
    refreshMyChat,
    blockUser,
    unBlockUser,
    muteNotification
  } = useChatActions();

  useEffect(() => {
    if (chatList) {
      const filtered = chatList.filter((chat: ChatItem) => {
        const firstName = chat.accountId.firstName || chat.accountId.first_name || '';
        const lastName = chat.accountId.lastName || chat.accountId.last_name || '';
        return `${firstName} ${lastName}`
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
      });
      setFilteredChats(filtered);
    }
  }, [chatList, searchQuery]);

  const handleChatPress = (chat: ChatItem) => {
    setSelectedChat(chat);
  };

  const handleBackToList = () => {
    setSelectedChat(null);
  };

  const handleBlockUser = async (accountId: string) => {
    try {
      await blockUser(accountId);
      refreshMyChat();
    } catch (error) {
      console.error('Error blocking user:', error);
    }
  };

  const handleUnblockUser = async (accountId: string) => {
    try {
      await unBlockUser(accountId);
      refreshMyChat();
    } catch (error) {
      console.error('Error unblocking user:', error);
    }
  };

  const handleMuteToggle = async (chat: ChatItem) => {
    try {
      await muteNotification(chat);
      refreshMyChat();
    } catch (error) {
      console.error('Error toggling mute:', error);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  // Show selected chat room
  if (selectedChat) {
    return (
      <PrivateChatRoom
        chat={selectedChat}
        onBack={handleBackToList}
      />
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F2875D]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-red-500 mb-4">Error loading chats</p>
        <Button onClick={() => refreshMyChat()} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h1 className={`${poppinsSemibold.className} text-xl text-gray-900`}>
            Messages
          </h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowBlockedChats(!showBlockedChats)}
            className="text-sm"
          >
            {showBlockedChats ? 'Show Active' : 'Show Blocked'}
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {filteredChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64">
            <MessageCircle className="h-12 w-12 text-gray-400 mb-4" />
            <p className={`${poppinsRegular.className} text-gray-500`}>
              {searchQuery ? 'No chats found' : 'No conversations yet'}
            </p>
          </div>
        ) : (
          filteredChats.map((chat) => (
            <ChatItem
              key={chat._id}
              chat={chat}
              onPress={() => handleChatPress(chat)}
              onBlock={() => handleBlockUser(chat.accountId._id)}
              onUnblock={() => handleUnblockUser(chat.accountId._id)}
              onMuteToggle={() => handleMuteToggle(chat)}
              formatTime={formatTime}
            />
          ))
        )}
      </div>
    </div>
  );
};

interface ChatItemProps {
  chat: ChatItem;
  onPress: () => void;
  onBlock: () => void;
  onUnblock: () => void;
  onMuteToggle: () => void;
  formatTime: (date: string) => string;
}

const ChatItem: React.FC<ChatItemProps> = ({
  chat,
  onPress,
  onBlock,
  onUnblock,
  onMuteToggle,
  formatTime,
}) => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div className="relative">
      <div
        onClick={onPress}
        className="flex items-center p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
      >
        {/* Avatar */}
        <div className="relative mr-3">
          <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
            {(chat.accountId.profilePicture || chat.accountId.profile_picture) ? (
              <img
                src={chat.accountId.profilePicture || chat.accountId.profile_picture}
                alt="Profile"
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <span className={`${poppinsSemibold.className} text-white text-lg`}>
                {getSafeUserInitials(chat.accountId)}
              </span>
            )}
          </div>
          {chat.unreadCount && chat.unreadCount > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {chat.unreadCount > 9 ? '9+' : chat.unreadCount}
            </div>
          )}
        </div>

        {/* Chat Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className={`${poppinsSemibold.className} text-gray-900 truncate`}>
              {(chat.accountId.firstName || chat.accountId.first_name || '')} {(chat.accountId.lastName || chat.accountId.last_name || '')}
            </h3>
            <div className="flex items-center gap-2">
              {chat.muted && <VolumeX className="h-4 w-4 text-gray-400" />}
              {chat.lastMessage && (
                <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                  {formatTime(chat.lastMessage.createdAt)}
                </span>
              )}
            </div>
          </div>
          {chat.lastMessage && (
            <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate mt-1`}>
              {chat.lastMessage.message}
            </p>
          )}
        </div>

        {/* Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            setShowMenu(!showMenu);
          }}
          className="ml-2 p-1"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>

      {/* Dropdown Menu */}
      {showMenu && (
        <div className="absolute right-4 top-16 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[150px]">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onMuteToggle();
              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            {chat.muted ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            {chat.muted ? 'Unmute' : 'Mute'}
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              chat.blocked ? onUnblock() : onBlock();
              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
          >
            <UserX className="h-4 w-4" />
            {chat.blocked ? 'Unblock' : 'Block'}
          </button>
        </div>
      )}
    </div>
  );
};

export default PrivateChats;

import { useCreateTicket, useGetHelpDeskCategories } from "@/redux/helpdesk/hooks";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { DropdownMenu } from "../ui/dropdown-menu";
import SelectDropdown from "../ui/select-dropdown";
import { Button } from "../ui/button";
import { toast } from 'react-toastify'; // or your toast library
import { poppinsMedium } from "@/fonts";
import { useQueryClient } from '@tanstack/react-query';


interface CreateHelpDeskTicketProps {
  closeModal: () => void;
}

const CreateHelpDeskTicket: React.FC<CreateHelpDeskTicketProps> = ({ closeModal }) => {
  const router = useRouter();
  const [category, setCategory] = useState('');
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isPostModalOpen, setIsPostModalOpen] = useState(false);
  const [loading, setLoading] = useState(false)
 const queryClient = useQueryClient();

  const { data: categories, isLoading: categoriesLoading } = useGetHelpDeskCategories();
  const { mutateAsync: createTicket } = useCreateTicket();

  const navigation = {
    back: () => router.back(),
    navigate: (route: string) => router.push(`/${route}`)
  };




  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();

    // Early validation before loading state
    if (!title.trim() || !category || !content.trim()) {
      toast.error('Please fill out all required fields', {
        toastId: 'empty-fields-error' // Prevent duplicate toasts
      });
      return;
    }

    setLoading(true);

    try {
      const payload = {
        title: title.trim(),
        category,
        message: content.trim(),
        metadata: { // Additional useful data
          createdAt: new Date().toISOString(),
          userAgent: navigator.userAgent,
        }
      };

      console.log('Ticket creation payload:', payload);

      const response = await createTicket(payload);

      console.log('Ticket creation response:', response);
await queryClient.invalidateQueries({ queryKey: ['helpdesk-tickets'] });

      // Success handling
      toast.success('Ticket created successfully!', {
        autoClose: 2000,
        onClose: () => {
          closeModal(); // ✅ Close the modal after success
          router.push('/helpdesk'); // ✅ Optional: redirect to helpdesk page
        },
      });

      // Reset form
      setContent('');
      setTitle('');
      setCategory('');



    } catch (error: any) {
      console.error('Ticket creation error:', error);

      // Enhanced error handling
      const errorMessage = error.response?.data?.message ||
        error.message ||
        'Failed to create ticket. Please try again.';

      toast.error(errorMessage, {
        toastId: 'ticket-creation-error'
      });

    } finally {
      setLoading(false);

    }
  }

  return (
    <div className="bg-white">
      {/* <CustomModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        NavigateToCreditPage={handleNavigateChallengeMode}
        type={type ? 'helpdesk' : 'alert'}
        message="All fields are required"
        handleConfirm={handleConfirm}
      /> */}

      {/* <HeaderTitle title="Help Desk" navigation={navigation} /> */}

      <div className=" mx-auto ">
        <div className="">
          <p className={`text-[#4F4F4F] text-[14px] ${poppinsMedium.className}`}>
            Tell us how we can help you
          </p>

          <div className="mt-[1.5rem]">
            <label className={`text-[#4F4F4F] mb-[8px] text-[14px] ${poppinsMedium.className}`}>
              Category
            </label>
            {categoriesLoading ? (
              <div className="w-full p-3 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600 mr-2"></div>
                Loading categories...
              </div>
            ) : categories ? (
              <SelectDropdown
                data={categories}
                onSelect={(selectedItem: string, index: number) => {
                  setCategory(selectedItem);
                }}
                defaultButtonText="Select a category"
                buttonTextAfterSelection={(selectedItem: string) => selectedItem}
                search={true}
              />
            ) : (
              <div className="w-full p-3 bg-red-50 border border-red-200 rounded-lg text-red-600">
                Failed to load categories
              </div>
            )}
          </div>

          <div className="mt-[1.5rem]">
            <label className={`text-[#4F4F4F] text-[14px] mb-[8px] ${poppinsMedium.className}`}>
              Title
            </label>
            <input
              type="text"
              maxLength={50}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full rounded-[6px] border border-[#D0D5DD] placeholder:text-[#98A2B3] placeholder:text-[14px] p-[1rem] "
            />
          </div>

          <div className="mt-[1.5rem]">
            <label className="mb-[8px]">
              Details
            </label>
            <textarea
              rows={10}
              placeholder="Describe your issue"
              value={content}
              onChange={(e) => setContent(e.target.value)}

              className="w-full rounded-[6px] px-[1rem] pt-[1rem]  border border-[#D0D5DD] h-[17vh]"
            />
          </div>

          <div className="pt-[1.5rem]">

            <Button
              onClick={handleSubmit}
              disabled={loading}
              variant="primary"
              className="justify-center w-full flex"
            >
              <span className="flex items-center text-white ">
                {loading && (
                  <svg
                    className="animate-spin h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    />
                  </svg>
                )}
                {loading ? 'Creating...' : 'Create'}
              </span>
            </Button>

          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateHelpDeskTicket;


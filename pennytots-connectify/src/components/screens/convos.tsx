'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Loading } from '@/components/ui/loading';
import { ArrowLeft, Plus } from 'lucide-react';
import { TopicCard } from './topic-list';
import { useConvos } from '../../redux/topic/hooks';
import { useGetUnreadNotifications } from '@/redux/main/hooks';
import { main } from '@/api/main';
import { poppinsSemibold } from '@/fonts';

interface ConvosProps {
  selectedTopicId?: string;
}

interface IConvos {
  _id: string;
  topicId: any;
}

function Convos({ selectedTopicId }: ConvosProps) {
  const { data, error, isFetching, refetch, isLoading } = useConvos();
  const { data: unread } = useGetUnreadNotifications(false);
  const router = useRouter();
  const [selectedTopic, setSelectedTopic] = useState<any>(null);

  // Clear unread notifications when component mounts and there are unread convos
  useEffect(() => {
    if (unread && unread.convos && unread.convos.length > 0) {
      main.clearUnReadNotifications({ source: 'convos' }).then((response) => {
        console.log('Cleared unread notifications:', response);
      });
    }
  }, [unread, data]);

  // Find the selected topic from the data
  React.useEffect(() => {
    if (selectedTopicId && data) {
      const topic = data.find((item: any) => item._id === selectedTopicId);
      setSelectedTopic(topic);
    }
  }, [selectedTopicId, data]);

  const handleTopicSelect = (topic: any) => {
    setSelectedTopic(topic);
    // Update URL without page reload
    window.history.pushState({}, '', `/convos?topic=${topic._id}`);
  };

  const handleBackToList = () => {
    setSelectedTopic(null);
    window.history.pushState({}, '', '/convos');
  };

  if (selectedTopic) {
    // Show individual topic view (conversation view)
    return (
      <div className="flex flex-col w-full min-h-full">
        {/* Header */}
        <div className="bg-white sticky top-0 z-10 border-b border-gray-200 ">
          <div className="flex items-center ga">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToList}
              className="p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className={`${poppinsSemibold.className} text-lg text-gray-900`}>
              Conversation
            </h1>
          </div>
        </div>

        {/* Topic Content */}
        <div className="flex-1 pb-4">
          <TopicCard
            item={selectedTopic}
            convo={true}
            refreshFunction={() => refetch()}
            onToggleMuted={() => {}}
            muted={false}
          />

          {/* Comments section would go here */}
          <div className=" bg-gray-50">
            <p className="text-gray-500 text-center">Comments section coming soon...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show convos list - conversations are topics the user has engaged with
  return (
    <div className="flex flex-col w-full min-h-full">
      {/* Convos List */}
      {isLoading ? (
        <div className="flex-1 flex justify-center items-center min-h-[400px]">
          <Loading size="lg" />
        </div>
      ) : data && data.length > 0 ? (
        <div className="pb-4">
          {data.map((item: IConvos) => (
            <div
              key={item._id}
              onClick={() => handleTopicSelect(item.topicId)}
              className="cursor-pointer hover:bg-gray-50"
            >
              <TopicCard
                item={item.topicId}
                convo={true}
                refreshFunction={() => refetch()}
                onToggleMuted={() => {}}
                muted={false}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="flex-1 flex flex-col justify-center items-center space-y-4 min-h-[400px]">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-700">No Conversations Found</h3>
            <p className="text-gray-500 mt-2">There are no conversations to display yet.</p>
            <p className="text-gray-500">Start a conversation by creating a topic!</p>
          </div>
          <Button
            onClick={() => router.push('/create-topic')}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Topic</span>
          </Button>
        </div>
      )}
    </div>
  );
}

export default Convos;

import { useGetHelpDeskTickets } from "@/redux/helpdesk/hooks";
import { MessageCircle } from "lucide-react";
import TicketCard from "./ticket-card";
import { useEffect, useState } from "react";
import Avatar from "../ui/avatar";
import HelpDeskMessages from "../helpdesk/HelpDeskMessages";
import { poppinsSemibold, poppinsRegular } from "@/fonts";
import EmptyPagesSVG from '@/assets/svg/empty-pages.svg'


interface Ticket {
  _id: string;
  ticketId: string;
  title: string;
  category: string;
  status: string;
  profilePicture: string;
  firstName: string;
  lastName: string;
  message: string;
  createdAt: string;
}

export default function HelpdeskScreen() {
  const { data: tickets, isLoading } = useGetHelpDeskTickets();
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);

  useEffect(() => {
    if (!isLoading && tickets) {
      console.log(tickets, "tickets");
    }
  }, [tickets, isLoading]);

  const handleTicketSelect = (ticket: Ticket) => {
    console.log("Selected ticket:", ticket);
    console.log("Ticket _id being used:", ticket._id);
    console.log("Ticket ticketId:", ticket.ticketId);
    setSelectedTicket(ticket);
  };

  return (
    <div className="flex h-full w-full">
      {/* Left sidebar - Ticket list */}
      <div className="bg-white border-x border-x-[#D9D9D9] border-t-0 border-b-0 flex flex-col h-full pl-[1rem] pr-[23px] w-full max-w-[425px]">
        <h1 className={`${poppinsRegular.className} text-[1rem] text-[#797C7B] mb-[11px]`}>HelpDesk</h1>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : tickets?.length > 0 ? (
          <div className=" overflow-y-auto">
            {tickets.map((ticket: Ticket) => (
              <div
                key={ticket._id || ticket.ticketId}
                onClick={() => handleTicketSelect(ticket)}
                className="cursor-pointer"
              >
                <TicketCard
                  ticket={ticket}
                  selected={(ticket._id || ticket.ticketId) === (selectedTicket?._id || selectedTicket?.ticketId)}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className={`${poppinsRegular.className} text-gray-600`}>No tickets found</p>
          </div>
        )}
      </div>

      {/* Right side - Ticket messages */}
      <div className="flex-1">
        {selectedTicket ? (
          <HelpDeskMessages
            helpdeskId={selectedTicket._id}
            helpdesk={{
              _id: selectedTicket._id,
              title: selectedTicket.title,
              category: selectedTicket.category,
              status: selectedTicket.status
            }}
            onBack={() => setSelectedTicket(null)}
          />
        ) : (
          <div className="flex items-center w-full justify-center h-full bg-white">
          <EmptyPagesSVG />
          </div>
        )}
      </div>
    </div>
  );
}


'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';
import { useUpdateProfile } from '@/redux/profile/hooks';
import { user } from '@/api/user';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar } from '@/components/ui/avatar';
import { Loading } from '@/components/ui/loading';
import { Select } from '@/components/ui/select';
import ImageUpload from '@/components/profile/ImageUpload';
import ProfileLayout from '@/components/layout/profile-layout';
import BriefcaseIcon from '@/assets/svg/briefcase.svg';
import BioIcon from '@/assets/svg/bio.svg';
import ProfileIcon from '@/assets/svg/profile.svg';
import LinkIcon from '@/assets/svg/link.svg';
import LocationIcon from '@/assets/svg/location.svg';
import GraduationIcon from '@/assets/svg/alumni.svg';
import YearIcon from '@/assets/svg/year.svg';
import CountryIcon from '@/assets/svg/country.svg';

import {
  ArrowLeft,
  Camera,
  Upload,
  Save,
  X
} from 'lucide-react';
import Link from 'next/link';
import MainLayout from '@/components/layout/main-layout';
import { poppinsMedium, poppinsSemibold } from '@/fonts';
import { SelectInterests } from '@/components/elements/SelectInterests';
import { CountrySelect } from '@/components/elements/CountrySelect';
import { useFetchTopic } from '@/redux/topic/hooks';
import { useFetchGroups } from '@/redux/group/hooks';

interface ProfileFormData {
  first_name: string;
  last_name: string;
  bio: string;
  website: string;
  facebook: string;
  linkedin: string;
  company: string;
  company_position: string;
  city: string;
  state: string;
  age: string;
  gender: string;
  graduation_status: string;
  graduation_year: string;
  country?: string; // Optional field for country
}
const EditProfileForm = ({ 
  onBack, 
  onUpdate 
}: {
  onBack: () => void;
  onUpdate: (data?: any) => void; // or whatever data type you expect
}) => {
  const router = useRouter();
  const { updateProfile, updateProfileWithImages, loading: updateLoading } = useUpdateProfile();

  const [formData, setFormData] = useState<ProfileFormData>({
    first_name: '',
    last_name: '',
    bio: '',
    website: '',
    facebook: '',
    linkedin: '',
    company: '',
    company_position: '',
    city: '',
    state: '',
    age: '',
    gender: '',
    graduation_status: '',
    graduation_year: '',
    country: '' // Initialize country as an empty string
  });

  const [profilePicture, setProfilePicture] = useState<string>('');
  const [headerImage, setHeaderImage] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState<Partial<ProfileFormData>>({});
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [headerImageFile, setHeaderImageFile] = useState<File | null>(null);

  useEffect(() => {
    fetchProfileData();
  }, []);

  const fetchProfileData = async () => {
    try {
      setLoading(true);
      const profileResponse = await user.profile();

      setFormData({
        first_name: profileResponse.first_name || '',
        last_name: profileResponse.last_name || '',
        bio: profileResponse.bio || '',
        website: profileResponse.website || '',
        facebook: profileResponse.facebook || '',
        linkedin: profileResponse.linkedin || '',
        company: profileResponse.company || '',
        company_position: profileResponse.company_position || '',
        city: profileResponse.city || '',
        state: profileResponse.state || '',
        age: profileResponse.age?.toString() || '',
        gender: profileResponse.gender || '',
        graduation_status: profileResponse.graduation_status || '',
        graduation_year: profileResponse.graduation_year || '',
        country: profileResponse.country || '' // Initialize country from profile response
      });

      setProfilePicture(profileResponse.profile_picture || '');
      setHeaderImage(profileResponse.header_image || '');
    } catch (error) {
      console.error('Error fetching profile data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const handleProfileImageChange = (file: File, previewUrl: string) => {
    setProfilePicture(previewUrl);
    setProfilePictureFile(file);
  };

  const handleHeaderImageChange = (file: File, previewUrl: string) => {
    setHeaderImage(previewUrl);
    setHeaderImageFile(file);
  };

  const handleProfileImageRemove = () => {
    setProfilePicture('');
    setProfilePictureFile(null);
  };

  const handleHeaderImageRemove = () => {
    setHeaderImage('');
    setHeaderImageFile(null);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ProfileFormData> = {};

    if (!formData.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }

    // Debug URL validation
    console.log('Validating URLs:', {
      website: formData.website,
      facebook: formData.facebook,
      linkedin: formData.linkedin
    });

    if (formData.website && !isValidURL(formData.website)) {
      console.log('Website URL validation failed:', formData.website);
      newErrors.website = 'Please enter a valid URL (e.g., example.com or https://example.com)';
    }

    if (formData.facebook && !isValidURL(formData.facebook)) {
      console.log('Facebook URL validation failed:', formData.facebook);
      newErrors.facebook = 'Please enter a valid Facebook URL (e.g., facebook.com/yourname)';
    }

    if (formData.linkedin && !isValidURL(formData.linkedin)) {
      console.log('LinkedIn URL validation failed:', formData.linkedin);
      newErrors.linkedin = 'Please enter a valid LinkedIn URL (e.g., linkedin.com/in/yourname)';
    }

    if (formData.age && (isNaN(Number(formData.age)) || Number(formData.age) < 13 || Number(formData.age) > 120)) {
      newErrors.age = 'Please enter a valid age between 13 and 120';
    }


    console.log('Validation errors:', newErrors);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidURL = (string: string): boolean => {
    if (!string.trim()) return true; // Empty string is valid (optional field)

    // Remove whitespace
    const cleanUrl = string.trim();

    try {
      // If the URL doesn't start with http:// or https://, add https://
      const urlToTest = cleanUrl.startsWith('http://') || cleanUrl.startsWith('https://')
        ? cleanUrl
        : `https://${cleanUrl}`;

      const url = new URL(urlToTest);

      // Additional check: make sure it has a valid hostname
      return url.hostname.includes('.') && url.hostname.length > 3;
    } catch (error) {
      console.log('URL validation error for:', cleanUrl, error);

      // Fallback: check if it looks like a valid domain
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.([a-zA-Z]{2,}|[a-zA-Z]{2,}\.[a-zA-Z]{2,})$/;
      const domain = cleanUrl.replace(/^https?:\/\//, '').split('/')[0];
      return domainRegex.test(domain);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const submitData = {
        ...formData,
        age: formData.age ? Number(formData.age) : undefined
      };

      // Remove empty fields
      Object.keys(submitData).forEach(key => {
        if (submitData[key as keyof typeof submitData] === '') {
          delete submitData[key as keyof typeof submitData];
        }
      });

      // Use updateProfileWithImages if there are image files to upload
      if (profilePictureFile || headerImageFile) {
        await updateProfileWithImages(
          submitData,
          profilePictureFile || undefined,
          headerImageFile || undefined,
          '/profile'
        );
      } else {
        await updateProfile(submitData, '/profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loading />
      </div>
    );
  }

return (

    <div className="flex justify-center w-full min-h-full" > {/* Changed from h-screen to min-h-full */}
      {/* Scrollable content container */}
      <div className="flex flex-col w-full  pb-[5rem]" style={{minWidth: '32.07vw'}}> {/* Added pb-8 for bottom spacing */}
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={onBack}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Edit Profile</h1>
        </div>
        
        {/* Rest of your form here... */}
        <form onSubmit={handleSubmit} className="flex-1"> {/* Added flex-1 */}
          {/* Header Image */}
          <div className="">
            {/* Header Image with Profile Picture Overlay */}
            <div className="relative">
              <ImageUpload
                type="header"
                currentImage={headerImage}
                onImageChange={handleHeaderImageChange}
                onImageRemove={handleHeaderImageRemove}
                disabled={updateLoading}
              />

              <ImageUpload
                type="profile"
                currentImage={profilePicture}
                onImageChange={handleProfileImageChange}
                onImageRemove={handleProfileImageRemove}
                disabled={updateLoading}
              />
            </div>
          </div>
          
          <div className="flex items-center w-full mt-[1.5rem]">
            <div className='flex gap-[1rem]'>
              <div>
                <BioIcon className="w-[18px] h-[18px]" />
              </div>
              <div className='flex flex-col  gap-[8px]]'>
                <Label htmlFor="first_name" className={`text-[#797978] ${poppinsSemibold.className}`}>First Name</Label>
                <Input
                  id="first_name"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  className={`border-0 border-b-1 text-[12px] text-[#797978] ${poppinsMedium.className} rounded-none px-0 py-2 focus:ring-0 focus:border-b-[#E8E8E8] ${errors.first_name ? 'border-b-red-500' : 'border-b-gray-300'}`}
                />
                {errors.first_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.first_name}</p>
                )}
              </div>
            </div>

            <div className='flex gap-[1rem] mt-[1.5rem]'>
              <div>
                <BioIcon className="w-[18px] h-[18px]" />
              </div>
              <div className='flex flex-col  gap-[8px]]'>
                <Label htmlFor="first_name" className={`text-[#797978] ${poppinsSemibold.className}`}>Last Name</Label>
                <Input
                  id="last_name"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  className={`border-0 border-b-1 text-[12px] text-[#797978] ${poppinsMedium.className} rounded-none px-0 py-2 focus:ring-0 focus:border-b-[#E8E8E8] ${errors.last_name ? 'border-b-red-500' : 'border-b-gray-300'}`}
                />
                {errors.last_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.last_name}</p>
                )}
              </div>
            </div>
          </div>

          <div className='flex gap-[1rem] mt-[1.5rem]'>
            <div>
              <GraduationIcon className="w-[18px] h-[22px]" />
            </div>
            <div className='flex flex-col w-full  gap-[8px]]'>
              <Label htmlFor="bio" className={`text-[#797978] ${poppinsSemibold.className}`}>I am a(n)</Label>
              <SelectInterests
                interest={formData.graduation_status}
                setInterest={(value) => handleInputChange('graduation_status', value)}
                multiple={false}
              />
              {errors.graduation_status && (
                <p className="text-red-500 text-sm mt-1">{errors.graduation_status}</p>
              )}
            </div>
          </div>

          <div className='flex gap-[1rem] mt-[1.5rem]'>
            <div>
              <YearIcon className="w-[21px] h-[20px]" />
            </div>
            <div className='flex flex-col w-full  gap-[8px]]'>
              <Label htmlFor="bio" className={`text-[#797978] ${poppinsSemibold.className}`}>I am a(n)</Label>
              <SelectInterests
                interest={formData.graduation_year}
                setInterest={(value) => handleInputChange('graduation_year', value)}
                multiple={false}
              />
              {errors.graduation_year && (
                <p className="text-red-500 text-sm mt-1">{errors.graduation_year}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center">
            <div className='flex gap-[1rem] mt-[1.5rem]'>
              <div>
                <BriefcaseIcon className="w-[20px] h-[19px]" />
              </div>
              <div className='flex flex-col  gap-[8px]]'>
                <Label htmlFor="bio" className={`text-[#797978] ${poppinsSemibold.className}`}>Position</Label>
                <Input
                  id="company_position"
                  value={formData.company_position}
                  onChange={(e) => handleInputChange('company_position', e.target.value)}
                  className={`border-0 border-b-1 text-[12px] text-[#797978] ${poppinsMedium.className} rounded-none px-0 py-2 focus:ring-0 focus:border-b-[#E8E8E8] ${errors.company_position ? 'border-b-red-500' : 'border-b-gray-300'}`}
                />
                {errors.company_position && (
                  <p className="text-red-500 text-sm mt-1">{errors.company_position}</p>
                )}
              </div>
            </div>
            <div className='flex gap-[1rem] mt-[1.5rem]'>
              <div>
                <BriefcaseIcon className="w-[20px] h-[19px]" />
              </div>
              <div className='flex flex-col  gap-[8px]]'>
                <Label htmlFor="bio" className={`text-[#797978] ${poppinsSemibold.className}`}>Company</Label>
                <Input
                  id="company"
                  value={formData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  className={`border-0 border-b-1 text-[12px] text-[#797978] ${poppinsMedium.className} rounded-none px-0 py-2 focus:ring-0 focus:border-b-[#E8E8E8] ${errors.company ? 'border-b-red-500' : 'border-b-gray-300'}`}
                />
                {errors.company && (
                  <p className="text-red-500 text-sm mt-1">{errors.company}</p>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center w-full mt-[1.5rem]">
            <div className='flex gap-[1rem] w-full'>
              <div>
                <BioIcon className="w-[18px] h-[18px]" />
              </div>
              <div className='flex flex-col w-full gap-[8px]]'>
                <Label htmlFor="first_name" className={`text-[#797978] ${poppinsSemibold.className}`}>Bio</Label>
                <Input
                  id="first_name"
                  value={formData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  className={`border-0 border-b-1 text-[12px] text-[#797978] ${poppinsMedium.className} rounded-none px-0 py-2 focus:ring-0 focus:border-b-[#E8E8E8] ${errors.bio ? 'border-b-red-500' : 'border-b-gray-300'}`}
                />
                {errors.bio && (
                  <p className="text-red-500 text-sm mt-1">{errors.bio}</p>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center w-full mt-[1.5rem]">
            <div className='flex gap-[1rem] w-full'>
              <div>
                <CountryIcon className="w-[20px] h-[18px]" />
              </div>
              <div className='flex flex-col w-full  gap-[8px]]'>
                <Label htmlFor="first_name" className={`text-[#797978] ${poppinsSemibold.className}`}>Country of residence</Label>
                <CountrySelect
                  value={formData.country || ''}
                  onChange={(value) => handleInputChange('country', value)}
                  placeholder="Select your country"
                />
                {errors.country && (
                  <p className="text-red-500 text-sm mt-1">{errors.country}</p>
                )}
              </div>
            </div>
          </div>

          {/* Personal Details */}
          <div className="flex w-full mt-[1.5rem] flex-col">
            <div className="flex items-center w-full">
              <div className='flex gap-[1rem] w-full'>
                <div>
                  <LocationIcon className="w-[18px] h-[18px]" />
                </div>
                <div className='flex flex-col w-full gap-[8px]]'>
                  <Label htmlFor="city" className={`text-[#797978] ${poppinsSemibold.className}`}>Town/City</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    className={`border-0 border-b-1 text-[12px] text-[#797978] ${poppinsMedium.className} rounded-none px-0 py-2 focus:ring-0 focus:border-b-[#E8E8E8] ${errors.city ? 'border-b-red-500' : 'border-b-gray-300'}`}
                  />
                  {errors.city && (
                    <p className="text-red-500 text-sm mt-1">{errors.city}</p>
                  )}
                </div>
              </div>
              <div className="flex items-center w-full ">
                <div className='flex gap-[1rem] w-full'>
                  <div>
                    <LocationIcon className="w-[18px] h-[18px]" />
                  </div>
                  <div className='flex flex-col w-full gap-[8px]]'>
                    <Label htmlFor="first_name" className={`text-[#797978] ${poppinsSemibold.className}`}>State</Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) => handleInputChange('state', e.target.value)}
                      className={`border-0 border-b-1 text-[12px] text-[#797978] ${poppinsMedium.className} rounded-none px-0 py-2 focus:ring-0 focus:border-b-[#E8E8E8] ${errors.state ? 'border-b-red-500' : 'border-b-gray-300'}`}
                    />
                    {errors.state && (
                      <p className="text-red-500 text-sm mt-1">{errors.state}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Button with proper spacing */}
            <div className="flex w-full mt-8 mb-4"> {/* Added mt-8 and mb-4 for proper spacing */}
              <Button
                type="submit"
                disabled={updateLoading}
                variant="primary"
                className="w-full py-3 px-6 text-base font-medium"
              >
                {updateLoading ? (
                  <span>Updating...</span>
                ) : (
                  <span>Update Profile</span>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>

);
};

export default EditProfileForm;
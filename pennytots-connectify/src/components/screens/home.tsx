'use client';

import React from 'react';
import TopicList from './topic-list';
import Convos from './convos';

interface HomeProps {
  activeTab?: 'posts' | 'convos';
}

function Home({ activeTab = 'posts' }: HomeProps) {
  return (
    <div className="flex flex-col flex-1" >
      {/* Tab Content */}
      <div className="flex-1 border-r-[1px] border-[#D9D9D9]">
        {activeTab === 'posts' ? (
          <TopicList />
        ) : (
          <Convos />
        )}
      </div>
    </div>
  );
}

export default Home;

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  X, 
  RefreshCw,
  User,
  MessageCircle,
  Users,
  FileText,
  MessageSquare,
  Flag,
  MoreVertical
} from 'lucide-react';
import { poppinsRegular, poppinsMedium, poppinsBold } from '@/fonts';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { Axios } from '@/api/axios';
import { ProfileClickHandler } from '@/components/profile/ProfileClickHandler';
import { useGetSponsoredAd } from '@/redux/sponsor/hooks';
import { useSearch, useSearchState } from '@/redux/search/hooks';
import { SearchParams } from '@/api/search';

interface SearchUser {
  _id: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
  bio?: string;
  company?: string;
  company_position?: string;
}

interface SearchChat {
  _id: string;
  user: SearchUser;
}

interface SearchGroup {
  _id: string;
  name: string;
  description?: string;
  members_count?: number;
  profile_picture?: string;
}

interface SearchTopic {
  _id: string;
  title: string;
  content: string;
  author: SearchUser;
  created_at: string;
  likes_count?: number;
  comments_count?: number;
}

interface SearchComment {
  _id: string;
  content: string;
  author: SearchUser;
  topic_id: string;
  created_at: string;
}

interface SearchResults {
  users: { docs: SearchUser[]; totalDocs: number };
  chats: { docs: SearchChat[]; totalDocs: number };
  groups: { docs: SearchGroup[]; totalDocs: number };
  topics: { docs: SearchTopic[]; totalDocs: number };
  topicComments: { docs: SearchComment[]; totalDocs: number };
}

const SearchScreen: React.FC = () => {
  const router = useRouter();
  const currentUserId = useSelector((state: RootState) => state.user.userId);
  const { adData: sponsoredAd, isLoading: isAdLoading, refetch: refetchAd } = useGetSponsoredAd();

  // Search state management
  const {
    query: searchInput,
    debouncedQuery,
    searchType,
    isSearching,
    setQuery: setSearchInput,
    setSearchType,
    clearSearch: clearSearchState
  } = useSearchState('', 300);

  const [tabIndex, setTabIndex] = useState(0);
  const [refreshing, setRefreshing] = useState(false);

  // Map tab index to search type
  const getSearchTypeFromTab = (index: number): SearchParams['searchType'] => {
    const types: SearchParams['searchType'][] = ['users', 'chats', 'groups', 'topics', 'topic-comments'];
    return types[index] || 'users';
  };

  // Update search type when tab changes
  useEffect(() => {
    setSearchType(getSearchTypeFromTab(tabIndex));
  }, [tabIndex, setSearchType]);

  // Search query using React Query
  const searchParams: SearchParams = {
    search: debouncedQuery,
    searchType: getSearchTypeFromTab(tabIndex),
    page: 1,
    limit: 50
  };

  const {
    data: searchResults,
    isLoading: loading,
    error: searchError,
    refetch: refetchSearch
  } = useSearch(searchParams, {
    enabled: debouncedQuery.trim().length > 0
  });

  // Report dialog state
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState<{ data: { _id: string }; type: string }>({
    data: { _id: '' },
    type: ''
  });

  const filterTypes = [
    'Users',
    'Chats',
    'Groups',
    'Posts',
    'Comments'
  ];

  // Extract results based on current tab
  const getCurrentResults = () => {
    if (!searchResults) return { data: [], totalResults: 0 };

    switch (tabIndex) {
      case 0:
        return {
          data: searchResults.users?.docs || [],
          totalResults: searchResults.users?.totalDocs || 0
        };
      case 1:
        return {
          data: searchResults.chats?.docs || [],
          totalResults: searchResults.chats?.totalDocs || 0
        };
      case 2:
        return {
          data: searchResults.groups?.docs || [],
          totalResults: searchResults.groups?.totalDocs || 0
        };
      case 3:
        return {
          data: searchResults.topics?.docs || [],
          totalResults: searchResults.topics?.totalDocs || 0
        };
      case 4:
        return {
          data: searchResults.topicComments?.docs || [],
          totalResults: searchResults.topicComments?.totalDocs || 0
        };
      default:
        return { data: [], totalResults: 0 };
    }
  };

  const { data: currentData, totalResults } = getCurrentResults();
  const users = tabIndex === 0 ? currentData as SearchUser[] : [];
  const chats = tabIndex === 1 ? currentData as SearchChat[] : [];
  const groups = tabIndex === 2 ? currentData as SearchGroup[] : [];
  const topics = tabIndex === 3 ? currentData as SearchTopic[] : [];
  const comments = tabIndex === 4 ? currentData as SearchComment[] : [];

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    refetchSearch().finally(() => {
      setRefreshing(false);
    });
  }, [refetchSearch]);

  const openReportDetails = (itemId: string, type: string) => {
    setReportDetails({ data: { _id: itemId }, type });
    setShowReportDialog(true);
  };

  const handleUserClick = (user: SearchUser) => {
    if (user._id !== currentUserId) {
      router.push(`/messages/${user._id}`);
    }
  };

  const handleChatClick = (chat: SearchChat) => {
    if (chat.user._id !== currentUserId) {
      router.push(`/messages/${chat.user._id}`);
    }
  };

  const handleTopicClick = (topic: SearchTopic) => {
    router.push(`/topic/${topic._id}`);
  };

  const clearSearch = () => {
    clearSearchState();
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Search Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              placeholder="Search users, chats, groups, posts..."
              className="pl-10 pr-10 h-12 bg-gray-50 border-gray-200"
            />
            {searchInput && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="h-12 px-4">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {filterTypes.map((filter, index) => (
                <DropdownMenuItem
                  key={index}
                  onClick={() => setTabIndex(index)}
                  className={tabIndex === index ? 'bg-blue-50 text-blue-600' : ''}
                >
                  {filter}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="h-12 w-12 p-0"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Results Summary */}
        {searchInput.trim() && (
          <div className="flex items-center gap-2 text-sm">
            <span className={`${poppinsBold.className} text-gray-700`}>
              {totalResults} {totalResults === 1 ? 'Result' : 'Results'}
            </span>
            <span className={`${poppinsRegular.className} text-gray-500`}>
              in {filterTypes[tabIndex]}
            </span>
          </div>
        )}
      </div>

      {/* Sponsored Ad */}
      {sponsoredAd && !isAdLoading && (
        <div className="p-4 border-b border-gray-200">
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative">
                <img
                  src={sponsoredAd.image || '/api/placeholder/400/200'}
                  alt={sponsoredAd.title || 'Sponsored Ad'}
                  className="w-full h-32 object-cover"
                />
                <div className="absolute top-2 right-2">
                  <Badge variant="secondary" className="text-xs">
                    Sponsored
                  </Badge>
                </div>
              </div>
              {sponsoredAd.title && (
                <div className="p-3">
                  <h3 className={`${poppinsMedium.className} text-sm font-medium text-gray-900`}>
                    {sponsoredAd.title}
                  </h3>
                  {sponsoredAd.description && (
                    <p className={`${poppinsRegular.className} text-xs text-gray-600 mt-1`}>
                      {sponsoredAd.description}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto">
        {(loading || isSearching) ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
            <span className={`${poppinsRegular.className} ml-3 text-gray-600`}>
              Searching...
            </span>
          </div>
        ) : !searchInput.trim() ? (
          <div className="flex flex-col items-center justify-center py-16 px-4">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Search className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className={`${poppinsMedium.className} text-lg text-gray-900 mb-2`}>
              Search Everything
            </h3>
            <p className={`${poppinsRegular.className} text-gray-600 text-center max-w-sm`}>
              Find users, chats, groups, posts, and comments all in one place
            </p>
          </div>
        ) : (
          <div className="p-4">
            {/* Users Results */}
            {tabIndex === 0 && (
              <div className="space-y-3">
                {users.length === 0 ? (
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className={`${poppinsRegular.className} text-gray-600`}>
                      No users found
                    </p>
                  </div>
                ) : (
                  users.map((user) => (
                    <Card key={user._id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3 flex-1">
                            <ProfileClickHandler
                              user={{
                                _id: user._id,
                                first_name: user.first_name,
                                last_name: user.last_name,
                                profile_picture: user.profile_picture,
                                firstName: user.first_name,
                                lastName: user.last_name,
                                profilePicture: user.profile_picture
                              }}
                              defaultAction="chat"
                              showAvatar={true}
                              avatarSize={48}
                              onChatStart={(chatData) => {
                                console.log('Chat started with:', user.first_name, user.last_name);
                              }}
                            >
                              <Avatar className="h-12 w-12">
                                <AvatarImage src={user.profile_picture} />
                                <AvatarFallback>
                                  {user.first_name?.[0]}{user.last_name?.[0]}
                                </AvatarFallback>
                              </Avatar>
                            </ProfileClickHandler>

                            <div className="flex-1 min-w-0">
                              <ProfileClickHandler
                                user={{
                                  _id: user._id,
                                  first_name: user.first_name,
                                  last_name: user.last_name,
                                  profile_picture: user.profile_picture,
                                  firstName: user.first_name,
                                  lastName: user.last_name,
                                  profilePicture: user.profile_picture
                                }}
                                defaultAction="chat"
                                className="hover:text-blue-600 cursor-pointer"
                                onChatStart={(chatData) => {
                                  console.log('Chat started with:', user.first_name, user.last_name);
                                }}
                              >
                                <h3 className={`${poppinsMedium.className} text-gray-900 truncate`}>
                                  {user.first_name} {user.last_name}
                                </h3>
                              </ProfileClickHandler>

                              {user.company && (
                                <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate`}>
                                  {user.company_position} at {user.company}
                                </p>
                              )}
                              {user.bio && (
                                <p className={`${poppinsRegular.className} text-sm text-gray-500 truncate mt-1`}>
                                  {user.bio}
                                </p>
                              )}
                            </div>
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => router.push(`/profile/${user._id}`)}>
                                <User className="h-4 w-4 mr-2" />
                                View Profile
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => openReportDetails(user._id, 'user')}>
                                <Flag className="h-4 w-4 mr-2" />
                                Report User
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            )}

            {/* Chats Results */}
            {tabIndex === 1 && (
              <div className="space-y-3">
                {chats.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className={`${poppinsRegular.className} text-gray-600`}>
                      No chats found
                    </p>
                  </div>
                ) : (
                  chats.map((chat) => (
                    <Card key={chat._id} className="hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => handleChatClick(chat)}>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={chat.user.profile_picture} />
                            <AvatarFallback>
                              {chat.user.first_name?.[0]}{chat.user.last_name?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <h3 className={`${poppinsMedium.className} text-gray-900 truncate`}>
                              {chat.user.first_name} {chat.user.last_name}
                            </h3>
                            <p className={`${poppinsRegular.className} text-sm text-gray-500`}>
                              Chat conversation
                            </p>
                          </div>
                          <MessageCircle className="h-5 w-5 text-gray-400" />
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            )}

            {/* Groups Results */}
            {tabIndex === 2 && (
              <div className="space-y-3">
                {groups.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className={`${poppinsRegular.className} text-gray-600`}>
                      No groups found
                    </p>
                  </div>
                ) : (
                  groups.map((group) => (
                    <Card key={group._id} className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3 flex-1">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={group.profile_picture} />
                              <AvatarFallback>
                                {group.name?.[0]?.toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <h3 className={`${poppinsMedium.className} text-gray-900 truncate`}>
                                {group.name}
                              </h3>
                              {group.description && (
                                <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate`}>
                                  {group.description}
                                </p>
                              )}
                              {group.members_count && (
                                <p className={`${poppinsRegular.className} text-xs text-gray-500 mt-1`}>
                                  {group.members_count} members
                                </p>
                              )}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => router.push(`/group/${group._id}`)}>
                                <Users className="h-4 w-4 mr-2" />
                                View Group
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => openReportDetails(group._id, 'group')}>
                                <Flag className="h-4 w-4 mr-2" />
                                Report Group
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            )}

            {/* Topics/Posts Results */}
            {tabIndex === 3 && (
              <div className="space-y-3">
                {topics.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className={`${poppinsRegular.className} text-gray-600`}>
                      No posts found
                    </p>
                  </div>
                ) : (
                  topics.map((topic) => (
                    <Card key={topic._id} className="hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => handleTopicClick(topic)}>
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={topic.author.profile_picture} />
                            <AvatarFallback>
                              {topic.author.first_name?.[0]}{topic.author.last_name?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <span className={`${poppinsMedium.className} text-sm text-gray-900`}>
                                {topic.author.first_name} {topic.author.last_name}
                              </span>
                              <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                                {new Date(topic.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            <h3 className={`${poppinsMedium.className} text-gray-900 mb-2 line-clamp-2`}>
                              {topic.title}
                            </h3>
                            <p className={`${poppinsRegular.className} text-sm text-gray-600 line-clamp-3 mb-3`}>
                              {topic.content}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              {topic.likes_count !== undefined && (
                                <span className="flex items-center gap-1">
                                  <span>❤️</span>
                                  {topic.likes_count}
                                </span>
                              )}
                              {topic.comments_count !== undefined && (
                                <span className="flex items-center gap-1">
                                  <MessageSquare className="h-3 w-3" />
                                  {topic.comments_count}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            )}

            {/* Comments Results */}
            {tabIndex === 4 && (
              <div className="space-y-3">
                {comments.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className={`${poppinsRegular.className} text-gray-600`}>
                      No comments found
                    </p>
                  </div>
                ) : (
                  comments.map((comment) => (
                    <Card key={comment._id} className="hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => router.push(`/topic/${comment.topic_id}`)}>
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={comment.author.profile_picture} />
                            <AvatarFallback>
                              {comment.author.first_name?.[0]}{comment.author.last_name?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <span className={`${poppinsMedium.className} text-sm text-gray-900`}>
                                {comment.author.first_name} {comment.author.last_name}
                              </span>
                              <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                                commented on {new Date(comment.created_at).toLocaleDateString()}
                              </span>
                            </div>
                            <p className={`${poppinsRegular.className} text-sm text-gray-600 line-clamp-3`}>
                              {comment.content}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchScreen;

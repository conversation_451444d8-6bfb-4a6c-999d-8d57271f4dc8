'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { poppinsMedium, poppinsRegular, poppinsSemibold } from '@/fonts';
import { RefreshCw } from 'lucide-react';
import {
  useGetSubscription,
  useGetFreeSubscription,
  useGetCredit
} from '@/redux/credit/hooks';
import { SponsorshipPlansModal } from '@/components/ui/sponsorship-plans-modal';

interface MembershipProps {
  closeModal?: () => void;
}

const Membership: React.FC<MembershipProps> = ({ closeModal }) => {
  const [showSponsorshipPlans, setShowSponsorshipPlans] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error' | null>(null);

  const {
    data: subscription,
    isLoading: isSubscriptionLoading,
    refetch: refetchSubscription,
    error: subscriptionError
  } = useGetSubscription();

  const {
    data: credit,
    isLoading: isCreditLoading,
    refetch: refetchCredit
  } = useGetCredit();

  const {
    mutate: getFreeSubscription,
    isPending: freeSubscriptionLoading
  } = useGetFreeSubscription();

  const handleRefresh = () => {
    refetchSubscription();
    refetchCredit();
  };

  const handleSponsorshipPlans = () => {
    setShowSponsorshipPlans(true);
  };

  const handleFreePlan = () => {
    // Check if the subscription exists and if daysLeft is not 0
    if (subscription && subscription.daysLeft !== 0) {
      setAlertMessage('You still have days left on your current plan');
      setAlertType('error');
      return;
    }

    getFreeSubscription(undefined, {
      onSuccess: (response: any) => {
        setAlertMessage(response.message || 'Free subscription activated successfully!');
        setAlertType('success');
        refetchSubscription();
      },
      onError: (error: any) => {
        setAlertMessage(
          error.response?.data?.message ||
          error.message ||
          "Looks like you've already received your free subscription in the past 5 days"
        );
        setAlertType('error');
      },
    });
  };

  // Clear alert after 5 seconds
  useEffect(() => {
    if (alertMessage) {
      const timer = setTimeout(() => {
        setAlertMessage('');
        setAlertType(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [alertMessage]);

  const getDaysLeftDisplay = () => {
    if (isSubscriptionLoading) {
      return (
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#5A5E5C]"></div>
        </div>
      );
    }

    if (subscription) {
      return (
        <div className="text-center">
          <p className={`${poppinsMedium.className} text-[2.5rem] text-[#797978] mb-2`}>
            {subscription.daysLeft}
          </p>
          <p className={`${poppinsRegular.className} text-[0.875rem] text-[#797978]`}>
            {subscription.daysLeft === 1 ? 'day' : 'days'}
          </p>
        </div>
      );
    }

    return (
      <div className="text-center">
        <p className={`${poppinsMedium.className} text-[2.5rem] text-[#797978] mb-2`}>
          0
        </p>
        <p className={`${poppinsRegular.className} text-[0.875rem] text-[#797978]`}>
          No Subscription
        </p>
      </div>
    );
  };

  // This matches the React Native logic exactly
  const getCurrentPlanDisplay = () => {
    if (subscription && subscription.subscription) {
      return (
        <h2 className={`${poppinsSemibold.className} text-[1rem] text-[#696969]`}>
          {subscription.subscription.subscriptionType.charAt(0).toUpperCase() +
           subscription.subscription.subscriptionType.slice(1)} Plan
        </h2>
      );
    }
    return (
      <h2 className={`${poppinsSemibold.className} text-[1rem] text-[#696969]`}>
        Free Plan
      </h2>
    );
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      {/* Alert Messages */}
      {alertMessage && (
        <div className={`mb-6 p-4 rounded-lg border ${
          alertType === 'success'
            ? 'bg-green-50 border-green-200 text-green-700'
            : 'bg-red-50 border-red-200 text-red-700'
        }`}>
          <p className={`${poppinsRegular.className} text-[0.875rem]`}>
            {alertMessage}
          </p>
        </div>
      )}

      {/* Header with Refresh */}
      <div className="flex items-center justify-between mb-8">
        <h1 className={`${poppinsMedium.className} text-[1.5rem] text-[#5A5E5C]`}>
          Membership Access
        </h1>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={isSubscriptionLoading}
          className="p-2"
        >
          <RefreshCw className={`w-4 h-4 ${isSubscriptionLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Membership Days Left */}
      <div className="text-center mb-8">
        <p className={`${poppinsMedium.className} text-[0.9375rem] text-[#797978] mb-4`}>
          Membership days left
        </p>
        {getDaysLeftDisplay()}
      </div>

      {/* Action Buttons */}
      <div className="space-y-4 mb-8">
        <Button
          onClick={handleSponsorshipPlans}
          className="w-full bg-[#F56630] hover:bg-[#E55520] text-white rounded-lg py-3"
          disabled={isSubscriptionLoading}
        >
          <span className={`${poppinsMedium.className} text-[1rem]`}>
            Sponsorship Plans
          </span>
        </Button>

        <Button
          onClick={handleFreePlan}
          variant="outline"
          className="w-full border-[#F56630] text-[#F56630] hover:bg-[#F56630] hover:text-white rounded-lg py-3"
          disabled={freeSubscriptionLoading || isSubscriptionLoading}
        >
          <span className={`${poppinsMedium.className} text-[1rem] flex items-center justify-center gap-2`}>
            {freeSubscriptionLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            )}
            Free Plan
          </span>
        </Button>
      </div>

      {/* Current Plan Display - Matches React Native exactly */}
      <div className="border border-[#F0F2F5] rounded-lg p-6">
        <p className={`${poppinsMedium.className} text-[0.875rem] text-[#4F4F4F] mb-4`}>
          Current Plan
        </p>
        <div className="text-center">
          {getCurrentPlanDisplay()}
        </div>
      </div>

      {/* Credit Information (if available) */}
      {credit && (
        <div className="mt-6 border border-[#F0F2F5] rounded-lg p-6">
          <p className={`${poppinsMedium.className} text-[0.875rem] text-[#4F4F4F] mb-4`}>
            Available Credits
          </p>
          <div className="text-center">
            <p className={`${poppinsMedium.className} text-[1.5rem] text-[#696969]`}>
              {isCreditLoading ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#5A5E5C] mx-auto"></div>
              ) : (
                credit.amount || 0
              )}
            </p>
          </div>
        </div>
      )}

      {/* Sponsorship Plans Modal */}
      <SponsorshipPlansModal
        isOpen={showSponsorshipPlans}
        onClose={() => setShowSponsorshipPlans(false)}
        currentPlan={subscription?.subscription?.subscriptionType}
      />
    </div>
  );
};

export default Membership;

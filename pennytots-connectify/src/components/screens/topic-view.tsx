'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

import { Loading } from '@/components/ui/loading';
import { TopicCard } from './topic-list';
import { useSingleTopic, useTopicComments} from '@/redux/topic/hooks';
import { poppinsSemibold, poppinsRegular } from '@/fonts';



interface Comment {
  _id: string;
  comment: string;
  userId: {
    _id: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
    company?: string;
    company_position?: string;
    subscriptionType?: string;
  };
  createdAt: string;
  subComments?: Comment[];
  likes?: number;
  isLiked?: boolean;
  subCommentsCount?: number;
  image?: string;
  document?: string;
  audio?: string;
  video?: string;
  attachmentSize?: string;
}

interface TopicViewProps {
  topicId: string;
}

export default function TopicView({ topicId }: TopicViewProps) {
  const router = useRouter();
 
  

  // Fetch topic data
  const { 
    data: topicData, 
    isLoading: topicLoading, 
    error: topicError,
    refetch: refetchTopic 
  } = useSingleTopic(topicId);

  // Fetch comments
  const { 
    data: comments, 
    isLoading: commentsLoading,
    refetch: refetchComments 
  } = useTopicComments(topicId);


  const handleBack = () => {
    router.back();
  };



  if (topicLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loading size="lg" />
      </div>
    );
  }

  if (topicError || !topicData) {
    return (
      <div className="min-h-[400px] flex flex-col items-center justify-center">
        <h2 className={`${poppinsSemibold.className} text-xl text-gray-900 mb-2`}>
          Topic Not Found
        </h2>
        <p className={`${poppinsRegular.className} text-gray-600 mb-4`}>
          The topic you're looking for doesn't exist or has been removed.
        </p>
        <Button onClick={handleBack} variant="outline">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-full">
      {/* Header */}
  <div className="bg-white">
          <TopicCard
            item={topicData}
            convo={false}
            refreshFunction={refetchTopic}
            onToggleMuted={() => {}}
            muted={false}
          />
        </div>
      </div>
   
  );
}
 
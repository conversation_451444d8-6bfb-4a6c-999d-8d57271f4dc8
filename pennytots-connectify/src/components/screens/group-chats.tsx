'use client';

import React, { useState } from 'react';
import { Search, Users, Plus, MoreVertical, UserX, VolumeX, Volume2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import { useRouter } from 'next/navigation';
import { useMyGroups, useSuggestedGroups, useJoinGroup, useLeaveGroup } from '@/redux/group/hooks';
import { GroupProps } from '@/redux/group/types';
import { Loading } from '@/components/ui/loading';
import { timeAgo } from '@/lib/time-utils';

const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 24) {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }
};

const GroupChat = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'my-groups' | 'recommended'>('my-groups');
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { data: myGroups, isLoading: myGroupsLoading, refetch: refetchMyGroups } = useMyGroups();
  const { data: suggestedGroups, isLoading: suggestedLoading, refetch: refetchSuggested } = useSuggestedGroups();

  const router = useRouter();

  const currentGroups = activeTab === 'my-groups' ? myGroups : suggestedGroups;
  const isLoading = activeTab === 'my-groups' ? myGroupsLoading : suggestedLoading;

  const filteredGroups = currentGroups?.filter((group: GroupProps) =>
    group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    group.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const handleGroupPress = (group: GroupProps) => {
    router.push(`/group-chat/${group._id}`);
  };

  const handleCreateGroup = () => {
    setShowCreateModal(true);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className=" border-b">
        <div className="flex items-center justify-between mb-4">
          <h1 className={`${poppinsSemibold.className} text-xl text-gray-900`}>
            Groups
          </h1>
          <Button
            onClick={handleCreateGroup}
            size="sm"
            className="bg-[#F2875D] hover:bg-[#E07A52] text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Group
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search groups..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Group List */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loading size="lg" />
          </div>
        ) : filteredGroups.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64">
            <Users className="h-12 w-12 text-gray-400 mb-4" />
            <p className={`${poppinsRegular.className} text-gray-500 text-center`}>
              {searchQuery ? 'No groups found' : 'No groups yet'}
            </p>
            {!searchQuery && (
              <Button
                onClick={handleCreateGroup}
                variant="outline"
                className="mt-4"
              >
                Create Your First Group
              </Button>
            )}
          </div>
        ) : (
          filteredGroups.map((group: GroupProps) => (
            <GroupCard
              key={group._id}
              group={group}
              onPress={() => handleGroupPress(group)}
              isMyGroup={activeTab === 'my-groups'}
            />
          ))
        )}
      </div>
    </div>
  );
};

interface GroupCardProps {
  group: GroupProps;
  onPress: () => void;
  isMyGroup: boolean;
    profilePicture?: string; // Added property

   lastMessage?: {
    sender: {
      firstName: string;
      // add other sender fields if needed
    };
    message: string;
    createdAt: string;
  };
}

const GroupCard: React.FC<GroupCardProps> = ({
  group,
  onPress,
  isMyGroup,
}) => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div className="relative">
      <div
        onClick={onPress}
        className="flex items-center  hover:bg-gray-50 cursor-pointer border-b border-gray-100"
      >
        {/* Group Avatar */}
        <div className="relative mr-3">
          <div className="w-12 h-12 rounded-full bg-[#F2875D] flex items-center justify-center">
            {group.profilePicture ? (
              <img
                src={group.profilePicture}
                alt="Group"
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <Users className="h-6 w-6 text-white" />
            )}
          </div>
          {group.unreadCount && group.unreadCount > 0 && (
            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {group.unreadCount > 9 ? '9+' : group.unreadCount}
            </div>
          )}
        </div>

        {/* Group Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className={`${poppinsSemibold.className} text-gray-900 truncate`}>
                {group.name}
              </h3>
              {group.isAdmin && (
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  Admin
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              {group.muted && <VolumeX className="h-4 w-4 text-gray-400" />}
              {group.lastMessage && (
                <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                  {formatTime(group.lastMessage.createdAt)}
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2 mt-1">
            <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
              {group.memberCount} members
            </span>
            {group.lastMessage && (
              <>
                <span className="text-gray-300">•</span>
                <p className={`${poppinsRegular.className} text-sm text-gray-600 truncate`}>
                  {group.lastMessage.sender.firstName}: {group.lastMessage.message}
                </p>
              </>
            )}
          </div>
        </div>

        {/* Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            setShowMenu(!showMenu);
          }}
          className="ml-2 p-1"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>

      {/* Dropdown Menu */}
      {showMenu && (
        <div className="absolute right-4 top-16 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[150px]">
          <button
            onClick={(e) => {
              e.stopPropagation();
              // Handle mute toggle
              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            {group.muted ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            {group.muted ? 'Unmute' : 'Mute'}
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              // Handle leave group
              setShowMenu(false);
            }}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
          >
            <UserX className="h-4 w-4" />
            Leave Group
          </button>
        </div>
      )}
    </div>
  );
};

export default GroupChat;

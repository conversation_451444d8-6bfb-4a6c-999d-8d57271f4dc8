import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { userAuthInfo, updateUser } from '@/redux/user/reducer';
import { updateUserInfo } from '@/redux/user/hooks';
import { toast } from 'react-toastify'; 
import { Axios } from '@/api/axios';
import { SelectInterests } from '../elements/SelectInterests';
import { useRouter } from 'next/navigation'
import { Button } from '../ui/button';
import { tags } from '@/api/tags';
import { useSetUserTags } from '@/redux/tags/hooks';
import { poppinsMedium } from '@/fonts';


interface AreaOfInterestProps {
  closeModal?:() => void;
  // Next.js pages don't typically receive navigation as props
  // Router is accessed via useRouter hook
}

const AreaOfInterest: React.FC<AreaOfInterestProps> = ({closeModal}) => {
  const router = useRouter();
  const profile = useSelector(userAuthInfo);
const { mutateAsync: setUserTags, isPending: isLoading } = useSetUserTags();
console.log(isLoading, 'isloading')
  const [interests, setInterests] = useState<string[]>([]);
  const [loading, setLoading] = useState(false)

useEffect(() => {

  const loadInterests = async () => {
    try {
      if (profile && profile.interests) {
        setInterests(profile.interests);
      }
    } catch (error) {
      console.error("Error loading interests:", error);
    } finally {
     
    }
  };

  loadInterests();
}, [profile]); // ✅ fixed: removed `loading`

  const updateAreaOfInterests = async () => {

    setLoading(true)
    if (interests.length === 0) {
            toast.error('Please select at least one tag', {
                toastId: 'empty-fields-error',
                 onClose: () => {
          closeModal?.(); // ✅ Close the modal after success
        }, // Prevent duplicate toasts
              });
     
      return;
    }

    try {
       await setUserTags(interests);
      
      // Update user info
      await updateUserInfo();
      
   
      
    } catch (error) {
      console.error('Error updating interests:', error);
           toast.error('Error failed to upload interest', {
                toastId: 'empty-fields-error' // Prevent duplicate toasts
              });
    } finally {
     setLoading(false)
     closeModal?.()
    }
  };


  return (
    <div className="bg-white w-full mt-[8px]">
      <div className="bg-white w-full">
    <h1 className={`mb-[1.5rem] text-[#4F4F4F] text-[0.875rem] ${poppinsMedium.className}`}>Pick the topic you are interested in</h1>
        <div className="flex flex-col justify-center items-center">
          <div className="w-full">
        <div className="flex w-full flex-col">
  {isLoading ? (
    <div className="flex justify-center items-center h-32">
      <svg
        className="animate-spin h-8 w-8 text-gray-500"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
        />
      </svg>
    </div>
  ) : (
    <SelectInterests
      interests={interests}
      setInterests={setInterests}
      multiple={true}
    />
  )}
</div>

            
                <div className="pt-[1.5rem]">
           
                       <Button
                         onClick={updateAreaOfInterests}
                         disabled={isLoading}
                         variant="primary"
                         className="justify-center w-full flex"
                       >
                         <span className="flex items-center text-white ">
                           {loading && (
                             <svg
                               className="animate-spin h-4 w-4"
                               xmlns="http://www.w3.org/2000/svg"
                               fill="none"
                               viewBox="0 0 24 24"
                             >
                               <circle
                                 className="opacity-25"
                                 cx="12"
                                 cy="12"
                                 r="10"
                                 stroke="currentColor"
                                 strokeWidth="4"
                               />
                               <path
                                 className="opacity-75"
                                 fill="currentColor"
                                 d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                               />
                             </svg>
                           )}
                           {loading ? 'Creating...' : 'Create'}
                         </span>
                       </Button>
           
                     </div>
            </div>
          </div>
        </div>
      </div>

  );
};

export default AreaOfInterest;
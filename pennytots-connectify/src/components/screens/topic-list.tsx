'use client';

import { useTopics } from '../../redux/topic/hooks';
import React from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Loading } from '@/components/ui/loading';
import { useRouter } from 'next/navigation';
import {
  Heart,
  MessageCircle,
  MoreVertical,
  Edit,
  Flag,
  VolumeX,
  Volume2,
  Trash2,
  UserPlus
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Avatar } from '@/components/ui/avatar';
import { useSelector } from 'react-redux';
import { userId, userToken, userAuthInfo, IsLoggedIn } from '../../redux/user/reducer';
import { ImageViewer } from '@/components/ui/image-viewer';
import { topic } from '../../api';
import { timeAgo } from '@/lib/time-utils';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { poppinsMedium, poppinsSemibold } from '@/fonts';
import { useTrackActivity } from '@/providers/ActivityContext';
import CommentIcon from "@/assets/svg/comment.svg";
import ThumbSVG from '../svgReactComponents/ThumbSVG';
import { useEnsureFreshProfile } from '../../redux/user/hooks';
import ProfileClickHandler from '../profile/ProfileClickHandler';

interface TopicProps {
  topicId?: string;
}

function Topic({ topicId }: TopicProps) {
  const { status, data, error, isFetching, refetch, isLoading } = useTopics();
  console.log(data, 'data')
  const router = useRouter();
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>('');

  // Ensure user profile is always fresh
  const { userProfile, isLoggedIn } = useEnsureFreshProfile();

  return (
    <div className="flex flex-col w-full min-h-full">
      {/* Topics List */}
      {isLoading ? (
        <div className="flex-1 flex justify-center items-center min-h-[400px]">
          <Loading size="lg" />
        </div>
      ) : data && data.length > 0 ? (
        <div className="pb-4">
          {data.map((item: any) => (
            <TopicCard
              key={item._id}
              item={item}
              convo={false}
              refreshFunction={() => refetch()}
              onToggleMuted={() => {
                throw new Error('Function not implemented.');
              }}
              muted={false}
            />
          ))}
        </div>
      ) : (
        <div className="flex-1 flex flex-col justify-center items-center space-y-4 min-h-[400px]">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-700">No Topics Found</h3>
            <p className="text-gray-500 mt-2">There are no topics to display yet.</p>
            <p className="text-gray-500">Be the first to create a topic!</p>
          </div>
          <Button
            onClick={() => router.push('/create-topic')}
            className="flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Topic</span>
          </Button>
        </div>
      )}
    </div>
  );
}




export type TopicCardProps = {
  item?: any;
  convo?: boolean;
  onToggleMuted?: () => void;
  muted?: boolean;
  refreshFunction?: () => void;
};

export const TopicCard: React.FC<TopicCardProps> = ({
  item,
  onToggleMuted,
  convo = false,
  refreshFunction,
}) => {
  const router = useRouter();
  const isLoggedIn = useSelector(IsLoggedIn);
  const Id = useSelector(userId);
  const token = useSelector(userToken);
  const userAuth = useSelector(userAuthInfo);

  const myId = useSelector(userId);
  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);
  const [isMuted, setIsMuted] = useState<boolean>(item?.muted || false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>('');

  // Activity tracking for sponsored ads
  const { trackActivity: trackSponsor, activityCounts } = useTrackActivity('sponsor');
  const { trackActivity: trackShare } = useTrackActivity('share');

  // Debug logging
  console.log('Activity counts:', activityCounts);

  useEffect(() => {
    if (item?.isLiked && item?.likes) {
      setIsLiked(item.isLiked);
      setNumLikes(item.likes);
    }
  }, [item]);

  function handleLike() {
    setIsLiked(!isLiked);
    setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);

    // Track sponsor activity when user likes a post
    trackSponsor();

    topic
      .likeTopic(item._id)
      .then((data) => {
        refreshFunction?.();
      })
      .catch((err) => {
        setIsLiked(!isLiked);
        setNumLikes(isLiked ? numLikes + 1 : numLikes - 1);
      });
  }

  const handleViewTopic = () => {
    // Track sponsor activity when user views a topic
    trackSponsor();
    router.push(`/topic/${item._id}`);
  };

  const handleEditTopic = () => {
    router.push(`/edit-topic/${item._id}`);
  };

  const handleDeleteTopic = async () => {
    try {
      await topic.deleteTopic(item._id);
      refreshFunction?.();
    } catch (error) {
      console.error('Error deleting topic:', error);
    }
  };

  return (
    <div className="w-full flex flex-col  border-b-2 border-[#F2F2F2]">

  
    <div
      className="w-full  flex flex-row justify-start px-[1rem]  items-start bg-white py-[1rem] gap-[1.25rem] cursor-pointer hover:bg-gray-50 "

      // onClick={handleViewTopic}
    >
      {/* Avatar Section */}
      <div className="w-[68px] h-[68px] flex justify-end">
        <div className="mb-2">
          <Avatar
            source={item?.userId?.profile_picture}
            size={68}
            type="user"
          />
        </div>
      </div>

      {/* Content Section */}
      <div className="w-full flex flex-col">
        {/* Header */}
        <div className="flex flex-row items-center justify-between">
          <div className="flex flex-row">
            <div className=" justify-center">
             {item.userId && (
  <ProfileClickHandler user={item.userId}>
    <h3 className={`text-[#5A5E5C] text-[1rem] hover:text-[#F56630] cursor-pointer ${poppinsSemibold.className}`}>
      {`${item.userId.first_name} ${item.userId.last_name}`.slice(0, 35)}
      {(item.userId.first_name.length + item.userId.last_name.length > 35) ? '...' : ''}
    </h3>
  </ProfileClickHandler>
)}

            </div>
          </div>

          {/* Menu */}
          <div className="flex items-center justify-end cursor-pointer">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="cursor-pointer">
                  <MoreVertical className="h-4 w-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {myId === item?.userId?._id && (
                  <DropdownMenuItem onClick={handleEditTopic}>
                    <Edit className="mr-2 h-4 w-4 " />
                    Edit post
                  </DropdownMenuItem>
                )}

                {myId !== item?.userId?._id && (
                  <DropdownMenuItem>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Follow
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem>
                  {item?.muted ? (
                    <>
                      <Volume2 className="mr-2 h-4 w-4" />
                      Unmute
                    </>
                  ) : (
                    <>
                      <VolumeX className="mr-2 h-4 w-4" />
                      Mute
                    </>
                  )}
                </DropdownMenuItem>

                {myId === item?.userId?._id && (
                  <DropdownMenuItem onClick={handleDeleteTopic} className="text-red-600">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}

                {myId !== item?.userId?._id && (
                  <DropdownMenuItem className="text-red-600">
                    <Flag className="mr-2 h-4 w-4" />
                    Report
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Position at Company */}
        <div className="w-full">
          <p className={` text-[#696969] text-[14px] ${poppinsMedium.className} `}>
            {item?.userId?.company_position && item?.userId?.company
              ? `${item.userId.company_position?.replace(/(\r\n|\n|\r)/gm, "") || "position"} at ${item.userId.company}`
              : "Position at Company"}
          </p>
        </div>

        {/* User Info */}
        <div className="w-full">
          <p className={` text-[#696969] text-[14px] ${poppinsMedium.className} `}>
            {item?.userId?.subscriptionType
              ? `${item.userId.subscriptionType.charAt(0).toUpperCase() + item.userId.subscriptionType.slice(1)} Member`
              : 'Free Member'}
          </p>
        </div>

        {/* Time */}
        <div className="flex items-start">
          <p className={` text-[#696969] text-[14px] mt-[4px] ${poppinsMedium.className} `}>
            {timeAgo(item?.createdAt)}
          </p>
        </div>

        {/* Content */}
        <div className="">
          <div className="flex flex-col">
            <p className={` text-[#5A5E5C] text-[14px] mt-[8px] ${poppinsMedium.className} `}>
              {item?.content}
            </p>

            {/* Image */}
            {item?.image && (
              <div className="flex-1 w-full mt-1 max-w-[492px]">
                <div
                  className="relative w-full h-48 cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => {
                    setSelectedImage(item.image!);
                    setShowImageViewer(true);
                  }}
                >
                  <Image
                    src={item.image}
                    alt="Topic image"
                    fill
                    className="object-cover rounded-lg border border-white"
                  />
                </div>
              </div>
            )}

            {/* Audio/Video/Document attachments would go here */}
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-row items-center justify-between mt-[1rem]">
          <div className="flex flex-row items-center justify-between w-full py-[8px]">
            {/* Comment Button */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                // Track sponsor activity when user clicks comment
                trackSponsor();
                router.push(`/comment/${item._id}`);
              }}
              className="flex items-center gap-[8px] cursor-pointer hover:bg-gray-100 p-2 rounded"
            >
              <CommentIcon
                width={24}
                height={24}
                className="comment-icon"
                alt="comment svg"
              />
              <span className={` text-[#5A5E5C] text-[14px] ${poppinsMedium.className} `}>
                {item.noOfComments || 0} Comment{(item.noOfComments || 0) !== 1 ? "s" : ""}
              </span>
            </div>

            {/* Like Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleLike();
              }}
              className="flex items-center gap-[8px] hover:bg-gray-100 p-2 rounded cursor-pointer"
            >
              <ThumbSVG color={isLiked ? "#FE3233" : "#5A5E5C"} />
              <span className={` ${poppinsMedium.className} text-[14px]  ${isLiked ? "text-[#FE3233]" : "text-[#5A5E5C]"
                }  `}> {numLikes || 0} Like{(numLikes || 0) !== 1 ? "s" : ""} </span>
            </Button>


          </div>
        </div>
      </div>
    </div>

      {/* Image Viewer */}
      <ImageViewer
        isOpen={showImageViewer}
        onClose={() => setShowImageViewer(false)}
        imageUrl={selectedImage}
        alt="Topic image"
      />
    </div>
  );
};

export default Topic;


'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Send, Heart, MessageCircle, MoreVertical, Reply, Edit, UserPlus, Volume2, VolumeX, Trash2, Flag } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loading } from '@/components/ui/loading';
import { TopicCard } from './topic-list';
import { useSingleTopic, useTopicComments, useCreateComment, useTopicSubComments, useCreateSubComment } from '@/redux/topic/hooks';
import { poppinsSemibold, poppinsRegular, poppinsMedium } from '@/fonts';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';

import { userId } from '@/redux/user/reducer';
import { Avatar } from '@/components/ui/avatar';
import { timeAgo } from '@/lib/time-utils';
import Image from 'next/image';
import EmojiPicker from '@/components/ui/emoji-picker';
import SelectAttachment from '@/components/ui/select-attachment';
import { ImageViewer } from '@/components/ui/image-viewer';
import FilePreview from '@/components/ui/file-preview';
import ReplyModal from '@/components/ui/reply-modal';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { topic } from '@/api/topic';
import ReplySVG from '@/assets/svg/reply.svg';
import CommentIcon from "@/assets/svg/comment.svg";
import SendIcon from "@/assets/svg/send.svg";

import ThumbSVG from '../svgReactComponents/ThumbSVG';

interface Comment {
    _id: string;
    comment: string;
    userId: {
        _id: string;
        first_name: string;
        last_name: string;
        profile_picture?: string;
        company?: string;
        company_position?: string;
        subscriptionType?: string;
    };
    createdAt: string;
    subComments?: Comment[];
    likes?: number;
    isLiked?: boolean;
    subCommentsCount?: number;
    image?: string;
    document?: string;
    audio?: string;
    video?: string;
    attachmentSize?: string;
}

interface TopicViewProps {
    topicId: string;
}

export default function TopicView({ topicId }: TopicViewProps) {
    const router = useRouter();
    const myId = useSelector(userId);

    const [commentText, setCommentText] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [showReplyModal, setShowReplyModal] = useState(false);
    const [replyModalComment, setReplyModalComment] = useState<Comment | null>(null);
    const [showImageViewer, setShowImageViewer] = useState(false);
    const [selectedImage, setSelectedImage] = useState<string>('');

    // Fetch topic data
    const {
        data: topicData,
        isLoading: topicLoading,
        error: topicError,
        refetch: refetchTopic
    } = useSingleTopic(topicId);

    // Fetch comments
    const {
        data: comments,
        isLoading: commentsLoading,
        refetch: refetchComments
    } = useTopicComments(topicId);

    // Create comment mutation
    const createCommentMutation = useCreateComment();
    const createSubCommentMutation = useCreateSubComment();
    const currentUser = useSelector(userAuthInfo);
    const handleBack = () => {
        router.back();
    };

    const handleSubmitComment = async () => {
        if ((!commentText.trim() && !selectedFile) || isSubmitting) return;

        setIsSubmitting(true);
        try {
            // Creating a regular comment only
            const formData = new FormData();
            formData.append('comment', commentText.trim());

            if (selectedFile) {
                formData.append('file', selectedFile);

                // Determine attachment type based on file type
                let attachmentType: string;
                if (selectedFile.type.startsWith('image/')) {
                    attachmentType = 'image';
                } else if (selectedFile.type.startsWith('video/')) {
                    attachmentType = 'video';
                } else if (selectedFile.type.startsWith('audio/')) {
                    attachmentType = 'audio';
                } else {
                    attachmentType = 'document';
                }

                formData.append('attachmentType', attachmentType);
            }

            await createCommentMutation.mutateAsync({
                postId: topicId,
                data: formData
            });

            setCommentText('');
            setSelectedFile(null);
            refetchComments();
        } catch (error) {
            console.error('Error creating comment:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleReply = (comment: Comment) => {
        setReplyModalComment(comment);
        setShowReplyModal(true);
    };

    const handleReplySubmit = async (replyText: string) => {
        if (!replyModalComment) return;

        try {
            await createSubCommentMutation.mutateAsync({
                commentId: replyModalComment._id,
                data: { comment: replyText }
            });

            refetchComments();
        } catch (error) {
            console.error('Error creating reply:', error);
            throw error;
        }
    };

    const handleCloseReplyModal = () => {
        setShowReplyModal(false);
        setReplyModalComment(null);
    };

    const handleImageClick = (imageUrl: string) => {
        setSelectedImage(imageUrl);
        setShowImageViewer(true);
    };

    const handleEmojiSelect = (emoji: string) => {
        setCommentText(prev => prev + emoji);
    };

    const handleFileSelect = (file: File) => {
        setSelectedFile(file);
    };

    const handleRemoveFile = () => {
        setSelectedFile(null);
    };

    if (topicLoading) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <Loading size="lg" />
            </div>
        );
    }

    if (topicError || !topicData) {
        return (
            <div className="min-h-[400px] flex flex-col items-center justify-center">
                <h2 className={`${poppinsSemibold.className} text-xl text-gray-900 mb-2`}>
                    Topic Not Found
                </h2>
                <p className={`${poppinsRegular.className} text-gray-600 mb-4`}>
                    The topic you're looking for doesn't exist or has been removed.
                </p>
                <Button onClick={handleBack} variant="outline">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Go Back
                </Button>
            </div>
        );
    }

    return (
        <div className="bg-white min-h-full">
            {/* Header */}
            <div className="bg-white sticky top-0 z-10">
                <div className="px-[1rem]">
                    <div className="flex items-center gap-[1rem]">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleBack}
                            className="p-2"
                        >
                            <ArrowLeft className="h-5 w-5" color='#5A5E5C' />
                        </Button>
                        <h1 className={`${poppinsMedium.className} text-[#5A5E5C] text-[1rem] `}>
                            Comment
                        </h1>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="w-full">
                {/* Topic Card */}
                <div className="bg-white">
                    <TopicCard
                        item={topicData}
                        convo={false}
                        refreshFunction={refetchTopic}
                        onToggleMuted={() => { }}
                        muted={false}
                    />
                </div>

                {/* Comments Section */}
                <div className="bg-white ">
                    <div className="px-4 py-4">
                        <div className="w-full flex gap-[20px] justify-center ">
                            <div className="">
                                <Avatar
                                    size={40}
                                    type="user"
                                    profilePicture={currentUser?.profile_picture || currentUser?.thumbnail}
                                    source={currentUser?.profile_picture || currentUser?.thumbnail}
                                />
                            </div>
                            <div className="mb-6 w-full max-w-[518px]">
                                {/* File Preview */}
                                {selectedFile && (
                                    <div className="mb-3">
                                        <FilePreview
                                            file={selectedFile}
                                            onRemove={handleRemoveFile}
                                        />
                                    </div>
                                )}

                                <div className="">
                                    <Textarea
                                        value={commentText}
                                        onChange={(e) => setCommentText(e.target.value)}
                                        placeholder="Post your comment"
                                        className={`pt-[17px] text-[#B5B5B5] text-[14px] pb-[50px] px-[25px] rounded-[1rem] border border-[#FCFBF7] focus:outline-none ${poppinsMedium.className}`}
                                        rows={3}
                                    />

                                    {/* Emoji and Attachment Controls */}

                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="  flex items-center ">
                                        <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                                        <SelectAttachment onFileSelect={handleFileSelect} />
                                    </div>

                                    <div className="flex justify-end">
                                        <Button
                                            onClick={handleSubmitComment}
                                            disabled={(!commentText.trim() && !selectedFile) || isSubmitting}
                                            className={`flex items-center gap-[8px] bg-[#F56E30] p-[10px] rounded-[1rem] text-white text-[14px] ${poppinsMedium.className}`}
                                        >
                                            <SendIcon />

                                            {isSubmitting ? 'Sending...' : 'Send'}
                                        </Button>
                                    </div>
                                </div>

                            </div>
                        </div>
                        {/* Comment Input */}


                        {/* Comments List */}
                        <div className="">
                            {commentsLoading ? (
                                <div className="flex justify-center py-8">
                                    <Loading size="md" />
                                </div>
                            ) : comments && comments.length > 0 ? (
                                comments.map((comment: Comment) => (
                                    <CommentItem
                                        key={comment._id}
                                        comment={comment}
                                        topicId={topicId}
                                        onRefresh={refetchComments}
                                        onReply={handleReply}
                                        onImageClick={handleImageClick}
                                    />
                                ))
                            ) : (
                                <div className="text-center py-8">
                                    <p className={`${poppinsRegular.className} text-gray-500`}>
                                        No comments yet. Be the first to comment!
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Reply Modal */}
            {replyModalComment && (
                <ReplyModal
                    isOpen={showReplyModal}
                    onClose={handleCloseReplyModal}
                    comment={replyModalComment}
                    onSubmit={handleReplySubmit}
                />
            )}

            {/* Image Viewer */}
            <ImageViewer
                isOpen={showImageViewer}
                onClose={() => setShowImageViewer(false)}
                imageUrl={selectedImage}
                alt="Comment attachment"
            />
        </div>
    );
}

interface CommentItemProps {
    comment: Comment;
    topicId: string;
    onRefresh: () => void;
    onReply?: (comment: Comment) => void;
    onImageClick?: (imageUrl: string) => void;
}

const CommentItem: React.FC<CommentItemProps> = ({ comment, onReply, onRefresh, onImageClick }) => {
    const myId = useSelector(userId);
    const [isLiked, setIsLiked] = useState(comment.isLiked || false);
    const [numLikes, setNumLikes] = useState(comment.likes || 0);
    const [showSubComments, setShowSubComments] = useState(false);
    const router = useRouter();
    // Fetch subcomments when showSubComments is true
    const { data: subComments, isLoading: loadingSubComments } = useTopicSubComments(
        showSubComments ? comment._id : ''
    );
    const handleEditTopic = () => {
        router.push(`/edit-topic/${comment._id}`);
    };

    const handleDeleteTopic = async () => {
        try {
            await topic.deleteComment(comment._id);
            onRefresh();
        } catch (error) {
            console.error('Error deleting topic:', error);
        }
    };

    const handleLike = () => {
        // TODO: Implement like functionality
        setIsLiked(!isLiked);
        setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);
    };

    const handleReply = () => {
        if (onReply) {
            onReply(comment);
        }
    };

    return (
        <div className="flex w-full border-b-2 border-[#F2F2F2]">


            <div className="w-full flex flex-col justify-start pl-[48px] items-start bg-white py-[1rem] gap-[1.25rem]">
                {/* Avatar Section */}
                <div className="flex w-full gap-[10px]">
                <div className="flex ">
                    <div className="mb-2">
                        <Avatar
                            source={comment.userId.profile_picture}
                            profilePicture={comment.userId.profile_picture}
                            size={40}
                            type="user"
                        />
                    </div>
                </div>

                {/* Content Section */}
                <div className="w-full flex flex-col max-[534px]">
                    {/* Header */}
                    <div className="flex flex-row items-center justify-between">
                        <div className="flex flex-row">
                            <div className="justify-center">
                                <h3 className={`text-[#5A5E5C] text-[1rem] ${poppinsSemibold.className}`}>
                                    {`${comment.userId.first_name} ${comment.userId.last_name}`.slice(0, 35)}
                                    {(comment.userId.first_name?.length + comment.userId.last_name?.length > 35) ? '...' : ''}
                                </h3>
                            </div>
                        </div>

                        {/* Menu */}
                        <div className="flex items-center justify-end cursor-pointer">
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <button className="cursor-pointer">
                                        <MoreVertical className="h-4 w-4" />
                                    </button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    {myId === comment?.userId?._id && (
                                        <DropdownMenuItem onClick={handleEditTopic}>
                                            <Edit className="mr-2 h-4 w-4 " />
                                            Edit post
                                        </DropdownMenuItem>
                                    )}

                                    {myId !== comment?.userId?._id && (
                                        <DropdownMenuItem>
                                            <UserPlus className="mr-2 h-4 w-4" />
                                            Follow
                                        </DropdownMenuItem>
                                    )}



                                    {myId === comment?.userId?._id && (
                                        <DropdownMenuItem onClick={handleDeleteTopic} className="text-red-600">
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Delete
                                        </DropdownMenuItem>
                                    )}

                                    {myId !== comment?.userId?._id && (
                                        <DropdownMenuItem className="text-red-600">
                                            <Flag className="mr-2 h-4 w-4" />
                                            Report
                                        </DropdownMenuItem>
                                    )}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>

                    {/* Position at Company */}
                    <div className="w-full">
                        <p className={`text-[#9D9D9D] text-[14px] ${poppinsMedium.className}`}>
                            {comment.userId.company_position && comment.userId.company
                                ? `${comment.userId.company_position?.replace(/(\r\n|\n|\r)/gm, "") || "position"} at ${comment.userId.company}`
                                : "Position at Company"}
                        </p>
                    </div>

                    {/* User Info */}
                    <div className="w-full">
                        <p className={`text-[#696969] text-[14px] ${poppinsMedium.className}`}>
                            {comment.userId.subscriptionType
                                ? `${comment.userId.subscriptionType.charAt(0).toUpperCase() + comment.userId.subscriptionType.slice(1)} Member`
                                : 'Free Member'}
                        </p>
                    </div>

                    {/* Time */}
                    <div className="flex items-start">
                        <p className={`text-[#696969] text-[14px] mt-[4px] ${poppinsMedium.className}`}>
                            {timeAgo(comment.createdAt)}
                        </p>
                    </div>

                    {/* Comment Content */}
                    <div className="mt-3">
                        <p className={`text-[#636363] text-[14px] leading-[20px] ${poppinsMedium.className}`}>
                            {comment.comment}
                        </p>
                    </div>

                    {/* Comment Attachments */}
                    {comment.image && (
                        <div className="mt-2 max-w-[68%]">
                            <div
                                className="relative w-full h-48 cursor-pointer hover:opacity-90 transition-opacity"
                                onClick={() => {
                                    if (onImageClick && comment.image) {
                                        onImageClick(comment.image);
                                    }
                                }}
                            >
                                <Image
                                    src={comment.image}
                                    alt="Comment attachment"
                                    fill
                                    className="object-cover rounded-lg border border-white"
                                />
                            </div>
                        </div>
                    )}

                    {/* Actions */}
                    <div className="flex  mt-[1rem] w-full">
                        <div className="flex w-full items-center justify-between ">
                            {/* Reply Button */}
                            <button
                                onClick={handleReply}
                                className="flex items-center cursor-pointer gap-[8px] hover:bg-gray-100  rounded"
                            >
                                <ReplySVG />

                                <span className={`text-[#5A5E5C] text-[14px] ${poppinsMedium.className}`}>
                                    Reply
                                </span>
                            </button>

                            {/* Sub Comments Button */}
                            <button
                                onClick={() => setShowSubComments(!showSubComments)}
                                className="flex items-center gap-[8px] hover:bg-gray-100 cursor-pointer rounded"
                            >
                                <CommentIcon
                                    width={24}
                                    height={24}
                                    className="comment-icon"
                                    alt="comment svg"
                                />
                                <span className={`text-[#5A5E5C] text-[14px] ${poppinsMedium.className}`}>
                                    {comment.subCommentsCount || 0} Comment{(comment.subCommentsCount || 0) !== 1 ? "s" : ""}
                                </span>
                            </button>

                            {/* Like Button */}
                            <button
                                onClick={handleLike}
                                className="flex items-center gap-[8px] cursor-pointer hover:bg-gray-100  rounded"
                            >
                                <ThumbSVG color={isLiked ? "#FE3233" : "#5A5E5C"} />
                                <span className={`text-[14px] ${poppinsMedium.className} ${isLiked ? "text-[#FE3233]" : "text-[#5A5E5C]"}`}>
                                    {numLikes || 0} Like{(numLikes || 0) !== 1 ? "s" : ""}
                                </span>
                            </button>
                        </div>
                    </div>

                    
                </div>
                </div>
                   <div className="w-full">
                {/* Sub Comments Section */}
                    {showSubComments && (
                        <div className="">
                            {loadingSubComments ? (
                                <div className="flex justify-center py-4">
                                    <Loading size="sm" />
                                </div>
                            ) : subComments && subComments.length > 0 ? (
                                <div className="">
                                    {subComments.map((subComment: Comment) => (
                                        <div key={subComment._id} className="flex gap-3 bg-gray-50 p-3 rounded-lg">
                                            <div className="w-12 h-12 flex-shrink-0">
                                                <Avatar
                                                    source={subComment.userId.profile_picture}
                                                    profilePicture={subComment.userId.profile_picture}
                                                    size={32}
                                                    type="user"
                                                />
                                            </div>
                                            <div className="flex-1">
                                                <div className="flex flex-col mb-2">
                                                    <h4 className={`${poppinsSemibold.className} text-[#5A5E5C] text-[14px]`}>
                                                        {subComment.userId.first_name} {subComment.userId.last_name}
                                                    </h4>
                                                    <p className={`${poppinsMedium.className} text-[#696969] text-[12px]`}>
                                                        {subComment.userId.company_position && subComment.userId.company
                                                            ? `${subComment.userId.company_position?.replace(/(\r\n|\n|\r)/gm, "") || "position"} at ${subComment.userId.company}`
                                                            : "Position at Company"}
                                                    </p>
                                                    <p className={`${poppinsMedium.className} text-[#696969] text-[12px]`}>
                                                        {subComment.userId.subscriptionType
                                                            ? `${subComment.userId.subscriptionType.charAt(0).toUpperCase() + subComment.userId.subscriptionType.slice(1)} Member`
                                                            : 'Free Member'}
                                                    </p>
                                                    <p className={`${poppinsMedium.className} text-[#696969] text-[12px] mt-1`}>
                                                        {timeAgo(subComment.createdAt)}
                                                    </p>
                                                </div>
                                                <p className={`${poppinsMedium.className} text-[#636363] text-[12px] leading-[20px]`}>
                                                    {subComment.comment}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className={`text-gray-500 text-sm ${poppinsRegular.className}`}>
                                    No replies yet
                                </p>
                            )}
                        </div>
                    )}
            </div>
            </div>

        </div>
    );
};

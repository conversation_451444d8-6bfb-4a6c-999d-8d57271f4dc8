# Navigation SVG Icons

This folder contains React components for all the navigation icons used in the sidebar.

## Available Icons

- `HomeIcon` - Home/Dashboard icon
- `PrivateChatsIcon` - Private chats icon
- `GroupChatsIcon` - Group chats icon
- `ProfileIcon` - User profile icon
- `SettingsIcon` - Settings/gear icon
- `MembershipIcon` - Trivia/membership icon
- `HelpdeskIcon` - Help/support icon
- `LogoutIcon` - Logout icon (red color)

## Usage

```tsx
import { HomeIcon, ProfileIcon } from '@/components/svgReactComponents/NavigationIcons';

// Basic usage
<HomeIcon />

// With custom props
<HomeIcon 
  className="my-custom-class" 
  width={24} 
  height={24} 
  fill="#000000" 
/>
```

## Props

All icons accept these props:

- `className?: string` - CSS classes
- `width?: number` - Icon width (default varies by icon)
- `height?: number` - Icon height (default varies by icon)  
- `fill?: string` - Fill color (default: "#797C7B")

## Adding New Icons

1. Add your SVG file to `src/assets/svg/`
2. Create a new React component in `NavigationIcons.tsx`
3. Export it from the file
4. Import and use in your navigation

Example:
```tsx
export const NewIcon: React.FC<IconProps> = ({ 
  className = "", 
  width = 20, 
  height = 20, 
  fill = "#797C7B" 
}) => (
  <svg 
    width={width} 
    height={height} 
    viewBox="0 0 20 20" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    {/* Your SVG paths here */}
  </svg>
);
```

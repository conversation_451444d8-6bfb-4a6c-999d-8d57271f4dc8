import React from 'react';

interface IconProps {
  className?: string;
  width?: number;
  height?: number;
  fill?: string;
}

export const HomeIcon: React.FC<IconProps> = ({ 
  className = "", 
  width = 20, 
  height = 18, 
  fill = "#797C7B" 
}) => (
  <svg 
    width={width} 
    height={height} 
    viewBox="0 0 20 18" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <g style={{mixBlendMode: "luminosity"}}>
      <path d="M7.64602 7.35254H7.28516V7.71325C7.28516 8.61494 8.01834 9.34844 8.91988 9.34844H9.28075V8.98758C9.28075 8.08604 8.54725 7.35254 7.64602 7.35254Z" fill={fill}/>
      <path d="M3.55279 7.7015C3.05881 7.45452 2.85858 6.85384 3.10557 6.35986C3.35256 5.86589 3.95324 5.66566 4.44721 5.91265L7.44721 7.41265C7.94119 7.65964 8.14142 8.26031 7.89443 8.75429C7.64744 9.24827 7.04676 9.44849 6.55279 9.2015L3.55279 7.7015Z" fill={fill}/>
      <path d="M3.55279 10.7015C3.05881 10.4545 2.85858 9.85384 3.10557 9.35986C3.35256 8.86589 3.95324 8.66566 4.44721 8.91265L7.44721 10.4127C7.94119 10.6596 8.14142 11.2603 7.89443 11.7543C7.64744 12.2483 7.04676 12.4485 6.55279 12.2015L3.55279 10.7015Z" fill={fill}/>
      <path d="M16.8944 6.35986C17.1414 6.85384 16.9412 7.45452 16.4472 7.7015L13.4472 9.2015C12.9532 9.44849 12.3526 9.24827 12.1056 8.75429C11.8586 8.26031 12.0588 7.65964 12.5528 7.41265L15.5528 5.91265C16.0468 5.66566 16.6474 5.86589 16.8944 6.35986Z" fill={fill}/>
      <path d="M16.8944 9.35986C17.1414 9.85384 16.9412 10.4545 16.4472 10.7015L13.4472 12.2015C12.9532 12.4485 12.3526 12.2483 12.1056 11.7543C11.8586 11.2603 12.0588 10.6596 12.5528 10.4127L15.5528 8.91265C16.0468 8.66566 16.6474 8.86589 16.8944 9.35986Z" fill={fill}/>
      <path fillRule="evenodd" clipRule="evenodd" d="M10 16.8071C10.3502 17.7438 10.3502 17.7437 10.3503 17.7437L10.3594 17.7403L10.3835 17.7312L10.4742 17.6968C10.5525 17.6668 10.6658 17.6232 10.8071 17.5678C11.0897 17.4572 11.4855 17.2991 11.9405 17.1087C12.844 16.7308 14.0062 16.2153 14.9787 15.6851C16.0587 15.0963 17.6546 14.4238 18.6095 14.0376C19.4414 13.7011 20 12.8935 20 11.9824V2.37628C20 0.935053 18.6204 -0.14557 17.2006 0.290756C16.2643 0.578503 15.0382 0.982082 14.1008 1.39021C13.1215 1.81657 11.9538 2.46704 11.0529 2.99543C10.6398 3.23769 10.2753 3.4588 10 3.62821C9.72474 3.4588 9.36018 3.23769 8.94712 2.99543C8.04622 2.46704 6.8785 1.81657 5.89918 1.39021C4.96177 0.982082 3.73568 0.578503 2.79939 0.290756C1.37964 -0.14557 0 0.935053 0 2.37628V11.9824C0 12.8935 0.558605 13.7011 1.39051 14.0376C2.34538 14.4238 3.94127 15.0963 5.02134 15.6851C5.99383 16.2153 7.15605 16.7308 8.0595 17.1087C8.51448 17.2991 8.91034 17.4572 9.19288 17.5678C9.33424 17.6232 9.44748 17.6668 9.52584 17.6968L9.61646 17.7312L9.64061 17.7403L9.64896 17.7434C9.64903 17.7435 9.64983 17.7438 10 16.8071ZM2.21185 2.20251C2.12004 2.17429 2 2.23617 2 2.37628V11.9824C2 12.0649 2.05132 12.1474 2.14048 12.1835C3.0946 12.5694 4.78695 13.2794 5.97866 13.9291C6.86058 14.4099 7.94836 14.8943 8.83131 15.2637C8.88855 15.2876 8.94482 15.311 9 15.3339V5.36122C8.72937 5.19426 8.35844 4.96878 7.9353 4.7206C7.04701 4.19961 5.96473 3.60008 5.10082 3.22395C4.2725 2.86332 3.13671 2.48674 2.21185 2.20251ZM11 5.36122V15.3339C11.0552 15.311 11.1115 15.2876 11.1687 15.2637C12.0516 14.8943 13.1394 14.4099 14.0213 13.9291C15.2131 13.2794 16.9054 12.5694 17.8595 12.1835C17.9487 12.1474 18 12.0649 18 11.9824V2.37628C18 2.23617 17.88 2.17429 17.7881 2.20251C16.8633 2.48674 15.7275 2.86332 14.8992 3.22395C14.0353 3.60008 12.953 4.19961 12.0647 4.7206C11.6416 4.96878 11.2706 5.19426 11 5.36122Z" fill={fill}/>
      <path d="M10 16.8071L10.3503 17.7437C10.1245 17.8281 9.87477 17.8279 9.64896 17.7434L10 16.8071Z" fill={fill}/>
      <path d="M11.9956 10.167V9.80615H11.6347C10.7335 9.80615 10 10.5396 10 11.4413V11.8021H10.3609C11.2624 11.8021 11.9956 11.0686 11.9956 10.167Z" fill={fill}/>
      <path d="M5.375 13.7227V14.0835C5.375 14.9851 6.10819 15.7182 7.00973 15.7182H7.37059V15.3574C7.37059 14.4562 6.63709 13.7227 5.73586 13.7227H5.375Z" fill={fill}/>
    </g>
  </svg>
);

export const PrivateChatsIcon: React.FC<IconProps> = ({ 
  className = "", 
  width = 20, 
  height = 20, 
  fill = "#797C7B" 
}) => (
  <svg 
    width={width} 
    height={height} 
    viewBox="0 0 20 20" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path fillRule="evenodd" clipRule="evenodd" d="M19.954 1.36044C19.9084 0.858365 19.4787 0.479294 18.9748 0.496668C13.098 0.699315 9.00969 3.7971 6.32259 7.50589C3.65219 11.1916 2.32342 15.5271 1.91031 18.4189C1.84008 18.9105 2.16019 19.3733 2.64497 19.481C3.12975 19.5888 3.61574 19.3051 3.76034 18.83C3.84039 18.567 3.92172 18.2924 4.00541 18.0098C4.52722 16.2479 5.14101 14.1754 6.11038 12.6629C6.66234 11.8017 7.26135 11.2381 7.91394 10.9913C8.53109 10.7579 9.35272 10.7493 10.5043 11.3251C11.6614 11.9037 13.091 11.499 13.8755 10.4965C14.4685 9.73882 14.6484 8.74489 14.3393 7.65634C15.47 7.63106 16.6745 7.46567 17.6826 6.91288C18.4692 6.48148 19.1278 5.82157 19.539 4.86643C19.9421 3.9299 20.0821 2.76952 19.954 1.36044ZM11.3544 9.62503C9.95465 8.92517 8.66618 8.77156 7.51087 9.12177C7.62497 8.9535 7.74194 8.78653 7.8618 8.62109C10.1473 5.46659 13.4503 2.91011 18.0982 2.45802C18.0855 3.17624 17.966 3.71318 17.7931 4.11489C17.5608 4.65461 17.2095 5.00448 16.7686 5.24627C15.8094 5.77227 14.4118 5.80511 12.8801 5.72449C12.5211 5.7056 12.1822 5.89087 12.0043 6.20324C11.8264 6.51562 11.8399 6.90163 12.0393 7.20073C12.8342 8.39308 12.5888 9.05661 12.3787 9.32506C12.094 9.68874 11.6228 9.75926 11.3544 9.62503Z" fill={fill}/>
    <path d="M3.80151 1.44648C3.80151 0.921602 3.37601 0.496103 2.85113 0.496103C2.32625 0.496103 1.90076 0.921602 1.90076 1.44648V2.39686H0.950378C0.425499 2.39686 0 2.82236 0 3.34724C0 3.87212 0.425499 4.29761 0.950378 4.29761H1.90076V5.24799C1.90076 5.77287 2.32625 6.19837 2.85113 6.19837C3.37601 6.19837 3.80151 5.77287 3.80151 5.24799V4.29761H4.75189C5.27677 4.29761 5.70227 3.87212 5.70227 3.34724C5.70227 2.82236 5.27677 2.39686 4.75189 2.39686H3.80151V1.44648Z" fill={fill}/>
  </svg>
);

export const GroupChatsIcon: React.FC<IconProps> = ({
  className = "",
  width = 21,
  height = 18,
  fill = "#797C7B"
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 21 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path fillRule="evenodd" clipRule="evenodd" d="M10.4979 0.888184C8.38964 0.888184 6.68056 2.59727 6.68056 4.70553C6.68056 6.81378 8.38964 8.52287 10.4979 8.52287C12.6062 8.52287 14.3152 6.81378 14.3152 4.70553C14.3152 2.59727 12.6062 0.888184 10.4979 0.888184ZM8.58923 4.70553C8.58923 3.6514 9.44377 2.79685 10.4979 2.79685C11.552 2.79685 12.4066 3.6514 12.4066 4.70553C12.4066 5.75965 11.552 6.6142 10.4979 6.6142C9.44377 6.6142 8.58923 5.75965 8.58923 4.70553Z" fill={fill}/>
    <path fillRule="evenodd" clipRule="evenodd" d="M10.4979 17.1119C9.41293 17.1119 7.84854 16.8555 6.66132 16.3099C6.07895 16.0422 5.45407 15.6453 5.08548 15.0514C4.8909 14.7379 4.76796 14.3683 4.77198 13.9569C4.77597 13.5495 4.90373 13.1631 5.11296 12.8091C6.06965 11.19 8.05437 9.4772 10.4979 9.4772C12.9414 9.4772 14.9261 11.19 15.8828 12.8091C16.0921 13.1631 16.2198 13.5495 16.2238 13.9569C16.2278 14.3683 16.1049 14.7379 15.9103 15.0514C15.5417 15.6453 14.9169 16.0422 14.3345 16.3099C13.1473 16.8555 11.5829 17.1119 10.4979 17.1119ZM6.7562 13.78C6.68311 13.9037 6.68066 13.9651 6.68056 13.9756C6.68048 13.9825 6.68024 14.0015 6.7072 14.0449C6.78297 14.167 7.00303 14.3663 7.45832 14.5756C8.34645 14.9837 9.62834 15.2032 10.4979 15.2032C11.3675 15.2032 12.6493 14.9837 13.5375 14.5756C13.9928 14.3663 14.2128 14.167 14.2886 14.0449C14.3156 14.0015 14.3153 13.983 14.3152 13.9761C14.3151 13.9656 14.3127 13.9037 14.2396 13.78C13.4979 12.5248 12.0473 11.3859 10.4979 11.3859C8.94852 11.3859 7.49791 12.5248 6.7562 13.78Z" fill={fill}/>
    <path fillRule="evenodd" clipRule="evenodd" d="M1.43171 7.56853C1.43171 5.98734 2.71353 4.70553 4.29472 4.70553C5.87591 4.70553 7.15773 5.98734 7.15773 7.56853C7.15773 9.14973 5.87591 10.4315 4.29472 10.4315C2.71353 10.4315 1.43171 9.14973 1.43171 7.56853ZM4.29472 6.6142C3.76766 6.6142 3.34038 7.04147 3.34038 7.56853C3.34038 8.0956 3.76766 8.52287 4.29472 8.52287C4.82178 8.52287 5.24905 8.0956 5.24905 7.56853C5.24905 7.04147 4.82178 6.6142 4.29472 6.6142Z" fill={fill}/>
    <path d="M1.80938 14.6732C1.57505 15.1453 1.00237 15.338 0.530256 15.1037C0.0581467 14.8694 -0.134612 14.2967 0.099717 13.8246C0.450325 13.1182 0.981007 12.409 1.64859 11.8663C2.31642 11.3234 3.1722 10.9087 4.14887 10.9087C4.67594 10.9087 5.10321 11.336 5.10321 11.863C5.10321 12.3901 4.67594 12.8174 4.14887 12.8174C3.72803 12.8174 3.28387 12.9967 2.85253 13.3474C2.42093 13.6982 2.05321 14.1819 1.80938 14.6732Z" fill={fill}/>
    <path fillRule="evenodd" clipRule="evenodd" d="M14.134 7.56853C14.134 5.98734 15.4158 4.70553 16.997 4.70553C18.5782 4.70553 19.86 5.98734 19.86 7.56853C19.86 9.14973 18.5782 10.4315 16.997 10.4315C15.4158 10.4315 14.134 9.14973 14.134 7.56853ZM16.997 6.6142C16.4699 6.6142 16.0426 7.04147 16.0426 7.56853C16.0426 8.0956 16.4699 8.52287 16.997 8.52287C17.524 8.52287 17.9513 8.0956 17.9513 7.56853C17.9513 7.04147 17.524 6.6142 16.997 6.6142Z" fill={fill}/>
    <path d="M20.9003 13.8246C21.1346 14.2967 20.9419 14.8694 20.4697 15.1037C19.9976 15.338 19.425 15.1453 19.1906 14.6732C18.9468 14.1819 18.5791 13.6982 18.1475 13.3474C17.7161 12.9967 17.272 12.8174 16.8511 12.8174C16.3241 12.8174 15.8968 12.3901 15.8968 11.863C15.8968 11.336 16.3241 10.9087 16.8511 10.9087C17.8278 10.9087 18.6836 11.3234 19.3514 11.8663C20.019 12.409 20.5497 13.1182 20.9003 13.8246Z" fill={fill}/>
  </svg>
);

export const ProfileIcon: React.FC<IconProps> = ({ 
  className = "", 
  width = 16, 
  height = 20, 
  fill = "#797C7B" 
}) => (
  <svg 
    width={width} 
    height={height} 
    viewBox="0 0 16 20" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path fillRule="evenodd" clipRule="evenodd" d="M7.61905 0C4.98912 0 2.85714 2.13198 2.85714 4.7619C2.85714 7.39183 4.98912 9.52381 7.61905 9.52381C10.249 9.52381 12.381 7.39183 12.381 4.7619C12.381 2.13198 10.249 0 7.61905 0ZM4.76191 4.7619C4.76191 3.18395 6.04109 1.90476 7.61905 1.90476C9.197 1.90476 10.4762 3.18395 10.4762 4.7619C10.4762 6.33986 9.197 7.61905 7.61905 7.61905C6.04109 7.61905 4.76191 6.33986 4.76191 4.7619Z" fill={fill}/>
    <path fillRule="evenodd" clipRule="evenodd" d="M7.61904 20C6.1519 20 4.0284 19.6669 2.43374 18.9689C1.65089 18.6263 0.854716 18.1359 0.392146 17.4261C0.148873 17.0528 -0.00492064 16.6131 0.000120265 16.1221C0.00511502 15.6357 0.16518 15.1696 0.434205 14.736C1.7384 12.6339 4.40647 10.4762 7.61904 10.4762C10.8316 10.4762 13.4997 12.6339 14.8039 14.736C15.0729 15.1696 15.233 15.6357 15.238 16.1221C15.243 16.6131 15.0892 17.0528 14.8459 17.4261C14.3834 18.1359 13.5872 18.6263 12.8043 18.9689C11.2097 19.6669 9.08619 20 7.61904 20ZM2.05275 15.7402C1.92735 15.9423 1.9055 16.0721 1.90478 16.1417C1.90411 16.2067 1.92082 16.2831 1.98797 16.3862C2.14623 16.629 2.53054 16.9321 3.19745 17.224C4.50233 17.7951 6.35546 18.0952 7.61904 18.0952C8.88263 18.0952 10.7358 17.7951 12.0406 17.224C12.7076 16.9321 13.0919 16.629 13.2501 16.3862C13.3173 16.2831 13.334 16.2067 13.3333 16.1417C13.3326 16.0721 13.3107 15.9423 13.1853 15.7402C12.1166 14.0177 9.98508 12.381 7.61904 12.381C5.25301 12.381 3.12147 14.0177 2.05275 15.7402Z" fill={fill}/>
  </svg>
);

export const SettingsIcon: React.FC<IconProps> = ({
  className = "",
  width = 20,
  height = 22,
  fill = "#797C7B"
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 20 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path fillRule="evenodd" clipRule="evenodd" d="M10 5.35135C6.88046 5.35135 4.35157 7.88023 4.35157 10.9998C4.35157 14.1193 6.88046 16.6482 10 16.6482C13.1195 16.6482 15.6484 14.1193 15.6484 10.9998C15.6484 7.88023 13.1195 5.35135 10 5.35135ZM6.23438 10.9998C6.23438 8.92008 7.92031 7.23416 10 7.23416C12.0797 7.23416 13.7656 8.92008 13.7656 10.9998C13.7656 13.0795 12.0797 14.7654 10 14.7654C7.92031 14.7654 6.23438 13.0795 6.23438 10.9998Z" fill={fill}/>
    <path fillRule="evenodd" clipRule="evenodd" d="M12.3552 1.61294C11.042 0.342562 8.95797 0.342562 7.64479 1.61294C7.31125 1.93561 6.84747 2.0863 6.38797 2.0213C4.57887 1.76542 2.89283 2.9904 2.57715 4.79002C2.49697 5.24712 2.21034 5.64163 1.80039 5.85914C0.186395 6.71548 -0.457616 8.69755 0.344787 10.339C0.548593 10.756 0.548593 11.2436 0.344787 11.6605C-0.457616 13.302 0.186395 15.2841 1.80039 16.1404C2.21034 16.3579 2.49697 16.7524 2.57715 17.2095C2.89283 19.0091 4.57887 20.2341 6.38797 19.9782C6.84747 19.9133 7.31125 20.0639 7.64479 20.3866C8.95797 21.657 11.042 21.657 12.3552 20.3866C12.6888 20.0639 13.1525 19.9133 13.612 19.9782C15.4211 20.2341 17.1072 19.0091 17.4229 17.2095C17.503 16.7524 17.7897 16.3579 18.1996 16.1404C19.8136 15.2841 20.4576 13.302 19.6552 11.6605C19.4514 11.2436 19.4514 10.756 19.6552 10.339C20.4576 8.69755 19.8136 6.71548 18.1996 5.85914C17.7897 5.64163 17.503 5.24712 17.4229 4.79002C17.1072 2.9904 15.4211 1.76542 13.612 2.0213C13.1525 2.0863 12.6888 1.93561 12.3552 1.61294ZM8.95389 2.96616C9.53717 2.4019 10.4628 2.4019 11.0461 2.96616C11.797 3.69262 12.8412 4.03188 13.8757 3.88556C14.6793 3.7719 15.4281 4.316 15.5684 5.11533C15.7489 6.14443 16.3942 7.03265 17.3172 7.52234C18.034 7.9027 18.3201 8.78307 17.9637 9.51216C17.5048 10.4508 17.5048 11.5487 17.9637 12.4874C18.3201 13.2165 18.034 14.0968 17.3172 14.4772C16.3942 14.9669 15.7489 15.8551 15.5684 16.8842C15.4281 17.6836 14.6793 18.2276 13.8757 18.114C12.8412 17.9677 11.797 18.3069 11.0461 19.0334C10.4628 19.5976 9.53717 19.5976 8.95389 19.0334C8.20296 18.3069 7.1588 17.9677 6.12428 18.114C5.32074 18.2276 4.57186 17.6836 4.43164 16.8842C4.25112 15.8551 3.6058 14.9669 2.68284 14.4772C1.96596 14.0968 1.67991 13.2165 2.03631 12.4874C2.49516 11.5487 2.49516 10.4508 2.03631 9.51216C1.67991 8.78307 1.96596 7.9027 2.68284 7.52234C3.6058 7.03265 4.25112 6.14443 4.43164 5.11533C4.57186 4.316 5.32074 3.7719 6.12428 3.88556C7.1588 4.03188 8.20296 3.69262 8.95389 2.96616Z" fill={fill}/>
  </svg>
);

export const MembershipIcon: React.FC<IconProps> = ({
  className = "",
  width = 22,
  height = 20,
  fill = "#797C7B"
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 22 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path fillRule="evenodd" clipRule="evenodd" d="M11.6393 0.22018C11.15 -0.0733926 10.5387 -0.073394 10.0494 0.22018L0.750126 5.79976C-0.250042 6.39986 -0.250042 7.84939 0.750126 8.44949L2.6041 9.56187V16.1537C2.6041 16.6047 2.80288 17.0648 3.2054 17.3661C3.98946 17.9532 6.98298 20 10.8443 20C14.7057 20 17.6992 17.9532 18.4833 17.3661C18.8858 17.0648 19.0846 16.6047 19.0846 16.1537V9.56187L20.9386 8.44949C21.9387 7.84938 21.9387 6.39986 20.9386 5.79976L11.6393 0.22018ZM2.54605 7.12462L10.8443 2.14565L19.1426 7.12462L15.6798 9.20233L11.958 6.54396C11.4951 6.21331 10.8518 6.32053 10.5212 6.78344C10.1905 7.24634 10.2978 7.88965 10.7607 8.2203L13.7535 10.3581L10.8443 12.1036L2.54605 7.12462ZM13.9344 12.652L11.6393 14.0291C11.15 14.3226 10.5387 14.3226 10.0494 14.0291L4.66416 10.7979V15.8789C5.57332 16.5145 7.94411 17.9399 10.8443 17.9399C13.7446 17.9399 16.1154 16.5145 17.0245 15.8789V10.7979L15.9945 11.4159V14.0773C15.9945 14.6462 15.5333 15.1074 14.9645 15.1074C14.3956 15.1074 13.9344 14.6462 13.9344 14.0773V12.652Z" fill={fill}/>
  </svg>
);

export const HelpdeskIcon: React.FC<IconProps> = ({
  className = "",
  width = 20,
  height = 20,
  fill = "#797C7B"
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M4 13C3.44772 13 3 12.5523 3 12C3 11.4477 3.44772 11 4 11C4.55228 11 5 11.4477 5 12C5 12.5523 4.55228 13 4 13Z" fill={fill}/>
    <path d="M7 12C7 12.5523 7.44772 13 8 13C8.55229 13 9 12.5523 9 12C9 11.4477 8.55229 11 8 11C7.44772 11 7 11.4477 7 12Z" fill={fill}/>
    <path d="M12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12C13 12.5523 12.5523 13 12 13Z" fill={fill}/>
    <path fillRule="evenodd" clipRule="evenodd" d="M18.478 11.448L15.9964 11.7582C15.9988 11.8385 16 11.9191 16 12C16 16.4183 12.4183 20 8 20C7.33907 20 6.69556 19.9196 6.07926 19.7677L1.78037 19.2303C0.723065 19.0982 -0.011726 18.1126 0.163446 17.0616L0.427141 15.4794L0.23231 13.9207C0.0803631 13.3044 0 12.6609 0 12C0 7.58172 3.58172 4 8 4C8.11364 4 8.22673 4.00237 8.33922 4.00706C9.16093 1.67344 11.3839 0 14 0C17.3137 0 20 2.68629 20 6C20 6.48944 19.9412 6.96646 19.8298 7.42385L19.6979 8.47939L19.8794 9.56843C20.0312 10.4793 19.3944 11.3335 18.478 11.448ZM14 2C16.2091 2 18 3.79086 18 6C18 6.34404 17.9568 6.67655 17.876 6.99301L17.8605 7.05391L17.7065 8.28556C17.687 8.44144 17.6903 8.59931 17.7161 8.75427L17.8424 9.51192L15.6883 9.78118C14.9424 7.19202 12.924 5.142 10.3539 4.35192C10.982 2.96457 12.3793 2 14 2ZM8 6C4.68629 6 2 8.68629 2 12C2 12.5153 2.06473 13.014 2.18599 13.4891L2.20154 13.55L2.41396 15.2494C2.43644 15.4293 2.43272 15.6114 2.40293 15.7902L2.15764 17.2619L6.44996 17.7985L6.51087 17.814C6.98597 17.9353 7.48471 18 8 18C11.3137 18 14 15.3137 14 12C14 8.68629 11.3137 6 8 6Z" fill={fill}/>
  </svg>
);

export const LogoutIcon: React.FC<IconProps> = ({
  className = "",
  width = 21,
  height = 20,
  fill = "#DE6144"
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path opacity="0.4" d="M13.244 5.368V4.435C13.244 2.4 11.594 0.75 9.559 0.75H4.684C2.65 0.75 1 2.4 1 4.435V15.565C1 17.6 2.65 19.25 4.684 19.25H9.569C11.598 19.25 13.244 17.605 13.244 15.576V14.633" stroke={fill} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M20.0371 10H7.99609" stroke={fill} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M17.1094 7.08496L20.0374 9.99996L17.1094 12.916" stroke={fill} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

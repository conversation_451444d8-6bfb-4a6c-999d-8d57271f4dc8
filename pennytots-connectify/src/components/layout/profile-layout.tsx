'use client';

import React, { useState } from 'react';
import Sidebar from '@/components/sidebar';
import { Header } from '@/components/ui/header';
import TriviaScreen from '@/components/screens/trivia';
import HomeScreen from '@/components/screens/home';
import ChatScreen from '@/components/screens/private-chats';
import GroupScreen from '@/components/screens/group-chats';
import ProfileScreen from '@/components/screens/profile';
import SettingScreen from '@/components/screens/settings';
import HelpDeskScreen from '@/components/screens/helpdesk';
import { SponsorAds } from '@/components/ui/sponsor-ads';
import {
  HomeIcon,
  PrivateChatsIcon,
  GroupChatsIcon,
  ProfileIcon,
  SettingsIcon,
  MembershipIcon,
  HelpdeskIcon
} from '@/components/svgReactComponents/NavigationIcons';

const sidebarItems = [
  {
    id: "home",
    label: "Home",
    icon: HomeIcon,
    component: HomeScreen,
  },
  {
    id: "chats",
    label: "Chats",
    icon: PrivateChatsIcon,
    component: ChatScreen,
  },
  {
    id: "groups",
    label: "Groups",
    icon: GroupChatsIcon,
    component: GroupScreen,
  },
  {
    id: "profile",
    label: "Profile",
    icon: ProfileIcon,
    component: ProfileScreen,
  },
  {
    id: "settings",
    label: "Settings",
    icon: SettingsIcon,
    component: SettingScreen,
  },
  {
    id: "helpdesk",
    label: "Help Desk",
    icon: HelpdeskIcon,
    component: HelpDeskScreen,
  },
];

interface ProfileLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const ProfileLayout: React.FC<ProfileLayoutProps> = ({
  children,
  title = "Profile"
}) => {
  const [activeScreen, setActiveScreen] = useState("profile");

  const handleScreenChange = (screenId: string) => {
    setActiveScreen(screenId);
  };

  const handleLogout = () => {
    console.log("Logout clicked");
  };

  // Add this function to handle tab changes
  const handleTabChange = (tabId: string) => {
    // Implement your tab change logic here
    console.log("Tab changed to:", tabId);
  };

  // You may also need to define activeTab, for now set a default
  const [activeTab, setActiveTab] = useState<'posts' | 'convos'>('posts');

  return (
    <div className="h-screen bg-[#F8F9FA]">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white ">
<Header
  showTabs={true}
  activeTab={activeTab}
  onTabChange={(tabId: string) => {
    setActiveTab(tabId as 'posts' | 'convos');
    handleTabChange(tabId);
  }}
  tabLabels={{
    posts: 'Posts',
    convos: 'Convos',
    profile: 'Profile'
  }}
/>      </div>

      {/* Main Layout Container - Below Fixed Header */}
      <div className="flex w-full  h-full pt-[88px]">
        {/* Fixed Left Sidebar */}
        <div className="hidden lg:block w-full max-w-[235px] h-full bg-white">
          <Sidebar
            items={sidebarItems}
            activeScreen={activeScreen}
            onScreenChange={handleScreenChange}
            onLogout={handleLogout}
          />
        </div>

        {/* Main Content Area - Scrollable */}
        <div className="flex w-full justify-between bg-[#FFFFFF] max-w-[1060px] ">
          {/* Screen Content - Scrollable Container */}
          <div className="w-full max-w-[676px] border-x-[1px] border-[#D9D9D9] h-screen overflow-y-auto">
            {children}
          </div>

          {/* Right Sidebar - Trivia Section */}
          <div className="w-full h-full overflow-y-auto bg-white">
            <div className="flex flex-col items-center p-4">
              {/* Sponsor Ads - Display above trivia */}
              <SponsorAds />

              {/* Trivia Section */}
            
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileLayout;

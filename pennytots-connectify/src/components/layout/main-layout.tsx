"use client"

import React, { use<PERSON><PERSON><PERSON>, useState, useEffect } from "react"
import { useDispatch } from "react-redux"
import { setLoading } from "../../redux/main/reducer"
import HomeScreen from "@/components/screens/home"
import Sidebar from "../../components/sidebar"
import { SponsoredModal } from "../../components/ui/sponsored-modal"
import { SponsorAds } from "../../components/ui/sponsor-ads"
import { LoadingDebug } from "../../components/debug/LoadingDebug"
import QuizPage from "../../app/quiz/page"
import ChatScreen from "@/components/screens/private-chats"
import GroupScreen from "@/components/screens/group-chats"
import ProfileScreen from "@/components/screens/profile"
import HelpDeskScreen from "@/components/screens/helpdesk"
import { Header } from "@/components/ui/header"
import { SearchFilter } from "@/components/ui/search-filter"
import SettingScreen from '@/components/settings/index'

// Import custom SVG icons
import {
  HomeIcon,
  PrivateChatsIcon,
  GroupChatsIcon,
  ProfileIcon,
  SettingsIcon,
  MembershipIcon,
  HelpdeskIcon
} from "@/components/svgReactComponents/NavigationIcons"
import { logout } from "@/redux/user/hooks"

const sidebarItems = [
    {
        id: "home",
        label: "Home",
        icon: HomeIcon,
        component: HomeScreen,
    },
    {
        id: "chats",
        label: "Chats",
        icon: PrivateChatsIcon,
        component: ChatScreen,
    },
    {
        id: "groups",
        label: "Groups",
        icon: GroupChatsIcon,
        component: GroupScreen,
    },
    {
        id: "profile",
        label: "Profile",
        icon: ProfileIcon,
        component: ProfileScreen,
    },
    {
        id: "settings",
        label: "Settings",
        icon: SettingsIcon,
        component: SettingScreen,
    },
  
    {
        id: "helpdesk",
        label: "Helpdesk",
        icon: HelpdeskIcon,
        component: HelpDeskScreen,
    },
]

interface MainLayoutProps {
  children?: React.ReactNode;
  activeScreen?: string;
  onScreenChange?: (screenId: string) => void;
  currentScreen?: string; // Add this to determine which screen we're on
  disableContentStyling?: boolean; // Add this to disable default content styling
}

export default function MainLayout({
  children,
  activeScreen = "home",
  onScreenChange,
  currentScreen,
  disableContentStyling = false
}: MainLayoutProps) {
  const [activeTab, setActiveTab] = useState<'posts' | 'convos' | 'profile'>('posts')
  const [search, setSearch] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const dispatch = useDispatch()

  // Reset loading state on component mount to prevent stuck loading
  useEffect(() => {
    dispatch(setLoading(false));
  }, [dispatch]);

  const currentScreenItem = sidebarItems.find((item) => item.id === activeScreen)
  const CurrentScreenComponent = currentScreenItem?.component || HomeScreen

  // Check if current screen is home to show tabs
  const isHomeScreen = activeScreen === "home"

  const handleScreenChange = (screenId: string) => {
    onScreenChange?.(screenId)
  }

  const handleLogout = () => {
logout()
console.log('clicked')
  }

  const handleTabChange = (tab: 'posts' | 'convos' | 'profile') => {
    setActiveTab(tab);
  }

  // Function to get dynamic tab labels based on current screen
  const getTabLabels = () => {
    const screenToUse = currentScreen || activeScreen;

    switch (screenToUse) {
      case 'profile':
        return {
          posts: 'My Posts',
          convos: 'My Conversations',
          profile: 'Profile'
        };
      case 'edit-profile':
        return {
          posts: 'Edit Profile',
          convos: 'Account Settings',
          profile: 'Profile'
        };
      case 'chats':
        return {
          posts: 'Private Chats',
          convos: 'Chat History',
          profile: 'Profile'
        };
      case 'groups':
        return {
          posts: 'Group Posts',
          convos: 'Group Chats',
          profile: 'Profile'
        };
      case 'settings':
        return {
          posts: 'Account Settings',
          convos: 'Privacy Settings',
          profile: 'Profile'
        };
      case 'helpdesk':
        return {
          posts: 'Helpdesk',
          convos: 'Support Chat',
          profile: 'Profile'
        };
      case 'trivia':
        return {
          posts: 'Trivia Questions',
          convos: 'Leaderboard',
          profile: 'Profile'
        };
      case 'home':
      default:
        return {
          posts: 'Posts',
          convos: 'Convos',
          profile: 'Profile'
        };
    }
  };

  const filterOptions = [
   { value: 'user', label: 'User' },
    { value: 'admin', label: 'Admin' },
    
    { value: 'moderator', label: 'Moderator' },
  ];

  // Optional: for active filter display
  const activeFilters = useMemo(() => {
    const filters = [];
    if (roleFilter !== 'all') {
      const option = filterOptions.find(opt => opt.value === roleFilter);
      if (option) {
        filters.push({ key: 'role', value: roleFilter, label: option.label });
      }
    }
    return filters;
  }, [roleFilter]);

  const handleRemoveFilter = (key: string) => {
    if (key === 'role') {
      setRoleFilter('all');
    }
  };

  const handleClearAll = () => {
    setRoleFilter('all');
    setSearch('');
  };

  return (
    <div className="h-screen w-full flex flex-col overflow-hidden" >
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-white">
        <div className="w-full  "     >
         <Header
  showTabs={true}
  activeTab={activeTab}
  onTabChange={handleTabChange}
  tabLabels={getTabLabels()}
/>
        </div>
      </div>

      {/* Main Layout Container - Below Fixed Header */}
      <div className="flex w-full justify-center  pt-[88px]" >
        <div className="w-full max-w-[1384px] flex">

    
        {/* Fixed Left Sidebar */}
        <div className="hidden lg:block  h-full bg-white w-full max-w-[235px] mr-[3.04vw]" > 
          <Sidebar
            items={sidebarItems}
            activeScreen={activeScreen}
            onScreenChange={handleScreenChange}
            onLogout={handleLogout}
          />
        </div>

        {/* Main Content Area - Scrollable */}
        <div className="flex bg-[#FFFFFF] w-full overflow-hidden">
          {/* Screen Content - Scrollable Container */}
        <div className={`w-full ${currentScreen === 'helpdesk' ? 'h-auto' : 'h-screen border-x-[1px] border-[#D9D9D9]   w-[max-content]'} `}>

            {children ? children : (
              isHomeScreen ? (
                <HomeScreen activeTab={activeTab === 'profile' ? 'posts' : activeTab} />
              ) : (
                <CurrentScreenComponent />
              )
            )}
          </div>

          {/* Right Sidebar - Only show on home screen */}
          {isHomeScreen && (
              <div className="flex flex-col items-center w-full max-w-[354px] ml-[3.04vw]" >
            <div className="w-full h-full overflow-y-auto bg-white ">
                <div className=" flex gap-[4px]">
                  <SearchFilter
                    searchValue={search}
                    onSearchChange={setSearch}
                    filterValue={roleFilter}
                    onFilterChange={setRoleFilter}
                    filterOptions={filterOptions}
                    filterPlaceholder="Filter by role"
                    activeFilters={activeFilters}
                    onRemoveFilter={handleRemoveFilter}
                    onClearAllFilters={handleClearAll}
                  />
                </div>

                {/* Sponsor Ads - Display above trivia */}
                <SponsorAds />

                <QuizPage />
                {/* Sponsored Modal */}
                <SponsoredModal />

                {/* Debug Components */}
                <LoadingDebug />
              </div>
            </div>
          )}
        </div>
            </div>
      </div>
    </div>
  )
}

'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loading } from '@/components/ui/loading';
import { 
  Users,
  Lock,
  Globe,
  Crown,
  Shield,
  MoreHorizontal,
  Filter,
  ChevronDown,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import Link from 'next/link';

interface Group {
  _id: string;
  name: string;
  description?: string;
  image?: string;
  members_count?: number;
  isPrivate?: boolean;
  role?: 'admin' | 'moderator' | 'member';
  category?: string;
  createdAt?: string;
  lastActivity?: string;
}

interface GroupsTabProps {
  groups: Group[];
  loading?: boolean;
  isOwnProfile?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  onLeaveGroup?: (groupId: string) => void;
  onJoinGroup?: (groupId: string) => void;
  emptyMessage?: string;
  emptySubMessage?: string;
}

const GroupsTab: React.FC<GroupsTabProps> = ({
  groups,
  loading = false,
  isOwnProfile = false,
  onLoadMore,
  hasMore = false,
  onLeaveGroup,
  onJoinGroup,
  emptyMessage = "No groups joined yet",
  emptySubMessage
}) => {
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'name' | 'members'>('newest');
  const [filterBy, setFilterBy] = useState<'all' | 'public' | 'private' | 'admin' | 'member'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const getRoleIcon = (role?: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-3 h-3 text-yellow-500" />;
      case 'moderator':
        return <Shield className="w-3 h-3 text-blue-500" />;
      default:
        return null;
    }
  };

  const getRoleBadge = (role?: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="outline" className="text-xs text-yellow-600 border-yellow-300">Admin</Badge>;
      case 'moderator':
        return <Badge variant="outline" className="text-xs text-blue-600 border-blue-300">Moderator</Badge>;
      default:
        return null;
    }
  };

  const formatMemberCount = (count?: number) => {
    if (!count) return '0 members';
    if (count < 1000) return `${count} members`;
    if (count < 1000000) return `${(count / 1000).toFixed(1)}K members`;
    return `${(count / 1000000).toFixed(1)}M members`;
  };

  const sortedAndFilteredGroups = groups
    .filter(group => {
      // Search filter
      if (searchTerm && !group.name.toLowerCase().includes(searchTerm.toLowerCase()) && 
          !group.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }
      
      // Type filter
      switch (filterBy) {
        case 'public':
          return !group.isPrivate;
        case 'private':
          return group.isPrivate;
        case 'admin':
          return group.role === 'admin';
        case 'member':
          return group.role === 'member';
        default:
          return true;
      }
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime();
        case 'name':
          return a.name.localeCompare(b.name);
        case 'members':
          return (b.members_count || 0) - (a.members_count || 0);
        case 'newest':
        default:
          return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
      }
    });

  if (loading && groups.length === 0) {
    return (
      <div className="flex justify-center py-8">
        <Loading />
      </div>
    );
  }

  if (groups.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Users className="w-12 h-12  text-gray-400 mb-4" />
          <p className="text-gray-500">{emptyMessage}</p>
          {emptySubMessage && (
            <p className="text-sm text-gray-400 mt-2">{emptySubMessage}</p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
            <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </Button>
        </div>

        {(sortBy !== 'newest' || filterBy !== 'all' || searchTerm) && (
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {groups.length !== sortedAndFilteredGroups.length && `${sortedAndFilteredGroups.length} of `}
              {groups.length} groups
            </Badge>
            {(sortBy !== 'newest' || filterBy !== 'all' || searchTerm) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSortBy('newest');
                  setFilterBy('all');
                  setSearchTerm('');
                }}
                className="text-xs"
              >
                Clear filters
              </Button>
            )}
          </div>
        )}

        {showFilters && (
          <Card>
            <CardContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 ga">
                <div>
                  <label className="text-sm font-medium mb-2 block">Sort by</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="newest">Recently joined</option>
                    <option value="oldest">First joined</option>
                    <option value="name">Name (A-Z)</option>
                    <option value="members">Most members</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Filter by</label>
                  <select
                    value={filterBy}
                    onChange={(e) => setFilterBy(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="all">All groups</option>
                    <option value="public">Public groups</option>
                    <option value="private">Private groups</option>
                    {isOwnProfile && (
                      <>
                        <option value="admin">Groups I admin</option>
                        <option value="member">Groups I'm member of</option>
                      </>
                    )}
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 ga">
        {sortedAndFilteredGroups.map((group) => (
          <Card key={group._id} className="hover:shadow-md transition-shadow">
            <CardContent className="">
              <div className="flex items-start gap-3">
                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                  {group.image ? (
                    <img 
                      src={group.image} 
                      alt={group.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Users className="w-6 h-6 text-gray-500" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-1 mb-1">
                        <h3 className="font-semibold text-sm truncate">{group.name}</h3>
                        {group.isPrivate ? (
                          <Lock className="w-3 h-3 text-gray-500 flex-shrink-0" />
                        ) : (
                          <Globe className="w-3 h-3 text-gray-500 flex-shrink-0" />
                        )}
                        {getRoleIcon(group.role)}
                      </div>
                      
                      {group.description && (
                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                          {group.description}
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Users className="w-3 h-3" />
                          <span>{formatMemberCount(group.members_count)}</span>
                        </div>
                        {getRoleBadge(group.role)}
                      </div>
                    </div>
                    
                    <Button variant="ghost" size="sm" className="ml-2">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="mt-3 pt-3 border-t border-gray-100">
                <Link href={`/groups/${group._id}`}>
                  <Button variant="outline" size="sm" className="w-full">
                    View Group
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="flex justify-center pt-4">
          <Button 
            variant="outline" 
            onClick={onLoadMore}
            disabled={loading}
          >
            {loading ? <Loading /> : 'Load More Groups'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default GroupsTab;

'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Loading } from '@/components/ui/loading';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  Heart,
  Share2,
  MoreHorizontal,
  Edit,
  Trash2,
  Flag,
  ChevronDown,
  Filter
} from 'lucide-react';
import { ImageViewer } from '@/components/ui/image-viewer';

interface Post {
  _id: string;
  content: string;
  author: {
    _id: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  };
  likes: number;
  comments: number;
  createdAt: string;
  image?: string;
  video?: string;
  tags?: Array<{_id: string; name: string}>;
  isLiked?: boolean;
}

interface PostsTabProps {
  posts: Post[];
  loading?: boolean;
  isOwnProfile?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  onLike?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onShare?: (postId: string) => void;
  onEdit?: (postId: string) => void;
  onDelete?: (postId: string) => void;
  onReport?: (postId: string) => void;
  emptyMessage?: string;
  emptySubMessage?: string;
}

const PostsTab: React.FC<PostsTabProps> = ({
  posts,
  loading = false,
  isOwnProfile = false,
  onLoadMore,
  hasMore = false,
  onLike,
  onComment,
  onShare,
  onEdit,
  onDelete,
  onReport,
  emptyMessage = "No posts yet",
  emptySubMessage
}) => {
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest');
  const [filterBy, setFilterBy] = useState<'all' | 'images' | 'videos'>('all');
  const [showFilters, setShowFilters] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 168) {
      const days = Math.floor(diffInHours / 24);
      return `${days}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const sortedAndFilteredPosts = posts
    .filter(post => {
      if (filterBy === 'images') return post.image;
      if (filterBy === 'videos') return post.video;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'popular':
          return (b.likes + b.comments) - (a.likes + a.comments);
        case 'newest':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

  if (loading && posts.length === 0) {
    return (
      <div className="flex justify-center py-8">
        <Loading />
      </div>
    );
  }

  if (posts.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <MessageSquare className="w-12 h-12  text-gray-400 mb-4" />
          <p className="text-gray-500">{emptyMessage}</p>
          {emptySubMessage && (
            <p className="text-sm text-gray-400 mt-2">{emptySubMessage}</p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
            <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </Button>
          
          {(sortBy !== 'newest' || filterBy !== 'all') && (
            <Badge variant="secondary" className="text-xs">
              {posts.length !== sortedAndFilteredPosts.length && `${sortedAndFilteredPosts.length} of `}
              {posts.length} posts
            </Badge>
          )}
        </div>
      </div>

      {showFilters && (
        <Card>
          <CardContent className="pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 ga">
              <div>
                <label className="text-sm font-medium mb-2 block">Sort by</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="newest">Newest first</option>
                  <option value="oldest">Oldest first</option>
                  <option value="popular">Most popular</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Filter by</label>
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="all">All posts</option>
                  <option value="images">Posts with images</option>
                  <option value="videos">Posts with videos</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Posts */}
      <div className="space-y-4">
        {sortedAndFilteredPosts.map((post) => (
          <Card key={post._id}>
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-300 flex items-center justify-center">
                  {post.author.profile_picture ? (
                    <img
                      src={post.author.profile_picture}
                      alt={`${post.author.first_name} ${post.author.last_name}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-300 flex items-center justify-center text-sm font-bold text-gray-600">
                      {post.author.first_name[0]}{post.author.last_name[0]}
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">
                        {post.author.first_name} {post.author.last_name}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDate(post.createdAt)}
                      </span>
                    </div>
                    
                    <div className="relative">
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <p className="text-gray-800 mb-3">{post.content}</p>
                  
                  {post.image && (
                    <img
                      src={post.image}
                      alt="Post image"
                      className="w-full max-w-md rounded-lg mb-3 cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => {
                        setSelectedImage(post.image!);
                        setShowImageViewer(true);
                      }}
                    />
                  )}
                  
                  {post.video && (
                    <video 
                      src={post.video} 
                      controls
                      className="w-full max-w-md rounded-lg mb-3"
                    />
                  )}
                  
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {post.tags.map((tag) => (
                        <Badge key={tag._id} variant="outline" className="text-xs">
                          #{tag.name}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <div className="flex items-center ga text-sm text-gray-500">
                    <button 
                      className={`flex items-center gap-1 hover:text-red-500 ${post.isLiked ? 'text-red-500' : ''}`}
                      onClick={() => onLike?.(post._id)}
                    >
                      <Heart className={`w-4 h-4 ${post.isLiked ? 'fill-current' : ''}`} />
                      {post.likes}
                    </button>
                    <button 
                      className="flex items-center gap-1 hover:text-blue-500"
                      onClick={() => onComment?.(post._id)}
                    >
                      <MessageSquare className="w-4 h-4" />
                      {post.comments}
                    </button>
                    <button 
                      className="flex items-center gap-1 hover:text-green-500"
                      onClick={() => onShare?.(post._id)}
                    >
                      <Share2 className="w-4 h-4" />
                      Share
                    </button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {hasMore && (
        <div className="flex justify-center pt-4">
          <Button 
            variant="outline" 
            onClick={onLoadMore}
            disabled={loading}
          >
            {loading ? <Loading /> : 'Load More Posts'}
          </Button>
        </div>
      )}

      {/* Image Viewer */}
      <ImageViewer
        isOpen={showImageViewer}
        onClose={() => setShowImageViewer(false)}
        imageUrl={selectedImage}
        alt="Post image"
      />
    </div>
  );
};

export default PostsTab;

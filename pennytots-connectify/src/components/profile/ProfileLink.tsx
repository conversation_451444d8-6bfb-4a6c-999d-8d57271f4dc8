'use client';

import Link from 'next/link';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';

interface ProfileLinkProps {
  userId: string;
  firstName: string;
  lastName: string;
  profilePicture?: string;
  className?: string;
  showAvatar?: boolean;
  avatarSize?: 'sm' | 'md' | 'lg';
  children?: React.ReactNode;
}

const ProfileLink: React.FC<ProfileLinkProps> = ({
  userId,
  firstName,
  lastName,
  profilePicture,
  className = '',
  showAvatar = false,
  avatarSize = 'sm',
  children
}) => {
  const currentUser = useSelector(userAuthInfo);
  const isOwnProfile = currentUser?._id === userId;
  
  // Determine the profile URL
  const profileUrl = isOwnProfile ? '/profile' : `/profile/${userId}`;
  
  // Avatar size classes
  const avatarSizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <Link 
      href={profileUrl}
      className={`inline-flex items-center gap-2 hover:underline ${className}`}
    >
      {showAvatar && (
        <div className={`${avatarSizeClasses[avatarSize]} rounded-full overflow-hidden bg-gray-300 flex items-center justify-center`}>
          {profilePicture ? (
            <img
              src={profilePicture}
              alt={`${firstName} ${lastName}`}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-300 flex items-center justify-center font-bold text-gray-600">
              {firstName[0]}{lastName[0]}
            </div>
          )}
        </div>
      )}
      {children || (
        <span className={textSizeClasses[avatarSize]}>
          {firstName} {lastName}
        </span>
      )}
    </Link>
  );
};

export default ProfileLink;

'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Loading } from '@/components/ui/loading';
import { Camera, Upload, X, AlertCircle } from 'lucide-react';


interface ImageUploadProps {
  type: 'profile' | 'header';
  currentImage?: string;
  onImageChange: (file: File, previewUrl: string) => void;
  onImageRemove?: () => void;
  className?: string;
  disabled?: boolean;
  maxSizeInMB?: number;
  acceptedFormats?: string[];
  profileData?: {
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  type,
  currentImage,
  onImageChange,
  onImageRemove,
  className = '',
  disabled = false,
  maxSizeInMB = 5,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  profileData
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string>('');
  const [previewUrl, setPreviewUrl] = useState<string>(currentImage || '');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): Promise<string | null> => {
    return new Promise((resolve) => {
      // Check file type
      if (!acceptedFormats.includes(file.type)) {
        resolve(`Please select a valid image file (${acceptedFormats.map(f => f.split('/')[1]).join(', ')})`);
        return;
      }

      // Check file size
      const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
      if (file.size > maxSizeInBytes) {
        resolve(`File size must be less than ${maxSizeInMB}MB`);
        return;
      }

      // Check image dimensions for profile pictures
      if (type === 'profile') {
        const img = new Image();
        img.onload = () => {
          if (img.width < 100 || img.height < 100) {
            resolve('Profile picture must be at least 100x100 pixels');
          } else if (img.width > 2000 || img.height > 2000) {
            resolve('Profile picture must be less than 2000x2000 pixels');
          } else {
            resolve(null);
          }
        };
        img.onerror = () => resolve('Invalid image file');
        img.src = URL.createObjectURL(file);
      } else {
        resolve(null);
      }
    });
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setError('');
    setIsUploading(true);

    try {
      const validationError = await validateFile(file);
      if (validationError) {
        setError(validationError);
        return;
      }

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPreviewUrl(result);
        onImageChange(file, result);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      setError('Error processing image');
      console.error('Image upload error:', error);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveImage = () => {
    setPreviewUrl('');
    setError('');
    if (onImageRemove) {
      onImageRemove();
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  if (type === 'profile') {
    return (
      <div className={`absolute bottom-0 left-0 w-[80px] h-[80px] ${className}`}>
        <div className="relative w-full h-full rounded-full overflow-hidden bg-gray-300 border-4 border-white">
          {/* Profile Image */}
          {currentImage || profileData?.profile_picture ? (
            <img
              src={currentImage || profileData?.profile_picture}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          ) : (
            <img
              src="/connectify-default-thumbnail.svg"
              alt="Default Profile"
              className="w-full h-full object-cover"
            />
          )}

          {/* Camera Button Overlay */}
          <div className="absolute inset-0  bg-opacity-0 hover:bg-opacity-30 flex items-center justify-center transition-all duration-200">
            <button
              type="button"
              onClick={triggerFileInput}
              disabled={disabled || isUploading}
              className="opacity-0 cursor-pointer hover:opacity-100 bg-black bg-opacity-50 text-white p-1.5 rounded-full transition-opacity duration-200"
            >
              <Camera className="w-3 h-3" />
            </button>
          </div>

          {/* Loading Overlay */}
          {isUploading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <Loading />
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled || isUploading}
        />
      </div>
    );
  }

  // Header image upload
  return (
    <div className={`relative rounded-tl-[12px] rounded-tr-[12px] rounded-bl-[39px] rounded-br-[12px] overflow-hidden ${className}`}>
      <div className="relative h-[210px]">
        {/* Header Image */}
        {currentImage ? (
          <img
            src={currentImage}
            alt="Header preview"
            className="w-full h-full object-cover"
          />
        ) : (
          <img
            src="/default-header-img.png"
            alt="Default Header"
            className="w-full h-full object-cover"
          />
        )}

        {/* Loading Overlay */}
        {isUploading && (
          <div className="absolute inset-0 bg-transparent bg-opacity-50 flex items-center justify-center">
            <Loading />
          </div>
        )}

        {/* Camera Button Overlay */}
        <div className="absolute inset-0 bg-transparent bg-opacity-30 flex items-center justify-center">
          <button
            type="button"
            onClick={triggerFileInput}
            disabled={disabled || isUploading}
            className="flex items-center cursor-pointer justify-center text-white bg-[#163E23] bg-opacity-50 w-[47px] h-[45px] rounded-[38px] hover:bg-opacity-70 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <Camera className="w-4 h-4" />
          </button>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled || isUploading}
      />
    </div>
  );
};

export default ImageUpload;

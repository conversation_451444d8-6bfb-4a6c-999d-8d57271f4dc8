'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';
import {
  handleProfileClick as handleProfileNavigation,
  createProfileChat<PERSON><PERSON><PERSON>,
  ChatUser,
  ProfileClickOptions
} from '@/lib/chat-utils';
import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { MessageCircle, User, MoreVertical } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { poppinsRegular, poppinsMedium } from '@/fonts';

interface ProfileClickHandlerProps {
  user: ChatUser;
  children?: React.ReactNode;
  className?: string;
  showAvatar?: boolean;
  avatarSize?: number;
  showDropdown?: boolean;
  defaultAction?: 'chat' | 'profile' | 'menu';
  onChatStart?: (chatData: any) => void;
  onProfileView?: (userId: string) => void;
  disabled?: boolean;
}

const ProfileClickHandler: React.FC<ProfileClickHandlerProps> = ({
  user,
  children,
  className = '',
  showAvatar = false,
  avatarSize = 32,
  showDropdown = false,
  defaultAction = 'chat',
  onChatStart,
  onProfileView,
  disabled = false
}) => {
  const router = useRouter();
  const currentUser = useSelector(userAuthInfo);
  const [isLoading, setIsLoading] = useState(false);

 const handleClick = async (action?: 'chat' | 'profile', e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (disabled || isLoading) return;
    if (user._id === currentUser?._id) {
      console.warn('Cannot interact with yourself');
      return;
    }

    setIsLoading(true);

    try {
      const actionToTake = action || defaultAction;

      const options: ProfileClickOptions = {
        showChatOption: actionToTake === 'chat' || actionToTake === 'menu',
        // showProfileOption: actionToTake === 'profile' || actionToTake === 'menu',
        onChatStart,
        onProfileView
      };

      console.log('ProfileClickHandler: Starting navigation with options:', options);
      await handleProfileNavigation(user, router, currentUser?._id, options);
      console.log('ProfileClickHandler: Navigation completed');
    } catch (error) {
      console.error('ProfileClickHandler: Error handling profile click:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChatClick = (e: React.MouseEvent) => {
    handleClick('chat', e);
  };

  const handleProfileViewClick = (e: React.MouseEvent) => {
    handleClick('profile', e);
  };

  const renderContent = () => {
    if (children) {
      return children;
    }

    const displayName = user.firstName || user.first_name;
    const displayLastName = user.lastName || user.last_name;

    return (
      <div className="flex items-center gap-2">
        {showAvatar && (
          <Avatar
            source={user.profilePicture || user.profile_picture}
            size={avatarSize}
            profilePicture={user.profilePicture || user.profile_picture}
          />
        )}
        <span className={`${poppinsMedium.className} text-sm`}>
          {displayName} {displayLastName}
        </span>
      </div>
    );
  };

  if (showDropdown) {
    return (
      <div className={`inline-flex items-center ${className}`}>
        <button
          onClick={(e) => handleClick('chat', e)} // works fine

          disabled={disabled || isLoading}
          className={`flex items-center gap-2 hover:opacity-80 transition-opacity ${
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          }`}
        >
          {renderContent()}
        </button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 ml-1"
              disabled={disabled || isLoading}
            >
              <MoreVertical className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleChatClick}>
              <MessageCircle className="h-4 w-4 mr-2" />
              Message
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleProfileViewClick}>
              <User className="h-4 w-4 mr-2" />
              View Profile
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  return (
    <button
      onClick={(e) => handleClick(undefined, e)}
      disabled={disabled || isLoading}
      className={`inline-flex items-center gap-2 hover:opacity-80 transition-opacity ${
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
      } ${className}`}
    >
      {renderContent()}
      {isLoading && (
        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 ml-1"></div>
      )}
    </button>
  );
};

export default ProfileClickHandler;

// Export a simplified version for common use cases
export const ProfileChatLink: React.FC<{
  user: ChatUser;
  children?: React.ReactNode;
  className?: string;
  showAvatar?: boolean;
  onChatStart?: (chatData: any) => void;
}> = ({ user, children, className, showAvatar, onChatStart }) => {
  return (
    <ProfileClickHandler
      user={user}
      className={className}
      showAvatar={showAvatar}
      defaultAction="chat"
      onChatStart={onChatStart}
    >
      {children}
    </ProfileClickHandler>
  );
};

// Export a profile link version
export const ProfileViewLink: React.FC<{
  user: ChatUser;
  children?: React.ReactNode;
  className?: string;
  showAvatar?: boolean;
  onProfileView?: (userId: string) => void;
}> = ({ user, children, className, showAvatar, onProfileView }) => {
  return (
    <ProfileClickHandler
      user={user}
      className={className}
      showAvatar={showAvatar}
      defaultAction="profile"
      onProfileView={onProfileView}
    >
      {children}
    </ProfileClickHandler>
  );
};

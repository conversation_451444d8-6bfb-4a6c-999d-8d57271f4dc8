'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, RefreshCw, X } from 'lucide-react';
import { poppinsRegular, poppinsMedium, poppinsBold } from '@/fonts';
import { useGetSponsoredAd } from '@/redux/sponsor/hooks';

interface SponsoredAdProps {
  className?: string;
  showCloseButton?: boolean;
  onClose?: () => void;
  variant?: 'card' | 'banner' | 'inline';
  size?: 'small' | 'medium' | 'large';
}

const SponsoredAd: React.FC<SponsoredAdProps> = ({
  className = '',
  showCloseButton = false,
  onClose,
  variant = 'card',
  size = 'medium'
}) => {
  const { adData, isLoading, error, refetch } = useGetSponsoredAd();
  const [isVisible, setIsVisible] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Auto-refresh ad every 30 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 30 * 60 * 1000); // 30 minutes

    return () => clearInterval(interval);
  }, [refetch]);

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleAdClick = () => {
    if (adData?.link) {
      window.open(adData.link, '_blank', 'noopener,noreferrer');
    }
  };

  if (!isVisible || isLoading || error || !adData) {
    return null;
  }

  const sizeClasses = {
    small: 'h-24',
    medium: 'h-32',
    large: 'h-48'
  };

  const containerClasses = {
    card: 'rounded-lg overflow-hidden shadow-sm',
    banner: 'rounded-none overflow-hidden',
    inline: 'rounded-md overflow-hidden'
  };

  return (
    <div className={`${className} ${containerClasses[variant]}`}>
      <Card className="border-0 shadow-sm">
        <CardContent className="p-0 relative">
          {/* Close Button */}
          {showCloseButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="absolute top-2 right-2 z-10 h-6 w-6 p-0 bg-black/20 hover:bg-black/40 text-white rounded-full"
            >
              <X className="h-3 w-3" />
            </Button>
          )}

          {/* Sponsored Badge */}
          <div className="absolute top-2 left-2 z-10">
            <Badge variant="secondary" className="text-xs bg-black/60 text-white border-0">
              Sponsored
            </Badge>
          </div>

          {/* Ad Image */}
          <div 
            className={`relative ${sizeClasses[size]} cursor-pointer group`}
            onClick={handleAdClick}
          >
            {!imageError ? (
              <img
                src={adData.image}
                alt={adData.title || 'Sponsored Advertisement'}
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
                onError={handleImageError}
                loading="lazy"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="text-2xl mb-2">📢</div>
                  <p className={`${poppinsMedium.className} text-sm`}>
                    {adData.title || 'Sponsored Content'}
                  </p>
                </div>
              </div>
            )}

            {/* Hover Overlay */}
            {adData.link && (
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="bg-white/90 rounded-full p-2">
                  <ExternalLink className="h-4 w-4 text-gray-700" />
                </div>
              </div>
            )}
          </div>

          {/* Ad Content */}
          {(adData.title || adData.description) && (
            <div className="p-3">
              {adData.title && (
                <h3 
                  className={`${poppinsMedium.className} text-sm font-medium text-gray-900 mb-1 cursor-pointer hover:text-blue-600 transition-colors`}
                  onClick={handleAdClick}
                >
                  {adData.title}
                </h3>
              )}
              
              {adData.description && (
                <p className={`${poppinsRegular.className} text-xs text-gray-600 line-clamp-2`}>
                  {adData.description}
                </p>
              )}

              {adData.cta && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAdClick}
                  className="mt-2 h-7 text-xs"
                >
                  {adData.cta}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              )}
            </div>
          )}

          {/* Refresh Button for Development */}
          {process.env.NODE_ENV === 'development' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refetch()}
              className="absolute bottom-2 right-2 h-6 w-6 p-0 bg-black/20 hover:bg-black/40 text-white rounded-full"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SponsoredAd;

// Preset components for common use cases
export const SponsoredBanner: React.FC<Omit<SponsoredAdProps, 'variant'>> = (props) => (
  <SponsoredAd {...props} variant="banner" />
);

export const SponsoredCard: React.FC<Omit<SponsoredAdProps, 'variant'>> = (props) => (
  <SponsoredAd {...props} variant="card" />
);

export const SponsoredInline: React.FC<Omit<SponsoredAdProps, 'variant'>> = (props) => (
  <SponsoredAd {...props} variant="inline" />
);

// Hook for managing ad visibility
export const useSponsoredAdVisibility = (initialVisible = true) => {
  const [isVisible, setIsVisible] = useState(initialVisible);
  const [dismissedAds, setDismissedAds] = useState<Set<string>>(new Set());

  const dismissAd = (adId: string) => {
    setDismissedAds(prev => new Set([...prev, adId]));
  };

  const isAdDismissed = (adId: string) => {
    return dismissedAds.has(adId);
  };

  const resetDismissed = () => {
    setDismissedAds(new Set());
  };

  return {
    isVisible,
    setIsVisible,
    dismissAd,
    isAdDismissed,
    resetDismissed
  };
};

// Ad placement component for inserting ads between content
export const AdPlacement: React.FC<{
  position: number;
  totalItems: number;
  frequency?: number;
  children: React.ReactNode;
}> = ({ position, totalItems, frequency = 5, children }) => {
  const shouldShowAd = position > 0 && position % frequency === 0 && position < totalItems - 1;

  if (!shouldShowAd) {
    return <>{children}</>;
  }

  return (
    <>
      {children}
      <div className="my-4">
        <SponsoredAd variant="inline" size="medium" />
      </div>
    </>
  );
};

"use client"

import type React from "react"
import { useState } from "react"
import { useRouter, usePathname } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { LogoutIcon } from "@/components/svgReactComponents/NavigationIcons"
import Image from "next/image"
import { poppins, poppinsRegular, poppinsSemibold } from "@/fonts"
import { PostModal } from "@/components/ui/post-modal"



interface SidebarItem {
  id: string
  icon: React.ComponentType<{ className?: string }>
  label: string
  component: React.ComponentType
}

interface SidebarProps {
  items: SidebarItem[]
  activeScreen: string
  onScreenChange: (screenId: string) => void
  onLogout: () => void
}

export default function Sidebar({ items, activeScreen, onScreenChange, onLogout }: SidebarProps) {
  const [isPostModalOpen, setIsPostModalOpen] = useState(false);
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [isAreaofInterest, setIsAreaofInterest] = useState(false)
  const router = useRouter();
  const pathname = usePathname();

  const handleOpenPostModal = () => {
    setIsPostModalOpen(true);
  };
  const handleOpenHelpModal = () => {
    setIsHelpModalOpen(true);
  };

  const handleOpenAreaOfInterestModal = () => {
   setIsAreaofInterest(true)
  }

   const handleCloseArea = () => {
    setIsAreaofInterest(false);
  };

  const handleClosePostModal = () => {
    setIsPostModalOpen(false);
  };
  const handleCloseHelpModal = () => {
    setIsHelpModalOpen(false);
  };

  const handleNavigation = (screenId: string) => {
    // Map screen IDs to routes
    const routeMap: { [key: string]: string } = {
      home: '/home',
      chats: '/chats',
      groups: '/groups',
      profile: '/profile',
      settings: '/settings',
      trivia: '/trivia',
      helpdesk: '/helpdesk',
    };

    const route = routeMap[screenId];
    if (route) {
      router.push(route);
    }

    // Also call the original handler if provided
    onScreenChange?.(screenId);
  };

  // Determine active screen from pathname
  const getActiveScreenFromPath = () => {
    if (pathname === '/home' || pathname === '/') return 'home';
    if (pathname === '/chats') return 'chats';
    if (pathname === '/groups') return 'groups';
    if (pathname === '/profile') return 'profile';
    if (pathname === '/settings') return 'settings';
    if (pathname === '/trivia') return 'trivia';
    if (pathname === '/helpdesk') return 'helpdesk';
    return activeScreen;
  };

  const currentActiveScreen = getActiveScreenFromPath();

  return (
    <>
      <div className=" bg-white flex flex-col justify-between w-full ">
        {/* Navigation Items */}
        <nav className="flex py-[1rem] pl-[1.25rem]" >
          <ul className="flex flex-col gap-[1rem]">
            {(items || []).map((item) => (
              <li key={item.id}>
                <Button
                  variant={currentActiveScreen === item.id ? "secondary" : "ghost"}
                  className={`${poppins.className} cursor-pointer py-[1rem] px-[8px] items-center gap-[1rem] flex justify-start w-full `}
                  onClick={() => handleNavigation(item.id)}
                >
                  <item.icon className="flex-shrink-0" />
                  <span className={` ${poppinsRegular.className} text-[#797C7B] text-[1.25rem]`}>{item.label}</span>
                </Button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Bottom Section */}
        <div className="flex flex-col mb-[8rem]">
          {/* Make a post button */}

          <div className="pl-[2rem]" >
            <Button
              variant="ghost"
              className={`${poppins.className} gap-[1rem] text-[#E51616] cursor-pointer items-center  flex justify-start w-full focus:outline-none focus:ring-0`}
              onClick={onLogout}
            >
              <LogoutIcon className="w-5 h-5 flex-shrink-0" />
              <span className="truncate">Log out</span>
            </Button>
          </div>
        </div>
        {pathname === '/home' ? (
  <div className="flex max-w-[216px] w-full " >
        <Button
      className={`${poppinsSemibold.className} py-[0.875rem] rounded-[32px] bg-[#F2875D] cursor-pointer w-full justify-center text-white flex focus:outline-none focus:ring-0`}
              onClick={handleOpenPostModal}     
    >
      <span className="truncate">Make a post</span>
    </Button>
  </div>
) : pathname === '/helpdesk' ? (
  <div className="flex flex-col items-center justify-center" >
    <Button
      className={`${poppinsSemibold.className} py-[0.875rem] rounded-[32px] bg-[#F2875D] cursor-pointer w-full justify-center text-white flex focus:outline-none focus:ring-0`}
              onClick={handleOpenHelpModal}
    >
      <span className="truncate">Make a complaint</span>
    </Button>
  </div>
) : null}

      </div>
      {/* Post Modal */}
      <PostModal
  isOpen={isPostModalOpen || isHelpModalOpen || isAreaofInterest}
  onClose={isPostModalOpen ? handleClosePostModal : isHelpModalOpen ? handleCloseHelpModal : handleCloseArea}
  mode={isPostModalOpen ? 'post' : isPostModalOpen ? 'helpdesk' : 'area-of-interest'}
/>

    </>
  )
}

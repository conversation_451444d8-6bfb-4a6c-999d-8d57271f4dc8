'use client';

import React from 'react';
import ProfileClickHandler from '@/components/profile/ProfileClickHandler';
import { poppinsSemibold } from '@/fonts';

// Test component to verify ProfileClickHandler functionality
const ProfileClickTest: React.FC = () => {
  // Sample user data for testing (mimicking topic userId structure)
  const testUser = {
    _id: 'test-user-123',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    profile_picture: '',
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON>',
    profilePicture: ''
  };

  // Test user with missing fields (to test error handling)
  const testUserIncomplete = {
    _id: 'test-user-456',
    first_name: '<PERSON>',
    last_name: '', // Empty last name
    profile_picture: '',
  };

  const handleChatStart = (chatData: any) => {
    console.log('Chat started with data:', chatData);
  };

  const handleProfileView = (userId: string) => {
    console.log('Profile viewed for user:', userId);
  };

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">ProfileClickHandler Test</h1>
      
      <div className="space-y-6">
        <div className="border p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Basic Profile Click (Chat)</h2>
          <p className="text-sm text-gray-600 mb-3">
            This should show a loading spinner when clicked and then navigate to chat.
          </p>
          <ProfileClickHandler
            user={testUser}
            defaultAction="chat"
            onChatStart={handleChatStart}
          >
            <h3 className={`text-[#5A5E5C] text-[1rem] hover:text-[#F56630] cursor-pointer ${poppinsSemibold.className}`}>
              {`${testUser.first_name} ${testUser.last_name}`.slice(0, 35)}
              {(testUser.first_name.length + testUser.last_name.length > 35) ? '...' : ''}
            </h3>
          </ProfileClickHandler>
        </div>

        <div className="border p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Profile Click with Dropdown</h2>
          <p className="text-sm text-gray-600 mb-3">
            This should show both chat and profile options.
          </p>
          <ProfileClickHandler
            user={testUser}
            showDropdown={true}
            onChatStart={handleChatStart}
            onProfileView={handleProfileView}
          />
        </div>

        <div className="border p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Profile Click with Avatar</h2>
          <p className="text-sm text-gray-600 mb-3">
            This should show an avatar alongside the name.
          </p>
          <ProfileClickHandler
            user={testUser}
            showAvatar={true}
            defaultAction="chat"
            onChatStart={handleChatStart}
          />
        </div>

        <div className="border p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Profile Click with Incomplete Data</h2>
          <p className="text-sm text-gray-600 mb-3">
            This tests error handling with incomplete user data.
          </p>
          <ProfileClickHandler
            user={testUserIncomplete}
            defaultAction="chat"
            onChatStart={handleChatStart}
          >
            <h3 className={`text-[#5A5E5C] text-[1rem] hover:text-[#F56630] cursor-pointer ${poppinsSemibold.className}`}>
              {`${testUserIncomplete.first_name} ${testUserIncomplete.last_name || 'Unknown'}`.slice(0, 35)}
            </h3>
          </ProfileClickHandler>
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Expected Behavior:</h3>
        <ul className="text-sm space-y-1">
          <li>• Clicking should show a loading spinner next to the name</li>
          <li>• After loading, it should navigate to /messages/test-user-123</li>
          <li>• The loading should not hang indefinitely</li>
          <li>• Console should show chat creation logs</li>
        </ul>
      </div>
    </div>
  );
};

export default ProfileClickTest;

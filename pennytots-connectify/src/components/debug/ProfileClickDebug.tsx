'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ProfileClickHandler from '@/components/profile/ProfileClickHandler';
import { poppinsRegular, poppinsMedium } from '@/fonts';

// Test user data
const testUser = {
  _id: '686f48e54622ba4de88913cb', // The user ID from your console log
  first_name: 'Test',
  last_name: 'User',
  profile_picture: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
};

const ProfileClickDebug: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]); // Keep last 20 logs
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const handleChatStart = (chatData: any) => {
    addLog(`✅ Chat started successfully: ${JSON.stringify(chatData)}`);
  };

  const handleProfileView = (userId: string) => {
    addLog(`👤 Profile view requested for user: ${userId}`);
  };

  const testDirectNavigation = () => {
    addLog('🧪 Testing direct navigation...');
    window.location.href = `/messages/${testUser._id}`;
  };

  const testAPICall = async () => {
    addLog('🧪 Testing API call...');
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/user/${testUser._id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        addLog(`✅ API call successful: ${JSON.stringify(data)}`);
      } else {
        addLog(`❌ API call failed: ${response.status} ${response.statusText}`);
      }
    } catch (error: any) {
      addLog(`❌ API call error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className={poppinsMedium.className}>
            Profile Click Debug Tool
          </CardTitle>
          <p className={`${poppinsRegular.className} text-gray-600`}>
            Debug tool to test profile click functionality and identify issues.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Test Profile Click */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>Test Profile Click</h3>
            <div className="flex items-center gap-4">
              <ProfileClickHandler
                user={testUser}
                showAvatar={true}
                avatarSize={40}
                defaultAction="chat"
                onChatStart={handleChatStart}
                onProfileView={handleProfileView}
                className="p-2 border rounded hover:bg-gray-50"
              >
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
                    {testUser.first_name[0]}
                  </div>
                  <span>{testUser.first_name} {testUser.last_name}</span>
                </div>
              </ProfileClickHandler>
              
              <span className={`${poppinsRegular.className} text-gray-500 text-sm`}>
                ← Click this to test profile click functionality
              </span>
            </div>
          </div>

          {/* Manual Tests */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>Manual Tests</h3>
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={testDirectNavigation}
                variant="outline" 
                size="sm"
              >
                Test Direct Navigation
              </Button>
              <Button 
                onClick={testAPICall}
                variant="outline" 
                size="sm"
                disabled={isLoading}
              >
                {isLoading ? 'Testing...' : 'Test API Call'}
              </Button>
              <Button 
                onClick={clearLogs}
                variant="outline" 
                size="sm"
              >
                Clear Logs
              </Button>
            </div>
          </div>

          {/* Debug Information */}
          <div className="p-4 border rounded">
            <h3 className={`${poppinsMedium.className} text-sm mb-3`}>Debug Information</h3>
            <div className="space-y-2 text-sm">
              <div><strong>Test User ID:</strong> {testUser._id}</div>
              <div><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</div>
              <div><strong>Token Present:</strong> {typeof window !== 'undefined' && localStorage.getItem('token') ? '✅ Yes' : '❌ No'}</div>
            </div>
          </div>

          {/* Logs */}
          <div className="p-4 border rounded">
            <div className="flex items-center justify-between mb-3">
              <h3 className={`${poppinsMedium.className} text-sm`}>Debug Logs</h3>
              <span className={`${poppinsRegular.className} text-xs text-gray-500`}>
                {logs.length} entries
              </span>
            </div>
            <div className="bg-gray-50 rounded p-3 max-h-64 overflow-y-auto">
              {logs.length === 0 ? (
                <p className={`${poppinsRegular.className} text-gray-500 text-sm`}>
                  No logs yet. Try clicking the test profile above.
                </p>
              ) : (
                <div className="space-y-1">
                  {logs.map((log, index) => (
                    <div 
                      key={index} 
                      className={`${poppinsRegular.className} text-xs font-mono`}
                    >
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileClickDebug;

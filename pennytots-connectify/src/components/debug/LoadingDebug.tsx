'use client';

import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { appLoading, setLoading } from '../../redux/main/reducer';
import { Button } from '../ui/button';

export const LoadingDebug: React.FC = () => {
  const isLoading = useSelector(appLoading);
  const dispatch = useDispatch();

  const resetLoading = () => {
    dispatch(setLoading(false));
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  if (!isLoading) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-[9999] bg-red-500 text-white p-4 rounded-lg shadow-lg">
      <div className="flex items-center gap-2">
        <span>App Loading is stuck!</span>
        <Button
          onClick={resetLoading}
          variant="outline"
          size="sm"
          className="bg-white text-red-500 hover:bg-gray-100"
        >
          Reset
        </Button>
      </div>
    </div>
  );
};

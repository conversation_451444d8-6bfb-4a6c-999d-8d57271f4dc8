import { useState } from 'react';
import { Check, ChevronDown } from 'lucide-react';
import { poppins } from '@/fonts';

// Country list for country selection
const COUNTRIES = [
  { _id: 'NG', name: 'Nigeria' },
  { _id: 'US', name: 'United States' },
  { _id: 'GB', name: 'United Kingdom' },
  { _id: 'CA', name: 'Canada' },
  { _id: 'AU', name: 'Australia' },
  { _id: 'DE', name: 'Germany' },
  { _id: 'FR', name: 'France' },
  { _id: 'IT', name: 'Italy' },
  { _id: 'ES', name: 'Spain' },
  { _id: 'NL', name: 'Netherlands' },
  { _id: 'BE', name: 'Belgium' },
  { _id: 'CH', name: 'Switzerland' },
  { _id: 'AT', name: 'Austria' },
  { _id: 'SE', name: 'Sweden' },
  { _id: 'NO', name: 'Norway' },
  { _id: 'D<PERSON>', name: 'Denmark' },
  { _id: 'FI', name: 'Finland' },
  { _id: 'IE', name: 'Ireland' },
  { _id: 'PT', name: 'Portugal' },
  { _id: 'GR', name: 'Greece' },
  { _id: 'P<PERSON>', name: 'Poland' },
  { _id: 'CZ', name: 'Czech Republic' },
  { _id: 'HU', name: 'Hungary' },
  { _id: 'SK', name: 'Slovakia' },
  { _id: 'SI', name: 'Slovenia' },
  { _id: 'HR', name: 'Croatia' },
  { _id: 'BG', name: 'Bulgaria' },
  { _id: 'RO', name: 'Romania' },
  { _id: 'EE', name: 'Estonia' },
  { _id: 'LV', name: 'Latvia' },
  { _id: 'LT', name: 'Lithuania' },
  { _id: 'LU', name: 'Luxembourg' },
  { _id: 'MT', name: 'Malta' },
  { _id: 'CY', name: 'Cyprus' },
  { _id: 'JP', name: 'Japan' },
  { _id: 'KR', name: 'South Korea' },
  { _id: 'CN', name: 'China' },
  { _id: 'IN', name: 'India' },
  { _id: 'BR', name: 'Brazil' },
  { _id: 'MX', name: 'Mexico' },
  { _id: 'AR', name: 'Argentina' },
  { _id: 'CL', name: 'Chile' },
  { _id: 'CO', name: 'Colombia' },
  { _id: 'PE', name: 'Peru' },
  { _id: 'VE', name: 'Venezuela' },
  { _id: 'UY', name: 'Uruguay' },
  { _id: 'PY', name: 'Paraguay' },
  { _id: 'BO', name: 'Bolivia' },
  { _id: 'EC', name: 'Ecuador' },
  { _id: 'ZA', name: 'South Africa' },
  { _id: 'EG', name: 'Egypt' },
  { _id: 'MA', name: 'Morocco' },
  { _id: 'TN', name: 'Tunisia' },
  { _id: 'DZ', name: 'Algeria' },
  { _id: 'LY', name: 'Libya' },
  { _id: 'SD', name: 'Sudan' },
  { _id: 'ET', name: 'Ethiopia' },
  { _id: 'KE', name: 'Kenya' },
  { _id: 'UG', name: 'Uganda' },
  { _id: 'TZ', name: 'Tanzania' },
  { _id: 'RW', name: 'Rwanda' },
  { _id: 'GH', name: 'Ghana' },
  { _id: 'CI', name: 'Ivory Coast' },
  { _id: 'SN', name: 'Senegal' },
  { _id: 'ML', name: 'Mali' },
  { _id: 'BF', name: 'Burkina Faso' },
  { _id: 'NE', name: 'Niger' },
  { _id: 'TD', name: 'Chad' },
  { _id: 'CM', name: 'Cameroon' },
  { _id: 'CF', name: 'Central African Republic' },
  { _id: 'GA', name: 'Gabon' },
  { _id: 'CG', name: 'Republic of the Congo' },
  { _id: 'CD', name: 'Democratic Republic of the Congo' },
  { _id: 'AO', name: 'Angola' },
  { _id: 'ZM', name: 'Zambia' },
  { _id: 'ZW', name: 'Zimbabwe' },
  { _id: 'BW', name: 'Botswana' },
  { _id: 'NA', name: 'Namibia' },
  { _id: 'SZ', name: 'Eswatini' },
  { _id: 'LS', name: 'Lesotho' },
  { _id: 'MG', name: 'Madagascar' },
  { _id: 'MU', name: 'Mauritius' },
  { _id: 'SC', name: 'Seychelles' },
  { _id: 'MZ', name: 'Mozambique' },
  { _id: 'MW', name: 'Malawi' }
].sort((a, b) => a.name.localeCompare(b.name));

interface CountrySelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const CountrySelect = ({ 
  value, 
  onChange, 
  placeholder = "Select your country",
  className = "cursor-pointer w-full"
}: CountrySelectProps) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const handleSelectCountry = (countryId: string) => {
    onChange(countryId);
    setDropdownVisible(false);
  };

  const selectedCountry = COUNTRIES.find(country => country._id === value);

  return (
    <div className={`w-full ${className}`}>
      {/* Dropdown Trigger */}
      <button
        type="button"
        onClick={() => setDropdownVisible(!dropdownVisible)}
        className="w-full border border-gray-300 rounded-md px-3 py-3 mt-[8px] flex justify-between items-center"
      >
        <span className={`text-[#98A2B3] text-[14px] ${poppins.className}`}>
          {selectedCountry ? selectedCountry.name : placeholder}
        </span>
        <ChevronDown size={16} />
      </button>

      {/* Dropdown */}
      {dropdownVisible && (
        <div className="border border-[#E4E7EC] rounded-[8px] py-[8px] gap-[4px] mt-[8px] flex flex-col max-h-60 overflow-y-auto">
          {COUNTRIES.map((country) => {
            const selected = value === country._id;

            return (
              <button
                key={country._id}
                type="button"
                onClick={() => handleSelectCountry(country._id)}
                className="w-full py-[8px] px-[1rem] flex gap-[12px] items-center hover:bg-gray-50"
              >
                <span
                  className={`w-[20px] h-[20px] border rounded-full flex items-center justify-center ${
                    selected ? 'border-green-600' : 'border-gray-400'
                  }`}
                >
                  {selected && <Check size={14} className="text-green-600" />}
                </span>
                <span className="text-sm">{country.name}</span>
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default CountrySelect;

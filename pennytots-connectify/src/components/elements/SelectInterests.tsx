import { useState } from 'react';
import { Check, ChevronDown, X } from 'lucide-react';
import { useInterests } from '@/redux/tags/useAllInterest';
import { poppins, poppinsMedium } from '@/fonts';

// Country list for country selection
const COUNTRIES = [
  { _id: 'NG', name: 'Nigeria' },
  { _id: 'US', name: 'United States' },
  { _id: 'GB', name: 'United Kingdom' },
  { _id: 'CA', name: 'Canada' },
  { _id: 'AU', name: 'Australia' },
  { _id: 'DE', name: 'Germany' },
  { _id: 'FR', name: 'France' },
  { _id: 'IT', name: 'Italy' },
  { _id: 'ES', name: 'Spain' },
  { _id: 'NL', name: 'Netherlands' },
  { _id: 'BE', name: 'Belgium' },
  { _id: 'CH', name: 'Switzerland' },
  { _id: 'AT', name: 'Austria' },
  { _id: 'SE', name: 'Sweden' },
  { _id: 'NO', name: 'Norway' },
  { _id: 'D<PERSON>', name: 'Denmark' },
  { _id: 'FI', name: 'Finland' },
  { _id: 'IE', name: 'Ireland' },
  { _id: '<PERSON>', name: 'Portugal' },
  { _id: 'GR', name: 'Greece' },
  { _id: 'PL', name: 'Poland' },
  { _id: 'CZ', name: 'Czech Republic' },
  { _id: 'HU', name: 'Hungary' },
  { _id: 'SK', name: 'Slovakia' },
  { _id: 'SI', name: 'Slovenia' },
  { _id: 'HR', name: 'Croatia' },
  { _id: 'BG', name: 'Bulgaria' },
  { _id: 'RO', name: 'Romania' },
  { _id: 'EE', name: 'Estonia' },
  { _id: 'LV', name: 'Latvia' },
  { _id: 'LT', name: 'Lithuania' },
  { _id: 'LU', name: 'Luxembourg' },
  { _id: 'MT', name: 'Malta' },
  { _id: 'CY', name: 'Cyprus' },
  { _id: 'JP', name: 'Japan' },
  { _id: 'KR', name: 'South Korea' },
  { _id: 'CN', name: 'China' },
  { _id: 'IN', name: 'India' },
  { _id: 'BR', name: 'Brazil' },
  { _id: 'MX', name: 'Mexico' },
  { _id: 'AR', name: 'Argentina' },
  { _id: 'CL', name: 'Chile' },
  { _id: 'CO', name: 'Colombia' },
  { _id: 'PE', name: 'Peru' },
  { _id: 'VE', name: 'Venezuela' },
  { _id: 'UY', name: 'Uruguay' },
  { _id: 'PY', name: 'Paraguay' },
  { _id: 'BO', name: 'Bolivia' },
  { _id: 'EC', name: 'Ecuador' },
  { _id: 'ZA', name: 'South Africa' },
  { _id: 'EG', name: 'Egypt' },
  { _id: 'MA', name: 'Morocco' },
  { _id: 'TN', name: 'Tunisia' },
  { _id: 'DZ', name: 'Algeria' },
  { _id: 'LY', name: 'Libya' },
  { _id: 'SD', name: 'Sudan' },
  { _id: 'ET', name: 'Ethiopia' },
  { _id: 'KE', name: 'Kenya' },
  { _id: 'UG', name: 'Uganda' },
  { _id: 'TZ', name: 'Tanzania' },
  { _id: 'RW', name: 'Rwanda' },
  { _id: 'GH', name: 'Ghana' },
  { _id: 'CI', name: 'Ivory Coast' },
  { _id: 'SN', name: 'Senegal' },
  { _id: 'ML', name: 'Mali' },
  { _id: 'BF', name: 'Burkina Faso' },
  { _id: 'NE', name: 'Niger' },
  { _id: 'TD', name: 'Chad' },
  { _id: 'CM', name: 'Cameroon' },
  { _id: 'CF', name: 'Central African Republic' },
  { _id: 'GA', name: 'Gabon' },
  { _id: 'CG', name: 'Republic of the Congo' },
  { _id: 'CD', name: 'Democratic Republic of the Congo' },
  { _id: 'AO', name: 'Angola' },
  { _id: 'ZM', name: 'Zambia' },
  { _id: 'ZW', name: 'Zimbabwe' },
  { _id: 'BW', name: 'Botswana' },
  { _id: 'NA', name: 'Namibia' },
  { _id: 'SZ', name: 'Eswatini' },
  { _id: 'LS', name: 'Lesotho' },
  { _id: 'MG', name: 'Madagascar' },
  { _id: 'MU', name: 'Mauritius' },
  { _id: 'SC', name: 'Seychelles' },
  { _id: 'MZ', name: 'Mozambique' },
  { _id: 'MW', name: 'Malawi' }
].sort((a, b) => a.name.localeCompare(b.name));

interface IInterest {
  _id: string;
  name: string;
}

type SelectInterestsProps =
  | {
    multiple: true;
    setInterests: (interests: string[]) => void;
    interests: string[];
    type?: 'interests' | 'countries';
  }
  | {
    multiple?: false;
    setInterest: (interest: string) => void;
    interest: string;
    type?: 'interests' | 'countries';
  };

interface InterestButtonProps {
  item: IInterest;
  onDelete: () => void;
}

const InterestButton = ({ item, onDelete }: InterestButtonProps) => (
  <div className="">
    <div className="inline-flex flex items-center py-[8px] px-[12px] rounded-[20px] border gap-[8px] border-[#AFBBC5]">
      <span className={`${poppinsMedium.className} text-[#5A5E5C] text-[14px]`}>{item.name}</span>
      <button onClick={onDelete} className="text-black text-sm">
        <X size={14} />
      </button>
    </div>
  </div>

);

export const SelectInterests = (props: SelectInterestsProps) => {
  const { allInterests, loading: isLoading, error } = useInterests();
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Extract props based on multiple flag
  const multiple = props.multiple;
  const setInterests = multiple ? props.setInterests : undefined;
  const interests = multiple ? props.interests : [];
  const setInterest = !multiple ? props.setInterest : undefined;
  const interest = !multiple ? props.interest : '';
  const type = props.type || 'interests';

  // Use appropriate data source based on type
  const dataSource = type === 'countries' ? COUNTRIES : allInterests;

  const handleSelectInterest = (value: string) => {
    if (multiple && setInterests) {
      const newSelected = interests.includes(value)
        ? interests.filter((item) => item !== value)
        : [...interests, value];
      setInterests(newSelected);
    } else if (setInterest) {
      setInterest(value);
      setDropdownVisible(false);
    }
  };

  const handleRemoveInterest = (id: string) => {
    if (multiple && setInterests) {
      setInterests(interests.filter((item) => item !== id));
    } else if (setInterest) {
      setInterest('');
    }
  };

  const selectedInterests = multiple
    ? interests.map((id) => dataSource.find((i) => i._id === id)).filter(Boolean)
    : interest
      ? [dataSource.find((i) => i._id === interest)]
      : [];

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full">
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded-md mb-3 cursor-pointer"></div>
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="h-8 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full">
        <div className="text-red-600 text-sm">
          Error loading interests: {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className=" w-full ">
      {/* Selected Interests */}
      <div className="flex gap-[8px] border border-[#E4E7EC] py-[10px] px-[1rem] rounded-[6px]">
        {selectedInterests.map(
          (item) =>
            item && (
              <InterestButton
                key={item._id}
                item={item}
                onDelete={() => handleRemoveInterest(item._id)}
              />
            )
        )}
      </div>

      <div className="w-full">
        <h1 className={` ${poppinsMedium.className} text-[#4F4F4F] text-[0.875rem] mt-[8px]`}>I am intereested in:</h1>

        {/* Dropdown Trigger */}
        <button
          type="button"
          onClick={() => setDropdownVisible(!dropdownVisible)}
          className="w-full flex-row border border-gray-300 rounded-md px-3 py-3 mt-[8px] flex justify-between items-center"
        >


          <span className={`text-[#98A2B3] text-[14px] ${poppins.className}`}>
            {interest
              ? dataSource.find((item) => item._id === interest)?.name
              : type === 'countries' ? 'Click to select your country' : 'Click to select your interests'}
          </span>
          <ChevronDown size={16} />
        </button>
      </div>
      {/* Dropdown */}
      {dropdownVisible && (
        <div className="border border-[#E4E7EC] rounded-[8px] py-[8px] gap-[4px] mt-[8px] flex max-h-60 overflow-y-auto">
          {dataSource.map((item) => {
            const selected = multiple
              ? interests.includes(item._id)
              : interest === item._id;

            return (
              <button
                key={item._id}
                type="button"
                onClick={() => handleSelectInterest(item._id)}
                className="w-full py-[8px] px-[1rem] flex gap-[12px] items-center hover:bg-gray-50"
              >
                <span
                  className={`w-[20px] h-[20px] border rounded-full flex items-center justify-center ${selected ? 'border-green-600' : 'border-gray-400'
                    }`}
                >
                  {selected && <Check size={14} className="text-green-600" />}
                </span>
                <span className="text-sm">{item.name}</span>
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

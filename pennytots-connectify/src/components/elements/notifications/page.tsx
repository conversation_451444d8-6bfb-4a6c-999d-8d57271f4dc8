'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, MessageCircle, Users, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar } from '@/components/ui/avatar';
import { useGetUnreadNotifications } from '@/redux/main/hooks';
import { main } from '@/api/main';
import { poppinsSemibold, poppinsMedium } from '@/fonts';
import { timeAgo } from '@/lib/time-utils';

interface NotificationItem {
  _id: string;
  type: 'chat' | 'topic' | 'group' | 'quiz';
  title: string;
  message: string;
  createdAt: string;
  read: boolean;
  data?: any;
}

export default function NotificationsPage() {
  const router = useRouter();
  const { data: notifications, refetch } = useGetUnreadNotifications(true);

  useEffect(() => {
    // Clear notifications when user visits the page
    if (notifications?.convos?.length > 0) {
      main.clearUnReadNotifications({ source: "convos" }).then(() => {
        refetch();
      });
    }
  }, [notifications, refetch]);

  const handleBack = () => {
    router.back();
  };

  const handleNotificationClick = (notification: NotificationItem) => {
    // Navigate based on notification type
    switch (notification.type) {
      case 'chat':
        router.push(`/chat/${notification.data?.userId}`);
        break;
      case 'topic':
        router.push(`/topic/${notification.data?.topicId}`);
        break;
      case 'group':
        router.push(`/group/${notification.data?.groupId}`);
        break;
      case 'quiz':
        router.push('/quiz');
        break;
      default:
        break;
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'chat':
        return <MessageCircle className="h-5 w-5 text-blue-500" />;
      case 'topic':
        return <Bell className="h-5 w-5 text-green-500" />;
      case 'group':
        return <Users className="h-5 w-5 text-purple-500" />;
      case 'quiz':
        return <Bell className="h-5 w-5 text-orange-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const mockNotifications: NotificationItem[] = [
    {
      _id: '1',
      type: 'topic',
      title: 'New comment on your post',
      message: 'John Doe commented on your topic about technology trends',
      createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      read: false,
      data: { topicId: '123' }
    },
    {
      _id: '2',
      type: 'chat',
      title: 'New message',
      message: 'Sarah sent you a message',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      read: false,
      data: { userId: '456' }
    },
    {
      _id: '3',
      type: 'group',
      title: 'Group invitation',
      message: 'You were invited to join "Tech Enthusiasts" group',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      read: true,
      data: { groupId: '789' }
    }
  ];

  const allNotifications = notifications?.convos || mockNotifications;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-10">
        <div className="">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className={`${poppinsSemibold.className} text-lg text-gray-900`}>
              Notifications
            </h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl  p-4">
        {allNotifications.length > 0 ? (
          <div className="space-y-3">
            {allNotifications.map((notification: any) => (
              <Card 
                key={notification._id} 
                className={`cursor-pointer hover:shadow-md transition-shadow ${
                  !notification.read ? 'bg-blue-50 border-blue-200' : 'bg-white'
                }`}
                onClick={() => handleNotificationClick(notification)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className={`${poppinsSemibold.className} text-[14px] text-[#5A5E5C] mb-1`}>
                        {notification.title}
                      </h3>
                      <p className={`${poppinsMedium.className} text-[13px] text-[#696969] mb-2`}>
                        {notification.message}
                      </p>
                      <p className={`${poppinsMedium.className} text-[12px] text-[#999999]`}>
                        {timeAgo(notification.createdAt)}
                      </p>
                    </div>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"></div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Bell className="h-12 w-12 text-gray-400  mb-4" />
            <h3 className={`${poppinsSemibold.className} text-lg text-gray-600 mb-2`}>
              No notifications yet
            </h3>
            <p className={`${poppinsMedium.className} text-gray-500`}>
              You'll see notifications here when you have new activity
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

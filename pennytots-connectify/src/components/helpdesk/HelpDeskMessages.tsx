'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Send, ImageIcon, X, Paperclip } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import Avatar from '@/components/ui/avatar';
import { useGetHelpdeskMessages, useReplyHelpDeskMessage } from '@/redux/helpdesk/hooks';
import { poppinsSemibold, poppinsRegular, poppinsMedium, poppinsItalic } from '@/fonts';
import { useSelector } from 'react-redux';
import { userAuthInfo } from '@/redux/user/reducer';
import { formatDistanceToNow } from 'date-fns';

import SendIcon from '@/assets/svg/reply-send.svg';
import Image from 'next/image';
import EmojiPicker from '@/components/ui/emoji-picker';

import SelectAttachment from '@/components/ui/select-attachment';
import { queryClient } from '@/redux/user/hooks';

interface Message {
  _id: string;
  message: string;
  senderId: string;
  createdAt: string;
  image?: string;
}

interface Helpdesk {
  _id: string;
  title: string;
  category: string;
  status: string;
}

interface HelpDeskMessagesProps {
  helpdeskId: string;
  helpdesk: Helpdesk;
  onBack?: () => void;
}

const HelpDeskMessages: React.FC<HelpDeskMessagesProps> = ({
  helpdeskId,
  helpdesk,
  onBack
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { data: helpdeskMessages, isLoading: messagesLoading } = useGetHelpdeskMessages(helpdeskId);
const {
  mutateAsync: replyMessage,
  isSuccess,
  isError,
 
  status,
  error,
  data,
  reset
} = useReplyHelpDeskMessage();

  const currentUser = useSelector(userAuthInfo);
  const [content, setContent] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);


  useEffect(() => {
    console.log(content, "content")
  }, [content]);
useEffect(() => {
  if (isSuccess) {
    console.log('✅ Message sent successfully!');
    // You can trigger animations, reset form, etc.
  }

  if (isError) {
    console.error('❌ Failed to send message:', error);
    // Show error modal/toast
  }
}, [isSuccess, isError]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
  if (e.key === 'Enter') {
    e.preventDefault(); // prevent default action like newline
    handleSubmit(e); // separate function since event is different
  }
};


  useEffect(() => {
    scrollToBottom();
  }, [helpdeskMessages]);

  const timeAgo = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault()
    if (!content.trim() && !selectedImage) return;
   console.log('seeing is belieiving', content, selectedImage)
    const formData = new FormData();
    formData.append('message', content);

    if (selectedImage) {
      formData.append('file', selectedImage);
    }

    const payload = {
      helpdeskId: helpdeskId,
      data: formData,
    };
    console.log(payload, 'backend')

    try {
      await replyMessage(payload);
      // Query invalidation is handled in the hook's onSuccess callback
      setContent('');
      setSelectedImage(null);
    } catch (error) {
      console.error('Error sending reply:', error);
    }
  };

  const handleImageClick = (imageUrl: string) => {
    window.open(imageUrl, '_blank');
  };

  const handleAttachmentSelect = (file: File) => {
    setSelectedImage(file);
  };


  if (messagesLoading) {
    return (
      <div className="flex flex items-center justify-center bg-white">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className={`${poppinsRegular.className} text-gray-600`}>Loading messages...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex flex-col bg-white" style={{ height: '41.99vw' }}>
      {/* Header */}
      <div className="flex items-center px-[1rem] rounded-tr-lg rounded-br-lg  py-[12px] bg-[#E9E9E9]">


        <div>
          <h3 className={`${poppinsMedium.className} text-[#696969] text-[0.875rem]`}>
            {helpdesk.title}
          </h3>


        </div>
      </div>

      {/* Messages Container */}
      <div className="flex flex-col overflow-y-auto w-full pl-[2.625rem]">
        {helpdeskMessages && helpdeskMessages.length > 0 ? (
          <>
            {helpdeskMessages.map((message: Message) => (
              <div key={message._id} className="flex flex-col mt-[2.0625rem]">
                <div className="flex items-center justify-between">
                  <span className={`${poppinsSemibold.className} text-[#D0C69F] text-[0.875rem]`}>
                    {message.senderId !== currentUser._id
                      ? 'Pennytots Admin'
                      : `Me`}
                  </span>
                 
                </div>

                <div className={`${poppinsMedium.className} text-[#5A5E5C] text-[12px]`}>
                  {message.message}
                </div>
                 <span className={`${poppinsItalic.className} text-[#696969] text-[12px]`}>
                    {timeAgo(message.createdAt)}
                  </span>

                {message.image && (
                  <div className="mt-2">
                    <img
                      src={message.image}
                      alt="Message attachment"
                      onClick={() => handleImageClick(message.image!)}
                      className="max-w-sm w-full h-auto rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                    />
                  </div>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className={`${poppinsRegular.className} text-gray-500`}>
              No messages yet. Start the conversation!
            </p>
          </div>
        )}
      </div>
 
      {/* Reply Section */}
      <div className=" w-full items-center justify-center  flex pl-[2.825rem] mt-auto">
             <div className=''> 

   
         {selectedImage && (
                <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Paperclip className="h-4 w-4 text-blue-600" />
                      <span className={`${poppinsRegular.className} text-sm text-blue-800`}>
                        {selectedImage.name}
                      </span>
                      <span className={`${poppinsRegular.className} text-xs text-blue-600`}>
                        ({(selectedImage.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedImage(null)}
                      className="p-1 text-blue-600 hover:text-blue-800"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
      
   </div>
        <form
          onSubmit={handleSubmit}
          className="flex items-center w-full"
        >

          <div className="relative w-full">
            <input
              type="text"
              placeholder='Message..'
              value={content}
              onKeyDown={handleKeyPress}
              onChange={(e) => setContent(e.target.value)}
              className="border w-full border-[#F2F2F2] bg-[#FBFAF7] rounded-lg p-[12px] focus:outline-none"
            />
            <div className="absolute top-2 right-5">
              <SelectAttachment onFileSelect={handleAttachmentSelect} />

            </div>
          </div>
           <div>



          <Button
            type='submit'
            disabled={ (!content.trim() && !selectedImage)}
            className="p-[12px] ml-[1rem] flex items-center text-white gap-[8px] rounded-[10px] bg-[#163E23] "
          >
            Reply
            <SendIcon width={14} height={14} />
          </Button>
        </div>
        </form>
       
      </div>



    </div>
  );
};

export default HelpDeskMessages;

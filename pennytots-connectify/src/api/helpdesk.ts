import { HelpDeskDTO } from "@/redux/helpdesk/types";
import { Axios } from "./axios";



const getHelpDeskTickets = async () => {
  const { data } = await Axios.get('/helpdesk/get-tickets');
  console.log(data, "helpdesk-data")
  console.log(data.tickets.docs, "actual-tickets-array")
  if (data.tickets.docs && data.tickets.docs.length > 0) {
    console.log("First ticket structure:", data.tickets.docs[0]);
  }
  return data.tickets.docs;
};

const getHelpDeskCategories = async () => {
  const { data } = await Axios.get('/helpdesk/categories');
  return data.helpdeskCategories;
};

const createTicket = async (payload: any) => {
  const { data } = await Axios.post('/helpdesk/create-ticket', payload);
  return data;
};


const getHelpDeskMessages = async (helpdeskId: string) => {
  console.log("Fetching messages for helpdeskId:", helpdeskId);
  const { data } = await Axios.get('/helpdesk/get-messages/' + helpdeskId);
  console.log(data, "helpdesk-messages-response")
  console.log("Messages array:", data.messages?.docs);
  return data.messages.docs;
};


const replyHelpDeskMessage = async (payload: HelpDeskDTO) => {
  const { data } = await Axios.post(
    '/helpdesk/reply-message/' + payload.helpdeskId,
    payload.data,{
      headers: {
        'Content-Type': 'multipart/form-data',
      }}
  );
  return data;
};



export const tickets = {
  getHelpDeskTickets,
  getHelpDeskCategories,
  createTicket,
  getHelpDeskMessages,
 replyHelpDeskMessage,
};

import { Axios } from './axios';

// Credit API functions
const getCredit = async () => {
  const { data } = await Axios.get('/credit/get-credit');
  return data.credit;
};

const getSubscription = async () => {
  const { data } = await Axios.get('/subscription/get-subscription');
  return data;
};

const getFreeCredit = async () => {
  const { data } = await Axios.get('/credit/get-free-credit');
  return data;
};

const getFreeSubscription = async () => {
  const { data } = await Axios.get('/subscription/get-free-subscription');
  return data;
};

const buyCredit = async (payload: any) => {
  const { data } = await Axios.post('/credit/buy', payload);
  return data;
};

const buySubscription = async (payload: any) => {
  const { data } = await Axios.post('/subscription/buy', payload);
  return data;
};

const restoreSubscription = async (payload: any) => {
  const { data } = await Axios.post('/subscription/restore-purchases', payload);
  return data;
};

const getTransactionHistory = async () => {
  const { data } = await Axios.get('/credit/transaction-history');
  return data.transactions?.docs || [];
};

// Subscription plans configuration
export const SUBSCRIPTION_PLANS = {
  diamond: {
    id: 'diamond',
    name: 'Diamond Sponsorship',
    description: 'Diamond - 30-day public recognition and 30-day uninterrupted access',
    price: 99.99,
    duration: 30,
    recognition: 30,
    features: ['30-day public recognition', '30-day uninterrupted access', 'Premium support']
  },
  gold: {
    id: 'gold',
    name: 'Gold Sponsorship',
    description: 'Gold - 20-day public recognition and 30-day uninterrupted access',
    price: 79.99,
    duration: 30,
    recognition: 20,
    features: ['20-day public recognition', '30-day uninterrupted access', 'Priority support']
  },
  silver: {
    id: 'silver',
    name: 'Silver Sponsorship',
    description: 'Silver - 10-day public recognition and 30-day uninterrupted access',
    price: 49.99,
    duration: 30,
    recognition: 10,
    features: ['10-day public recognition', '30-day uninterrupted access', 'Standard support']
  },
  bronze: {
    id: 'bronze',
    name: 'Bronze Sponsorship',
    description: 'Bronze - 5-day public recognition and 30-day uninterrupted access',
    price: 29.99,
    duration: 30,
    recognition: 5,
    features: ['5-day public recognition', '30-day uninterrupted access', 'Basic support']
  }
};

export const credit = {
  buyCredit,
  getCredit,
  getFreeCredit,
  getTransactionHistory,
  getSubscription,
  buySubscription,
  getFreeSubscription,
  restoreSubscription
};


import { ICreateCommentDTO, ICreateSubCommentDTO } from '@/redux/topic/types';
import { Axios } from './axios';


const getTopics = async () => {
  try {
    const { data } = await Axios.get('/topics/getTopics?page=1&limit=50');
    console.log(data, 'topic-data')
    return data.topics.docs;
  } catch (error) {
    console.error('Error fetching topics:', error);
    throw error;
  }
};

const deleteTopic = async (topicId: any)=>{
  try{
    const response = await Axios.delete(`/topics/delete/${topicId}`)
  } catch(error){
    throw error;
  }
}
const deleteComment = async (commentId: any)=>{
  try{
    const response = await Axios.delete(`/topics/deleteComment/${commentId}`)
  } catch(error){
    
    throw error;
  }
}

const deleteSubComment = async (subCommentId: any) => {
  try {
    const response = await Axios.delete(`/topics/deleteSubComment/${subCommentId}`);
    return response
  } catch (error: any) {
    // Log the full error object
  
  }
};



const fetchTopic = async (userDetails: any) => {

  try {
    const response = await Axios.get(`/topics/get-user-topics/${userDetails._id}?page=1&limit=50`)
      

  

    if (response.data && response.data.topics && response.data.topics.docs) {
    const getTopic = response.data.topics.docs;
    
      return getTopic;
    } else {
      return [];
    }
  } catch (error) {
    throw error;
  }
};

//Convos
const getConvos = async () => {
  const { data } = await Axios.get('/topics/get-convos?page=1&limit=20');
  return data.topics.docs;
};

const removeFromConvos = async (topicId: string) => {
  const { data } = await Axios.delete(
    '/topics/remove-topic-from-convos/' + topicId,
  );
  return data;
};

const getSingleTopic = async (topicId: string) => {
  const { data } = await Axios.get('/topics/viewTopic/' + topicId);
  return data.topic;
};

const editTopic = async (topicId: string, payload: any) => {
  const { data } = await Axios.post('/topics/edit-topic/' + topicId, payload);
  return data;
};

const getTopicComments = async (topicId: string) => {
  const { data } = await Axios.get(
    '/topics/view-topic-comments/' + topicId + '?page=1&limit=10',
  );
  return data.comments.docs; 
};

const getTopicSubComments = async (commentId: string) => {
  const { data } = await Axios.get(
    '/topics/view-topic-sub-comments/' + commentId + '?page=1&limit=10',
  );
   return data.comments

 
};

const likeTopic = async (topicId: string) => {
  const { data } = await Axios.post('/topics/likeTopic/' + topicId);
  return data;
};

const createComment = async (payload: ICreateCommentDTO) => {
  try {
    const { data } = await Axios.post(
      '/topics/create-comment/' + payload.postId,
      payload.data,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      },
    );
    return data;
  } catch (error: any) {
    const errorMessage =
      error?.data?.message || 'An error occurred while creating the comment';
    throw new Error(errorMessage);
  }
};

// Create SubComment
const createSubComment = async (payload: ICreateSubCommentDTO) => {
  try {
    const { data } = await Axios.post(
      '/topics/create-topic-sub-comment/' + payload.commentId,
      payload.data,
    );
    return data;
  } catch (error: any) {
    const errorMessage =
      error?.data?.message || 'An error occurred while creating the sub-comment';
    throw new Error(errorMessage);
  }
};

const reportTopic = async (topicId: string,description: string) =>{
  const { data } = await Axios.post('/topics/flag/' + topicId, {description});
  return data;
}

const reportSubComments = async (commentId: string,description: string) =>{
  const { data } = await Axios.post('/topics/flag-comment/' + commentId, {description});

  return data;
}


// Function to fetch topics based on searchInput and searchType
const search = async ({ search, searchType }: { search: string; searchType: string }) => {
  const response = await Axios.post('/app/search?page=1&limit=50', {
    search: search,
    searchType,
  });
  return response.data;
};


const createTopic = async (topicData: FormData) => {
  try {
    const { data } = await Axios.post('/topics/create', topicData, {
      
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
     console.log(data, 'createTopic')
    return data;
   
  } catch (error: any) {
    console.error('Error creating topic:', error);
    // Re-throw the original error to preserve the response structure
    throw error;
  }
};

export const topic = {
  getTopics,
  getConvos,
  removeFromConvos,
  getSingleTopic,
  editTopic,
  getTopicComments,
  getTopicSubComments,
  likeTopic,
  createComment,
  createSubComment,
  reportTopic,
  reportSubComments,
  search,
  fetchTopic,
  deleteSubComment,
  deleteTopic,
  deleteComment,
  createTopic
};

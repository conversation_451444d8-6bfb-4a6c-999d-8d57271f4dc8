import { Axios } from './axios';

const getSponsoredAd = async () => {
  try {
    // Try the sponsored ads endpoint first
    const { data } = await Axios.get('/sponsored/ads/display');
    console.log(data, "Sponsored ads response")
    return data;
  } catch (error: any) {
    console.log('Primary sponsored ads endpoint not available, trying fallback...');

    try {
      // Try alternative endpoint (if any exists)
      const { data } = await Axios.get('/app/sponsored-ads');
      console.log(data, "Fallback sponsored ads response")
      return data;
    } catch (fallbackError: any) {
      console.log('Fallback endpoint also not available, using default ad');

      // Return a rotating set of default ads to simulate dynamic content
      const defaultAds = [
        {
          _id: 'default-ad-1',
          title: 'Discover Amazing Deals',
          description: 'Check out our latest offers and promotions. Don\'t miss out on exclusive deals!',
          image: 'https://placehold.co/400x200/F58634/FFF?text=Sponsored+Deal',
          link: '/sponsor',
          company: 'PennyTots',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'default-ad-2',
          title: 'Join Our Community',
          description: 'Connect with like-minded people and share your experiences!',
          image: 'https://placehold.co/400x200/4A90E2/FFF?text=Join+Community',
          link: '/sponsor',
          company: 'PennyTots',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'default-ad-3',
          title: 'Premium Features',
          description: 'Unlock premium features and enhance your experience with us!',
          image: 'https://placehold.co/400x200/7ED321/FFF?text=Premium+Features',
          link: '/sponsor',
          company: 'PennyTots',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      // Rotate ads based on current time to simulate dynamic content
      const adIndex = Math.floor(Date.now() / (1000 * 60 * 30)) % defaultAds.length; // Change every 30 minutes
      const selectedAd = defaultAds[adIndex];

      console.log(`Using default ad ${adIndex + 1}:`, selectedAd.title);
      return { ad: selectedAd };
    }
  }
};

export const sponsoredAd = {
  getSponsoredAd,
};

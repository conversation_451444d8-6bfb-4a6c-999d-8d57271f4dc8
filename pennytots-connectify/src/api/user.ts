import { Axios } from './axios';

const login = async (data: any) => {
  console.log(data)
  return await Axios.post('/user/login', data);
};

const register = async (data: any) => {
  return await Axios.post('/user/register', data);
};

const profile = async () => {
  const { data } = await Axios.get('/user/view-profile');
  return data;
};

const sendPhoneVerification = async (payload: any) => {
  const { data } = await Axios.post(
    '/user/send-verify-phone-number-token',
    payload,
  );

  return data;
};

const validatePhoneNumber = async (payload: any) => {
  const { data } = await Axios.post('/user/verify-phone-number-token', payload);

  return data;
};

const sendResetPasswordToken = async (payload: any) => {
  const { data } = await Axios.post('/user/send-verify-email-token', payload);

  return data;
};

const reportUser = async (userId: string,description: string) =>{
  const { data } = await Axios.patch('/user/report/' + userId, {description});

  return data;
}

const deactivateAccount = async () =>{
  const { data } = await Axios.post('/user/deactivate-account/' );
  return data;
}

const deleteAccount = async () =>{
  const { data } = await Axios.post('/user/delete-account/' );
  return data;
}

export const logUserError = async (payload : any) =>{
  const { data } = await Axios.post('/log/error-log/', payload);
  return data;
}

export const logCrashReport = async (payload : any) => {
  const { data } = await Axios.post('/crashReport/crash-report', payload);
  return data;
};

interface ChangePinParams {
  currentPIN: string;
  newPIN: string;
}

const changeUserPin = async ({ currentPIN, newPIN }: ChangePinParams) => {
  const response = await Axios.patch('/user/reset-password', {
    password: currentPIN,
    newPassword: newPIN,
  });
  return response.data;
};

export const user = {
  login,
  register,
  profile,
  sendPhoneVerification,
  validatePhoneNumber,
  sendResetPasswordToken,
  reportUser,
  deactivateAccount,
  deleteAccount,
  changeUserPin
};

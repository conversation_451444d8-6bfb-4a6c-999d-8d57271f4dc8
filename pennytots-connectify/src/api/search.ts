import { Axios } from './axios';

export interface SearchParams {
  search: string;
  searchType: 'users' | 'chats' | 'groups' | 'topics' | 'topic-comments' | 'topic-sub-comments';
  page?: number;
  limit?: number;
}

export interface SearchUser {
  _id: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
  bio?: string;
  company?: string;
  company_position?: string;
  subscriptionType?: string;
}

export interface SearchChat {
  _id: string;
  user: SearchUser;
  lastMessage?: {
    content: string;
    createdAt: string;
  };
}

export interface SearchGroup {
  _id: string;
  name: string;
  description?: string;
  members_count?: number;
  profile_picture?: string;
  isPrivate?: boolean;
}

export interface SearchTopic {
  _id: string;
  title: string;
  content: string;
  author: SearchUser;
  userId: SearchUser;
  created_at: string;
  createdAt: string;
  likes_count?: number;
  comments_count?: number;
  likes?: number;
  isLiked?: boolean;
  images?: string[];
}

export interface SearchComment {
  _id: string;
  content: string;
  author: SearchUser;
  userId: SearchUser;
  topic_id: string;
  topicId: string;
  created_at: string;
  createdAt: string;
  likes_count?: number;
  isLiked?: boolean;
}

export interface SearchResults {
  users?: {
    docs: SearchUser[];
    totalDocs: number;
    totalPages: number;
    page: number;
    limit: number;
  };
  chats?: {
    docs: SearchChat[];
    totalDocs: number;
    totalPages: number;
    page: number;
    limit: number;
  };
  groups?: {
    docs: SearchGroup[];
    totalDocs: number;
    totalPages: number;
    page: number;
    limit: number;
  };
  topics?: {
    docs: SearchTopic[];
    totalDocs: number;
    totalPages: number;
    page: number;
    limit: number;
  };
  topicComments?: {
    docs: SearchComment[];
    totalDocs: number;
    totalPages: number;
    page: number;
    limit: number;
  };
}

// Main search function that matches the React Native implementation
const performSearch = async (params: SearchParams): Promise<SearchResults> => {
  const { search, searchType, page = 1, limit = 50 } = params;

  try {
    const response = await Axios({
      method: 'POST',
      url: `/app/search?page=${page}&limit=${limit}`,
      data: {
        search: search.trim(),
        searchType: searchType,
      },
    });

    return response.data;
  } catch (error) {
    console.error('Search API error:', error);
    throw error;
  }
};

// Specific search functions for each type
const searchUsers = async (query: string, page = 1, limit = 50): Promise<SearchResults['users']> => {
  const result = await performSearch({
    search: query,
    searchType: 'users',
    page,
    limit
  });
  return result.users;
};

const searchChats = async (query: string, page = 1, limit = 50): Promise<SearchResults['chats']> => {
  const result = await performSearch({
    search: query,
    searchType: 'chats',
    page,
    limit
  });
  return result.chats;
};

const searchGroups = async (query: string, page = 1, limit = 50): Promise<SearchResults['groups']> => {
  const result = await performSearch({
    search: query,
    searchType: 'groups',
    page,
    limit
  });
  return result.groups;
};

const searchTopics = async (query: string, page = 1, limit = 50): Promise<SearchResults['topics']> => {
  const result = await performSearch({
    search: query,
    searchType: 'topics',
    page,
    limit
  });
  return result.topics;
};

const searchComments = async (query: string, page = 1, limit = 50): Promise<SearchResults['topicComments']> => {
  const result = await performSearch({
    search: query,
    searchType: 'topic-comments',
    page,
    limit
  });
  return result.topicComments;
};

// Combined search function that searches all types
const searchAll = async (query: string, page = 1, limit = 10): Promise<{
  users: SearchUser[];
  chats: SearchChat[];
  groups: SearchGroup[];
  topics: SearchTopic[];
  comments: SearchComment[];
  totalResults: number;
}> => {
  try {
    const [usersResult, chatsResult, groupsResult, topicsResult, commentsResult] = await Promise.allSettled([
      searchUsers(query, page, limit),
      searchChats(query, page, limit),
      searchGroups(query, page, limit),
      searchTopics(query, page, limit),
      searchComments(query, page, limit)
    ]);

    const users = usersResult.status === 'fulfilled' ? usersResult.value?.docs || [] : [];
    const chats = chatsResult.status === 'fulfilled' ? chatsResult.value?.docs || [] : [];
    const groups = groupsResult.status === 'fulfilled' ? groupsResult.value?.docs || [] : [];
    const topics = topicsResult.status === 'fulfilled' ? topicsResult.value?.docs || [] : [];
    const comments = commentsResult.status === 'fulfilled' ? commentsResult.value?.docs || [] : [];

    const totalResults = users.length + chats.length + groups.length + topics.length + comments.length;

    return {
      users,
      chats,
      groups,
      topics,
      comments,
      totalResults
    };
  } catch (error) {
    console.error('Search all error:', error);
    return {
      users: [],
      chats: [],
      groups: [],
      topics: [],
      comments: [],
      totalResults: 0
    };
  }
};

// Chat-specific search within a conversation
const searchChatMessages = async (chatId: string, query: string): Promise<any[]> => {
  try {
    const response = await Axios.post(`/chats/search/${chatId}`, {
      search: query.trim()
    });
    return response.data.docs || [];
  } catch (error) {
    console.error('Chat search error:', error);
    throw error;
  }
};

export const search = {
  performSearch,
  searchUsers,
  searchChats,
  searchGroups,
  searchTopics,
  searchComments,
  searchAll,
  searchChatMessages
};

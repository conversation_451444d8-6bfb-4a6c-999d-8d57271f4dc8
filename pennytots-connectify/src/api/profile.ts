import { Axios } from "./axios";

interface ProfileUpdateData {
  first_name?: string;
  last_name?: string;
  bio?: string;
  website?: string;
  facebook?: string;
  linkedin?: string;
  company?: string;
  company_position?: string;
  city?: string;
  state?: string;
  age?: number | string; // Allow both number and string for age ranges
  gender?: string;
  [key: string]: any;
}

const updateProfile = async (formData: ProfileUpdateData) => {
  try {
    console.log('Updating profile with data:', formData);
    const { data } = await Axios.patch("/user/update-profile", formData);
    console.log('Profile update response:', data);
    return data;
  } catch (error: any) {
    console.error('Profile update failed:', error);
    // Try alternative endpoint
    try {
      const { data } = await Axios.post("/user/update-profile", formData);
      return data;
    } catch (altError) {
      console.error('Alternative profile update failed:', altError);
      throw error;
    }
  }
};

const uploadProfilePicture = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('file', file); // Use 'file' as the key, not 'profile_picture'
    console.log('Uploading profile picture:', file.name);

    const { data } = await Axios.patch("/user/update-profile-picture", formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Profile picture upload response:', data);
    return data;
  } catch (error: any) {
    console.error('Profile picture upload failed:', error);
    console.error('Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    throw error;
  }
};

const uploadHeaderImage = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('file', file); // Use 'file' as the key, not 'header_image'
    console.log('Uploading header image:', file.name);

    const { data } = await Axios.patch("/user/update-header-image", formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Header image upload response:', data);
    return data;
  } catch (error: any) {
    console.error('Header image upload failed:', error);
    console.error('Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    throw error;
  }
};

const getProfileById = async (userId: string) => {
  const { data } = await Axios.get(`/user/profile/${userId}`);
  return data;
};

const updateProfileWithImages = async (
  profileData: ProfileUpdateData,
  profilePicture?: File,
  headerImage?: File
) => {
  try {
    console.log('Starting profile update with images...', {
      profileData,
      hasProfilePicture: !!profilePicture,
      hasHeaderImage: !!headerImage
    });

    const results: any = {};

    // 1. Update profile data first (if any)
    if (Object.keys(profileData).length > 0) {
      console.log('Updating profile data...');
      results.profile = await updateProfile(profileData);
    }

    // 2. Upload profile picture if provided
    if (profilePicture) {
      console.log('Uploading profile picture...');
      results.profilePicture = await uploadProfilePicture(profilePicture);
    }

    // 3. Upload header image if provided
    if (headerImage) {
      console.log('Uploading header image...');
      results.headerImage = await uploadHeaderImage(headerImage);
    }

    console.log('All updates successful:', results);
    return results;
  } catch (error: any) {
    console.error('Profile update failed:', error);
    console.error('Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });

    // Provide more specific error messages
    if (error.response?.status === 404) {
      throw new Error('Profile update endpoint not found. Please contact support.');
    } else if (error.response?.status === 413) {
      throw new Error('File size too large. Please use smaller images.');
    } else if (error.response?.status === 400) {
      throw new Error(error.response?.data?.message || 'Invalid data provided.');
    } else if (!error.response) {
      throw new Error('Network error. Please check your internet connection.');
    }

    throw error;
  }
};

export const profile = {
  updateProfile,
  uploadProfilePicture,
  uploadHeaderImage,
  getProfileById,
  updateProfileWithImages,
};

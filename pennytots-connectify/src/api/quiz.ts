import { Axios } from "./axios";

// Quiz/Trivia API functions
const getQuestionsOld = async () => {
  const { data } = await Axios.get('/quiz/random');
  return data;
};

const getQuestions = async (country: any) => {
  const { data } = await Axios.get(`/quiz/random/${country}`);
  console.log(country, "datasstr");
  return data;
};

// PennyTots management
const changeUserPennyTots = async (type: "reduce" | "increase") => {
  const REDUCE_PENNYTOTS = 10; // You can move this to constants
  const INCREASE_PENNYTOTS = 10; // You can move this to constants

  if (type === 'reduce') {
    const { data } = await Axios.get(`/quiz/reduce/${REDUCE_PENNYTOTS}`);
    return data;
  }
  if (type === 'increase') {
    const { data } = await Axios.get(`/quiz/increase/${INCREASE_PENNYTOTS}`);
    return data;
  }
};

const stakeUserPennyTots = async (type: "reduce" | "increase", amount: number) => {
  if (type === 'reduce') {
    const { data } = await Axios.get(`/quiz/reduce/${amount}`);
    return data;
  }
  if (type === 'increase') {
    const newAmount: number = amount;
    const { data } = await Axios.get(`/quiz/increase/${newAmount}`);
    return data;
  }
};

// Search functionality
const search = async({ queryKey}: any ) => {
    const [_, search] = queryKey;
    if (search.search.length < 2) {
        return '[]';
    }

    const { data } = await Axios.post("/app/search?page=1&limit=50", search);
    return data;
}

// Notification functions
const muteNotifications = async(payload: any) => {
    console.log("notification", payload)
    const { data } = await Axios.post("/app/mute-notifications", payload);
    console.log("mute notification", payload)
    return data;
}

const unreadNotifications = async() => {
    const { data } = await Axios.get("/app/unread-notifications");
    return data?.notifications;
}

const clearUnReadNotifications = async(payload: any) => {
    const { data } = await Axios.get(`/app/clear-unread-notifications/${payload.source}`);
    return data;
}

export const quiz = {
  getQuestions,
  getQuestionsOld,
  changeUserPennyTots,
  stakeUserPennyTots,
  search,
  muteNotifications,
  unreadNotifications,
  clearUnReadNotifications
};
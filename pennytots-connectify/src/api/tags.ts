import { Axios } from './axios';

interface ICreateTopicResponse {
  topic: {
    _id: string;
    content: string;
    author: any;
    tags: any[];
    createdAt: string;
    updatedAt: string;
  };
}

const getTags = async () => {
  const { data } = await Axios.get('/tags/list');
  return data;
};

const setUserTags = async (interests: string[]) => {
  const { data } = await Axios.post('/tags/set-user-tags', interests);
  return data;
};

const createTopic = async (content: string, tags: string[]) => {
  const topicData = new FormData();
  topicData.append('content', content);
  topicData.append('tags', JSON.stringify(tags));
  console.log(topicData, 'topicData')

  const { data } = await Axios.post<ICreateTopicResponse>('/topics/create', topicData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data;
};

export const tags = {
  getTags,
  setUserTags,
  createTopic
};

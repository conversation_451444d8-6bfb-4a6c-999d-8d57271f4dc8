import { Axios } from './axios';

const blockUser = async (accountId: string) => {
  const { data } = await Axios.patch(`/chats/block/${accountId}`, {});
  return data;
};

const unblockUser = async (accountId: string) => {
  const { data } = await Axios.patch(`/chats/unblock/${accountId}`, {});
  return data;
};

// Get regular/unblocked chats
const getChats = async () => {
  const { data } = await Axios.get(`/chats/my-chats`);

  // Check if data has the expected structure
  if (data && data.chats && data.chats.docs) {
    return data.chats.docs;
  } else if (data && Array.isArray(data)) {
    return data;
  } else {
    return data;
  }
};

// Get blocked chats
const getBlockedChats = async () => {
  const { data } = await Axios.get(`/chats/my-chats/?blocked=true`);

  // Check if data has the expected structure
  if (data && data.chats && data.chats.docs) {
    return data.chats.docs;
  } else if (data && Array.isArray(data)) {
    return data;
  } else {
    return data;
  }
};

const muteNotification = async (payload: {
  contentId: string;
  type: "user";
  action: "mute" | "unmute";
}) => {
  try {
    const response = await Axios.post("/app/mute-notifications/", payload);
    return response.data;
  } catch (error) {
    console.error("Mute notification error:", error);
    throw error;
  }
};

const getSearchChats = async (chatId: string, search: string) => {
  const { data } = await Axios.post(`/chats/search/${chatId}`, { search });
  return data;
};

const getUserChats = async ({ userId, pageNumber, limit }: any) => {
  const { data } = await Axios.get(
    `/chats/${userId}?page=${pageNumber}&limit=${limit}`
  );
  return data;
};

const getBotChats = async ({ pageNumber, limit }: any) => {
  const { data } = await Axios.get(
    `/chats/bot-chats?page=${pageNumber}&limit=${limit}`
  );
  return data;
};

// Get or create a chat with a specific user
const getOrCreateUserChat = async (userId: string) => {
  try {
    // First try to get existing chat using the same pattern as getUserChats
    const { data } = await Axios.get(`/chats/${userId}?page=1&limit=1`);

    // If we get chat data, return the first chat or create a new structure
    if (data && data.chats && data.chats.docs && data.chats.docs.length > 0) {
      const existingChat = data.chats.docs[0];

      // Ensure the accountId has both field formats for compatibility
      if (existingChat.accountId) {
        existingChat.accountId = {
          ...existingChat.accountId,
          firstName: existingChat.accountId.firstName || existingChat.accountId.first_name || '',
          lastName: existingChat.accountId.lastName || existingChat.accountId.last_name || '',
          first_name: existingChat.accountId.first_name || existingChat.accountId.firstName || '',
          last_name: existingChat.accountId.last_name || existingChat.accountId.lastName || '',
          profilePicture: existingChat.accountId.profilePicture || existingChat.accountId.profile_picture || '',
          profile_picture: existingChat.accountId.profile_picture || existingChat.accountId.profilePicture || '',
        };
      }

      return existingChat;
    } else {
      // No existing chat found, create a temporary structure
      // The actual chat will be created when the first message is sent
      return {
        _id: `temp-${userId}-${Date.now()}`,
        accountId: {
          _id: userId,
          firstName: '',
          lastName: '',
          first_name: '',
          last_name: '',
          profilePicture: '',
          profile_picture: '',
        },
        messages: [],
        isNewChat: true,
        unreadCount: 0,
        muted: false,
        blocked: false,
      };
    }
  } catch (error: any) {
    console.log('No existing chat found, creating temporary structure');
    // Create a temporary chat structure for new chats
    return {
      _id: `temp-${userId}-${Date.now()}`,
      accountId: {
        _id: userId,
        firstName: '',
        lastName: '',
        first_name: '',
        last_name: '',
        profilePicture: '',
        profile_picture: '',
      },
      messages: [],
      isNewChat: true,
      unreadCount: 0,
      muted: false,
      blocked: false,
    };
  }
};

export const chat = {
  getUserChats,
  getSearchChats,
  getBotChats,
  getChats,
  getBlockedChats,
  unblockUser,
  blockUser,
  muteNotification,
  getOrCreateUserChat,
};

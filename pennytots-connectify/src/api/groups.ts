import { Axios } from './axios';

// Get user's groups
const getMyGroups = async () => {
  try {
    const { data } = await Axios.get('/groups/my-groups');
    console.log('getMyGroups data:', data);
    
    // Check if data has the expected structure
    if (data && data.groups && data.groups.docs) {
      return data.groups.docs;
    } else if (data && Array.isArray(data)) {
      return data;
    } else {
      return data;
    }
  } catch (error) {
    console.error('Error fetching groups:', error);
    throw error;
  }
};

// Get group chat messages
const getGroupChats = async (groupId: string, pageNumber: number = 1, limit: number = 50) => {
  try {
    const { data } = await Axios.get(`/groups/chats/${groupId}?page=${pageNumber}&limit=${limit}`);
    console.log('getGroupChats data:', data);
    return data;
  } catch (error) {
    console.error('Error fetching group chats:', error);
    throw error;
  }
};

// Get groups for a specific user
const getUserGroups = async (userId: string) => {
  try {
    const { data } = await Axios.get(`/groups/user-groups/${userId}`);
    console.log('getUserGroups data:', data);
    return data;
  } catch (error) {
    console.error('Error fetching user groups:', error);
    throw error;
  }
};

// Send group message (via socket - this is a placeholder)
const sendGroupMessage = async (messageData: {
  groupId: string;
  message: string;
  type?: string;
  quotedData?: any;
}) => {
  try {
    console.log('sendGroupMessage: Sending group message via API:', messageData);
    
    // This is a placeholder - group messages are typically sent via socket
    // In a real implementation, you might have a POST /groups/send-message endpoint
    
    return {
      success: true,
      message: 'Group message sent via API fallback',
      data: {
        _id: `group-msg-${Date.now()}`,
        message: messageData.message,
        createdAt: new Date(),
        type: messageData.type || 'message'
      }
    };
  } catch (error) {
    console.error('sendGroupMessage: Error sending group message via API:', error);
    throw error;
  }
};

export const groups = {
  getMyGroups,
  getGroupChats,
  getUserGroups,
  sendGroupMessage,
};

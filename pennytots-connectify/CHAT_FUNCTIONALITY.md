# Enhanced Chat Functionality

This document describes the comprehensive chat functionality that has been integrated into the pennytot-connectify-web application.

## Overview

The enhanced chat system provides a complete messaging solution with the following key features:

- **Profile-to-Chat Navigation**: Click any profile name or avatar to start a chat
- **File Attachments**: Support for images, videos, audio, and documents
- **Real-time Messaging**: WebSocket-based real-time communication
- **Search & Filtering**: Advanced search across chats and messages
- **Accessibility**: Full keyboard navigation and screen reader support
- **Error Handling**: Robust error boundaries and retry mechanisms
- **Toast Notifications**: User-friendly notifications for all actions

## Components

### Core Components

#### 1. ProfileClickHandler
**Location**: `src/components/profile/ProfileClickHandler.tsx`

A versatile component that handles profile name/avatar clicks and opens private chats.

```tsx
<ProfileClickHandler
  user={userObject}
  defaultAction="chat" // or "profile"
  showAvatar={true}
  showDropdown={false}
  onChatStart={(chatData) => console.log('Chat started')}
  onProfileView={(userId) => console.log('Profile viewed')}
>
  {children}
</ProfileClickHandler>
```

**Features**:
- Configurable click actions (chat, profile, or custom)
- Optional dropdown menu with multiple actions
- Loading states and error handling
- Accessibility support

#### 2. Enhanced MessageInput
**Location**: `src/components/chat/MessageInput.tsx`

Advanced message input with file attachment support.

```tsx
<MessageInput
  onSendMessage={handleSendMessage}
  chatId={chatId}
  onUploadStart={(file, type) => console.log('Upload started')}
  onUploadProgress={(progress) => console.log('Progress:', progress)}
  onUploadComplete={(result) => console.log('Upload complete')}
  onUploadError={(error) => console.log('Upload error')}
/>
```

**Features**:
- File attachment support (images, videos, audio, documents)
- Upload progress tracking
- File size validation
- Drag & drop support
- Emoji picker integration

#### 3. ChatSearch
**Location**: `src/components/chat/ChatSearch.tsx`

Advanced search component with filtering capabilities.

```tsx
<ChatSearch
  onSearch={(query, filters) => performSearch(query, filters)}
  results={searchResults}
  onResultClick={(result) => navigateToResult(result)}
  showFilters={true}
/>
```

**Features**:
- Real-time search with debouncing
- Advanced filtering options
- Search result highlighting
- Keyboard navigation

### Utility Components

#### 4. Loading States
**Location**: `src/components/chat/ChatLoadingStates.tsx`

Collection of loading and status components:
- `ChatListSkeleton`: Loading skeleton for chat lists
- `MessageSkeleton`: Loading skeleton for messages
- `ConnectionStatus`: Real-time connection status indicator
- `UploadProgress`: File upload progress indicator
- `TypingIndicator`: Shows when users are typing

#### 5. Error Boundaries
**Location**: `src/components/chat/ChatErrorBoundary.tsx`

Robust error handling components:
- `ChatErrorBoundary`: General chat error boundary
- `ChatRoomErrorBoundary`: Specific to chat rooms
- `ChatListErrorBoundary`: Specific to chat lists

## Hooks

### 1. useChat
**Location**: `src/hooks/useChat.ts`

Comprehensive chat management hook.

```tsx
const {
  // State
  isConnected,
  isLoading,
  error,
  activeChat,
  messages,
  typingUsers,
  
  // Actions
  createChat,
  navigateToChat,
  sendMessage,
  sendMessageWithAttachment,
  startTyping,
  stopTyping
} = useChat({
  autoConnect: true,
  enableTypingIndicators: true,
  enableNotifications: true,
  enableToasts: true
});
```

### 2. useChatKeyboardShortcuts
**Location**: `src/hooks/useChatKeyboardShortcuts.ts`

Keyboard shortcuts management for chat functionality.

```tsx
const {
  shortcuts,
  registerShortcut,
  unregisterShortcut,
  showHelpDialog,
  setShowHelpDialog
} = useChatKeyboardShortcuts({
  inputRef: messageInputRef,
  enableGlobalShortcuts: true
});
```

### 3. useChatSearch
**Location**: `src/hooks/useChatSearch.ts`

Search functionality hook.

```tsx
const {
  query,
  results,
  isLoading,
  handleSearch,
  clearSearch
} = useChatSearch({
  searchInMessages: true,
  maxResults: 20
});
```

## Services

### 1. Attachment Upload Service
**Location**: `src/services/attachment-upload.ts`

Handles file uploads with progress tracking.

```tsx
import attachmentUploadService from '@/services/attachment-upload';

// Upload an image
const result = await attachmentUploadService.uploadImage({
  chatId: 'chat-id',
  file: imageFile,
  message: 'Check this out!',
  onProgress: (progress) => console.log(progress.percentage + '%'),
  onSuccess: (result) => console.log('Upload successful'),
  onError: (error) => console.error('Upload failed:', error)
});
```

### 2. Toast Service
**Location**: `src/services/toast.ts`

Centralized notification system.

```tsx
import toastService from '@/services/toast';

// Show success toast
toastService.chatCreated('John Doe');

// Show error toast
toastService.messageSendFailed('Network error');

// Custom toast
toastService.success('Success', 'Operation completed');
```

## Providers

### ChatProvider
**Location**: `src/providers/ChatProvider.tsx`

React context provider for global chat state management.

```tsx
<ChatProvider options={{ enableToasts: true }}>
  <App />
</ChatProvider>
```

**Available Hooks**:
- `useChatContext()`: Full chat context
- `useChatConnection()`: Connection management
- `useChatMessages()`: Message operations
- `useChatNavigation()`: Chat navigation
- `useQuickChat()`: Quick chat initiation

## Integration Guide

### 1. Basic Setup

1. Wrap your app with the ChatProvider:
```tsx
import ChatProvider from '@/providers/ChatProvider';

function App() {
  return (
    <ChatProvider>
      <YourApp />
    </ChatProvider>
  );
}
```

2. Initialize toast notifications:
```tsx
import { useProfileNotifications } from '@/components/profile/ProfileNotifications';
import toastService from '@/services/toast';

function Layout() {
  const { addNotification } = useProfileNotifications();
  
  useEffect(() => {
    toastService.setToastHandler(addNotification);
  }, []);
  
  return <YourLayout />;
}
```

### 2. Making Profile Names Clickable

Replace existing profile links with ProfileClickHandler:

```tsx
// Before
<span onClick={() => navigateToProfile(user.id)}>
  {user.name}
</span>

// After
<ProfileClickHandler
  user={user}
  defaultAction="chat"
  onChatStart={(chatData) => console.log('Chat started')}
>
  {user.name}
</ProfileClickHandler>
```

### 3. Enhanced Message Input

Replace existing message inputs:

```tsx
// Before
<input 
  value={message}
  onChange={(e) => setMessage(e.target.value)}
  onKeyPress={handleKeyPress}
/>

// After
<MessageInput
  onSendMessage={handleSendMessage}
  chatId={currentChatId}
  onUploadComplete={(result) => {
    // Handle successful upload
  }}
/>
```

## Keyboard Shortcuts

- `?` - Show keyboard shortcuts help
- `/` - Focus message input
- `Esc` - Cancel/close dialogs
- `Ctrl + ↑/↓` - Navigate messages
- `Ctrl + R` - Reply to selected message
- `Enter` - Send message
- `Shift + Enter` - New line in message

## Accessibility Features

- **Screen Reader Support**: All components have proper ARIA labels
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Proper focus handling for modals and navigation
- **Live Regions**: Real-time announcements for new messages
- **High Contrast**: Support for high contrast themes

## Demo

Visit `/chat-demo` to see all features in action with interactive examples.

## API Integration

The chat functionality integrates with your existing API endpoints:

- `POST /chats/upload-image-attachment/:chatId`
- `POST /chats/upload-video-attachment/:chatId`
- `POST /chats/upload-audio-attachment/:chatId`
- `POST /chats/upload-document-attachment/:chatId`
- `GET /chats/search` (if implemented)

## Troubleshooting

### Common Issues

1. **Toast notifications not showing**: Ensure `toastService.setToastHandler()` is called
2. **File uploads failing**: Check file size limits and API endpoints
3. **Socket connection issues**: Verify WebSocket configuration
4. **Profile clicks not working**: Ensure user objects have required fields (`_id`, `first_name`, `last_name`)

### Debug Mode

Enable debug logging:
```tsx
const chatHook = useChat({
  enableToasts: true,
  // Add debug flag if available
});
```

## Contributing

When adding new chat features:

1. Follow the existing component patterns
2. Add proper TypeScript types
3. Include accessibility features
4. Add error handling
5. Update this documentation

const {
  userModel,
  flaggedUsersModel,
  CreditModel,
} = require('../models/index');
const sendEmail = require('../utils/sendEmail');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const QRCode = require('qrcode');
const speakeasy = require('speakeasy');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const register = async (req, res) => {
  try {
    const checkUserPhoneNumber = await userModel.findOne({
      'phone_number.number': req.body.phone_number.number,
      'phone_number.code': req.body.phone_number.code,
    });

    const checkEmail = await userModel.findOne({
      email: req.body.email,
    });

    if (checkUserPhoneNumber) {
      throw 'Phone Number already exist';
    }

    if (checkEmail) {
      throw 'Email already exist';
    }

    let user = new userModel(req.body);

    user
      .save()
      .then(async (user) => {
        let token = jwt.sign({ id: user._id }, process.env.TOKEN_SECRET, {
          expiresIn: process.env.TOKEN_LIFE,
        });
        user = JSON.parse(JSON.stringify(user));

        await userModel.updateOne(
          { _id: user._id },
          { $set: { last_seen: new Date() } },
          { new: true },
        );

        await CreditModel.create({
          userId: user._id,
        });

        res.json({
          id: user._id,
          ...user,
          message: 'Registration Successful',
          token: {
            token,
          },
        });
      })
      .catch((error) => {
        throw error;
      });
  } catch (error) {
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

const login = async (req, res) => {
  let phone_number = req.body.phone_number;
  let password = req.body.password;

  try {
    await userModel.findOne({ phone_number }).then((user) => {
      
      if (user) {
        
        user.comparePassword(password, async function (err, result) {
          if (err) {
            res.json({
              message: err,
            });
            return;
          }
          if (result) {
            let token = jwt.sign({ id: user._id }, process.env.TOKEN_SECRET, {
              expiresIn: process.env.TOKEN_LIFE,
            });

            user = JSON.parse(JSON.stringify(user));

            if (user.suspended) {
              res.status(403).json({ message: 'Your account is suspended' });
              return;
            }

            await userModel.updateOne(
              { _id: user._id },
              { $set: { last_seen: new Date() } },
              { new: true },
            );

            res.json({
              id: user._id,
              ...user,
              message: 'Login Successful!',
              token: {
                token,
              },
            });
          } else {
            res.status(403).json({ message: 'Invalid Password' });
          }
        });
      } else {
        res.status(404).json({
          message: 'User does not exist',
        });
      }
    });
  } catch (err) {
    console.log(err);
    res.status(403).json({ message: 'Something went wrong' });
  }
};

const deactivateAccount = async (req, res) => {
  try {
    // Extract the user ID from the request (assuming it's sent in the request body)
    const userId = req.user.id;

    // Find the user by ID
    const user = await userModel.findById(userId);

    // If user not found, return an error
    if (!user) {
      return res.status(404).json({
        message: 'User not found',
      });
    }

    // Update the user to set the deactivated flag
    await userModel.updateOne(
      { _id: userId },
      { $set: { deactivated: true } },
      { new: true },
    );

    // Respond with a success message
    res.json({
      message: 'Account successfully deactivated',
    });
  } catch (error) {
    // Handle any errors that occur
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

const deleteAccount = async (req, res) => {
  try {
    // Extract the user ID from the request (assuming it's sent in the request body)
    const userId = req.user.id;

    // Find the user by ID
    const user = await userModel.findById(userId);

    // If user not found, return an error
    if (!user) {
      return res.status(404).json({
        message: 'User not found',
      });
    }

    // Delete the user
    await userModel.deleteOne({ _id: userId });

    // Respond with a success message
    res.json({
      message: 'Account successfully deleted',
    });
  } catch (error) {
    // Handle any errors that occur
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

const updateProfilePicture = async (req, res) => {
  let imageFolder = process.env.PROFILE_DIRECTORY + '/';

  let user = await userModel.findById(req.user.id);

  if (user.profile_picture) {
    bucket.file(imageFolder + user.profile_picture).delete();
  }

  let fileName =
    new Date().getTime() + user._id + path.extname(req.file.originalname);

  const blob = bucket.file(imageFolder + req.file.originalname);
  fs.createReadStream(req.file.path)
    .pipe(blob.createWriteStream())
    .on('error', (err) => {
      console.log(err);
      // res.status(400).json({ message: err });
      next(err);
    })
    .on('finish', async () => {
      try {
        await blob.rename(imageFolder + fileName);
        await bucket.file(imageFolder + fileName).makePublic();

        const update = await userModel.findOneAndUpdate(
          { _id: req.user.id },
          {
            $set: {
              profile_picture: fileName,
            },
          },
          { new: true },
        );

        res.status(200).json({
          message: 'Profile Picture Updated Successfully',
          user: update,
        });
      } catch (error) {
        res.status(400).json({ message: 'An Error Occurred', error });
      }
    });

  return;
};

const updateHeaderImage = async (req, res) => {
  let imageFolder = process.env.HEADER_IMAGE_DIRECTORY + '/';

  let user = await userModel.findById(req.user.id);

  if (user.header_image) {
    bucket.file(imageFolder + user.header_image).delete();
  }

  let fileName =
    new Date().getTime() + user._id + path.extname(req.file.originalname);

  const blob = bucket.file(imageFolder + req.file.originalname);
  fs.createReadStream(req.file.path)
    .pipe(blob.createWriteStream())
    .on('error', (err) => {
      console.log(err);
      // res.status(400).json({ message: err });
      next(err);
    })
    .on('finish', async () => {
      try {
        await blob.rename(imageFolder + fileName);
        await bucket.file(imageFolder + fileName).makePublic();

        const update = await userModel.findOneAndUpdate(
          { _id: req.user.id },
          {
            $set: {
              header_image: fileName,
            },
          },
          { new: true },
        );

        res.status(200).json({
          message: 'Header Image Updated Successfully',
          user: update,
        });
      } catch (error) {
        res.status(400).json({ message: 'An Error Occurred', error });
      }
    });

  return;
};

const resetPassword = async (req, res) => {
  try {
    req.userAccount.comparePassword(
      req.body.password,
      async function (err, result) {
        if (err) {
          console.log('122', err);
          res.json({
            message: err,
          });
          return;
        }
        if (result) {
          const hash = await bcrypt.hash(
            req.body.newPassword,
            Number(process.env.BCRYPT_SALT),
          );

          await userModel.updateOne(
            { _id: req.user.id },
            { $set: { password: hash } },
            { new: true },
          );

          sendEmail(
            req.userAccount.email,
            'Password Updated',
            {
              name: `${req.userAccount.first_name} ${req.userAccount.last_name}`,
              pin: req.body.newPassword,
            },
            './template/resetPassword.handlebars',
          );

          res.status(200).json({ message: 'Password has been updated' });
        } else {
          res.status(400).json({
            message: 'You entered an incorrect password. Try again',
          });
          return;
        }
      },
    );
  } catch (error) {
    res.status(400).json({
      message: error,
    });
  }
};

const updateProfile = async (req, res) => {
  try {
    const data = await userModel.findOneAndUpdate(
      { _id: req.user.id },
      {
        $set: req.body,
      },
      { new: true },
    );

    res.status(200).json({
      data,
      message: 'Profile updated successfully',
    });
  } catch (error) {
    console.log(error);
    res
      .status(404)
      .json({ error, message: 'An error occurred cant update profile' });
  }
};

const getAccount = async (req, res) => {
  try {
    let user = await userModel.findById(req.params.userId);
    user = JSON.parse(JSON.stringify(user));
    res.status(200).json({
      ...user,
      message: 'record found',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const reportUser = async (req, res) => {
  try {
    const flag = new flaggedUsersModel({
      reporterId: req.user.id,
      userId: req.params.accountId,
    });

    await flag.save();

    let countFlag = await flaggedUsersModel
      .find({ userId: req.params.accountId })
      .count();

    let flagLimit = process.env.USER_FLAG_LIMIT;

    if (countFlag >= flagLimit) {
      await userModel.updateOne(
        { _id: req.params.accountId },
        {
          $set: {
            suspended: true,
            reportedAndFlagged: true,
          },
        },
      );
    }

    res.status(200).json({
      message: 'User has been reported, thank you',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const viewProfile = async (req, res) => {
  try {
    let user = JSON.parse(JSON.stringify(req.userAccount));
    res.status(200).json({
      ...user,
      message: 'Success',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const searchAccount = async (req, res) => {
  try {
    // const user = await userModel.find({
    //   $or: [{ name: req.body.name }, { username: req.body.name }],
    // });

    const user = await userModel.find({
      $or: [{ name: /rest/ }, { username: /tes/ }],
    });

    console.log(user.length);
    if (user.length !== 0) {
      res.status(200).json({
        data: user,
        message: 'record found',
      });
    } else {
      res.status(404).json({
        message: 'No Result Found',
      });
    }
  } catch (error) {
    res.json({ message: error });
  }
};

const deviceToken = (req, res) => {
  const token = req.headers.authorization.split(' ')[1];
  const decode = jwt.verify(token, process.env.TOKEN_SECRET);

  let userId = decode?.id;
  let device_token = req.body.device_token;

  if (!device_token) {
    return res.status(401).json({
      message: 'a device token is required',
    });
  }

  userModel
    .findById(userId)
    .then(async (user) => {
      if (!user) {
        return res.status(404).json({
          message: 'User does not exist',
        });
      }

      if (user?.device_token === device_token) {
        return res.status(409).json({
          message: `Device token ${device_token} ${user?.device_token} already exist`,
        });
      }

      // remove token from previous accounts
      await userModel.updateMany(
        { device_token },
        { $unset: { device_token: 1 } },
      );

      await userModel.updateOne(
        { _id: userId },
        { $set: { device_token: device_token } },
        { new: true },
      );

      res.status(200).json({
        message: 'device token updated successfully',
      });
    })
    .catch((err) => {
      res.status(400).json({
        message: 'invalid request, something went wrong',
        error: err,
      });
    });
};

module.exports = {
  register,
  login,
  resetPassword,
  updateProfile,
  updateProfilePicture,
  updateHeaderImage,
  getAccount,
  viewProfile,
  reportUser,
  searchAccount,
  deviceToken,
  deactivateAccount,
  deleteAccount
};

const {
  userModel,
  groupsModel,
  groupMessagesModel,
  tagsModel,
  flaggedGroupsModel,
  mutedNotificationsModel,
} = require('../models/index');
const path = require('path');
const fs = require('fs');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const listGroups = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    sort: { createdAt: -1 },
  };

  let groups = await groupsModel.paginate({}, option);
  groups = JSON.parse(JSON.stringify(groups));

  return res.status(200).json({ groups });
};

const myGroups = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    sort: { createdAt: -1 },
  };

  let groups = await groupsModel.paginate(
    { participants: req.user.id, appropriate: true },
    option,
  );

  groups = JSON.parse(JSON.stringify(groups));

  for (i in groups.docs) {
    let muted = await mutedNotificationsModel.findOne({
      userId: req.user.id,
      contentId: groups.docs[i]._id,
      contentType: 'group',
    });

    if (muted) {
      groups.docs[i].muted = true;
    } else {
      groups.docs[i].muted = false;
    }

    let lastMessage = await groupMessagesModel
      .findOne({ groupId: groups.docs[i]._id })
      .sort({ createdAt: -1 });
    if (lastMessage) {
      if (lastMessage.message.length >= 30) {
        groups.docs[i].lastMessage =
          lastMessage.message.substring(0, 30) + '...';
      } else {
        groups.docs[i].lastMessage = lastMessage.message;
        groups.docs[i].lastMessageDate = lastMessage.createdAt;
      }
    } else {
      groups.docs[i].lastMessage = '';
    }
  }

  return res.status(200).json({ groups, message: 'Loaded successfully!!' });
};

const userGroups = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    sort: { createdAt: -1 },
  };

  let groups = await groupsModel.paginate(
    { participants: req.params.userId, appropriate: true },
    option,
  );

  groups = JSON.parse(JSON.stringify(groups));

  for (i in groups.docs) {
    let lastMessage = await groupMessagesModel
      .findOne({ groupId: groups.docs[i]._id })
      .sort({ createdAt: -1 });
    if (lastMessage) {
      if (lastMessage.message.length >= 30) {
        groups.docs[i].lastMessage =
          lastMessage.message.substring(0, 30) + '...';
      } else {
        groups.docs[i].lastMessage = lastMessage.message;
        groups.docs[i].lastMessageDate = lastMessage.createdAt;
      }
    } else {
      groups.docs[i].lastMessage = '';
    }
  }

  return res.status(200).json({ groups });
};

const suggestedGroups = async (req, res) => {
  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { createdAt: -1 },
    };

    let groups = await groupsModel.paginate(
      { tag: { $in: req.userAccount.interests }, appropriate: true },
      option,
    );

    groups = JSON.parse(JSON.stringify(groups));

    for (i in groups.docs) {
      let lastMessage = await groupMessagesModel
        .findOne({ groupId: groups.docs[i]._id })
        .sort({ createdAt: -1 });

      let user = await userModel.findById(groups.docs[i].createdBy);

      user = JSON.parse(JSON.stringify(user));

      if (user) {
        groups.docs[i].createdBy = {
          profile_picture: user.profile_picture,
          id: user._id,
          first_name: user.first_name,
          last_name: user.last_name,
        };
      }

      groups.docs[i].membersCount = groups.docs[i].participants.length;

      if (lastMessage) {
        if (lastMessage.message.length >= 30) {
          groups.docs[i].lastMessage =
            lastMessage.message.substring(0, 30) + '...';
        } else {
          groups.docs[i].lastMessage = lastMessage.message;
          groups.docs[i].lastMessageDate = lastMessage.createdAt;
        }
      } else {
        groups.docs[i].lastMessage = '';
      }
    }

    res.status(200).json({ groups });
  } catch (error) {
    res.status(400).json({ error });
  }
};

const createGroup = async (req, res) => {
  let groupHeaderFolder = process.env.GROUPS_HEADER_DIRECTORY + '/';

  const group = new groupsModel({
    name: req.body.name,
    admins: [req.user.id],
    participants: [req.user.id],
    tag: req.body.tag,
    description: req.body.description,
    createdBy: req.user.id,
  });

  try {
    const groupData = await group.save();

    if (groupData) {
      if (req.file) {
        let fileName =
          new Date().getTime() +
          groupData._id +
          path.extname(req.file.originalname);
        const blob = bucket.file(groupHeaderFolder + req.file.originalname);
        fs.createReadStream(req.file.path)
          .pipe(blob.createWriteStream())
          .on('error', (err) => {
            console.log(err);
            //  res.status(400).json({ message: err });
            next(err);
          })
          .on('finish', async () => {
            try {
              await blob.rename(groupHeaderFolder + fileName);
              await bucket.file(groupHeaderFolder + fileName).makePublic();

              const update = await groupsModel.findOneAndUpdate(
                { _id: groupData.id },
                {
                  $set: {
                    image: fileName,
                  },
                },
                { new: true },
              );

              let data = await groupMessagesModel.create({
                groupId: groupData.id,
                message: `${req.userAccount.first_name}  ${req.userAccount.last_name} created the group ${req.body.name}`,
                system: true,
              });
              res.status(200).json({
                message: 'Group Created Successfully',
                group: update,
              });
            } catch (error) {
              console.log(error);
              res
                .status(400)
                .json({ message: 'An Error Occurred', data: error });
            }
          });
      } else {
        let data = await groupMessagesModel.create({
          groupId: groupData.id,
          message: `${req.userAccount.first_name}  ${req.userAccount.last_name} created the group ${req.body.name}`,
          system: true,
        });

        res.status(200).json({
          message: 'Group Created Successfully',
          group: groupData,
        });
      }
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};

const getGroupChats = async (req, res) => {
  try {
    let getGroup = await groupsModel.findById(req.params.groupId);

    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { createdAt: -1 },
      populate: [
        {
          path: 'senderId',
          select: 'first_name last_name profile_picture',
          model: userModel,
        },
      ],
    };

    if (getGroup) {
      let chats = await groupMessagesModel.paginate(
        {
          groupId: req.params.groupId,
        },
        option,
      );
      chats = JSON.parse(JSON.stringify(chats));
      chats.groupId = req.params.groupId;

      // for (i in chats.docs) {
      //   if (chats.docs[i].senderId === req.user.id) {
      //     chats.docs[i].receiverId = req.params.accountId;
      //   } else if (chats.docs[i].senderId === req.user.accountId) {
      //     chats.docs[i].receiverId = req.params.accountId;
      //   }
      // }

      return res.status(200).json({ groupChats: chats });
    } else {
      return res
        .status(400)
        .json({ message: 'Group does not exist or have been deleted' });
    }
  } catch (err) {
    console.log(err);
    return res.status(400).json({ err });
  }
};

const getGroup = async (req, res) => {
  try {
    let group = await groupsModel.findById(req.params.groupId);

    if (!group) {
      res.status(400).json({ message: 'Cant find group' });
      return;
    }

    group = JSON.parse(JSON.stringify(group));

    let admins = await userModel.find(
      { _id: group.admins },
      {
        first_name: 1,
        last_name: 1,
        profile_picture: 1,
      },
    );

    let participants = await userModel.find(
      { _id: group.participants },
      {
        first_name: 1,
        last_name: 1,
        profile_picture: 1,
      },
    );

    let createdBy = await userModel.findById(group.createdBy, {
      first_name: 1,
      last_name: 1,
      profile_picture: 1,
    });

    let tag = await tagsModel.findById(group.tag, { name: 1 });

    admins = JSON.parse(JSON.stringify(admins));
    participants = JSON.parse(JSON.stringify(participants));

    for (i in admins) {
      admins[i].admin = true;
    }

    let ids = new Set(admins.map((d) => d._id));
    let merged = [...admins, ...participants.filter((d) => !ids.has(d._id))];

    group.participants = merged;
    group.createdBy = createdBy;
    group.tag = tag;
    delete group.admins;

    res.status(200).json(group);
  } catch (error) {
    console.log(error);
    res.status(200).json({
      error,
    });
  }
};

const updateGroup = async (req, res) => {
  try {
    let data = {};

    if (req.body.description == '') {
      data.description = '';
    } else if (req.body.description) {
      data.description = req.body.description;
    }

    if (req.body.name) {
      data.name = req.body.name;
    }

    if (req.body.tag) {
      data.tag = req.body.tag;
    }

    await groupsModel.updateOne(
      { _id: req.groupDetails._id },
      {
        $set: data,
      },
      { new: true },
    );

    res.status(200).json({
      message: 'Group updated successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: 'Something went wrong' });
  }
};

const updateGroupPicture = async (req, res) => {
  let imageFolder = process.env.GROUPS_HEADER_DIRECTORY + '/';
  let group = req.groupDetails;

  if (group.image) {
    bucket.file(imageFolder + group.image).delete();
  }

  let fileName =
    new Date().getTime() +
    group._id +
    req.user.id +
    path.extname(req.file.originalname);

  const blob = bucket.file(imageFolder + req.file.originalname);

  fs.createReadStream(req.file.path)
    .pipe(blob.createWriteStream())
    .on('error', (err) => {
      console.log(err);
      // res.status(400).json({ message: err });
      next(err);
    })
    .on('finish', async () => {
      try {
        await blob.rename(imageFolder + fileName);
        await bucket.file(imageFolder + fileName).makePublic();

        const update = await groupsModel.findOneAndUpdate(
          { _id: group._id },
          {
            $set: {
              image: fileName,
            },
          },
          { new: true },
        );

        res.status(200).json({
          message: 'Group Picture Updated Successfully',
          group: update,
        });
      } catch (error) {
        res.status(400).json({ message: 'An Error Occurred', error });
      }
    });

  return;
};

const makeGroupAdmin = async (req, res) => {
  try {
    let checkUser = await groupsModel.findOne({
      $and: [{ _id: req.params.groupId }, { admins: req.params.userId }],
    });

    if (checkUser) {
      res
        .status(400)
        .json({ message: 'User is already an admin of the group' });
      return;
    }

    await groupsModel.updateOne(
      { _id: req.params.groupId },
      { $push: { admins: req.params.userId } },
      { new: true },
    );

    let user = await userModel.findById(req.params.userId);

    let data = await groupMessagesModel.create({
      groupId: req.params.groupId,
      message: `${req.userAccount.first_name}  ${req.userAccount.last_name} made ${user.first_name}  ${user.last_name} group admin`,
      system: true,
    });

    var io = req.app.get('socketio');

    io.in('group-' + req.params.groupId).emit('group-messages', data);

    res.status(200).json({
      message: `${user.first_name} ${user.last_name} has been made admin successfully`,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};
const removeFromGroup = async (req, res) => {
  try {
    let checkUser = await groupsModel.findOne({
      $and: [{ _id: req.params.groupId }, { participants: req.params.userId }],
    });

    if (!checkUser) {
      res
        .status(400)
        .json({ message: 'User is not an a participant of the group' });
      return;
    }

    await groupsModel.updateOne(
      { _id: req.params.groupId },
      { $pull: { admins: req.params.userId, participants: req.params.userId } },
      { new: true },
    );

    let user = await userModel.findById(req.params.userId);

    let data = await groupMessagesModel.create({
      groupId: req.params.groupId,
      message: `${req.userAccount.first_name}  ${req.userAccount.last_name} removed ${user.first_name}  ${user.last_name} from the group`,
      system: true,
    });

    var io = req.app.get('socketio');

    io.in('group-' + req.params.groupId).emit('group-messages', data);

    res.status(200).json({
      message: `${user.first_name} ${user.last_name} has been removed successfully`,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};
const removeGroupAdmin = async (req, res) => {
  try {
    let checkUser = await groupsModel.findOne({
      $and: [{ _id: req.params.groupId }, { admins: req.params.userId }],
    });

    if (!checkUser) {
      res.status(400).json({ message: 'User is not an admin of the group' });
      return;
    }

    await groupsModel.updateOne(
      { _id: req.params.groupId },
      { $pull: { admins: req.params.userId } },
      { new: true },
    );

    let user = await userModel.findById(req.params.userId);

    let data = await groupMessagesModel.create({
      groupId: req.params.groupId,
      message: `${req.userAccount.first_name}  ${req.userAccount.last_name} removed ${user.first_name}  ${user.last_name} as group admin`,
      system: true,
    });

    var io = req.app.get('socketio');

    io.in('group-' + req.params.groupId).emit('group-messages', data);

    res.status(200).json({
      message: `${user.first_name} ${user.last_name} has been removed as admin successfully`,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};

const leaveGroup = async (req, res) => {
  try {
    let group = await groupsModel.findOne({
      $and: [{ _id: req.params.groupId }, { participants: req.user.id }],
    });

    if (!group) {
      res.status(400).json({ message: 'Cant find group' });
      return;
    }

    group = JSON.parse(JSON.stringify(group));

    await groupsModel.updateOne(
      { _id: group._id },
      { $pull: { admins: req.user.id, participants: req.user.id } },
      { new: true },
    );

    let data = await groupMessagesModel.create({
      groupId: req.params.groupId,
      message: `${req.userAccount.first_name}  ${req.userAccount.last_name} left the group`,
      system: true,
    });

    var io = req.app.get('socketio');

    io.in('group-' + req.params.groupId).emit('group-messages', data);

    res.status(200).json({
      message: 'You left the group successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: 'Something went wrong' });
  }
};

const joinGroup = async (req, res) => {
  try {
    let group = await groupsModel.findOne({
      $and: [{ _id: req.params.groupId }, { participants: req.user.id }],
    });

    if (group) {
      res
        .status(400)
        .json({ message: 'You are already a participant of this group' });
      return;
    }

    await groupsModel.updateOne(
      { _id: req.params.groupId },
      { $push: { participants: req.user.id } },
      { new: true },
    );

    let data = await groupMessagesModel.create({
      groupId: req.params.groupId,
      message: `${req.userAccount.first_name}  ${req.userAccount.last_name} joined the group`,
      system: true,
    });

    var io = req.app.get('socketio');

    io.in('group-' + req.params.groupId).emit('group-messages', data);

    res.status(200).json({
      message: 'You joined the group successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};

const flagGroup = async (req, res) => {
  try {
    const flag = new flaggedGroupsModel({
      reporterId: req.user.id,
      groupId: req.params.groupId,
    });

    await flag.save();

    let countFlag = await flaggedGroupsModel
      .find({ groupId: req.params.groupId })
      .count();

    let flagLimit = process.env.GROUP_FLAG_LIMIT;

    if (countFlag >= flagLimit) {
      const update = await groupsModel.updateOne(
        { _id: req.params.groupId },
        {
          $set: {
            appropriate: false,
          },
        },
      );
    }

    res.status(200).json({
      message:
        'Group has been reported, thank you, you will get a feedback soon',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};
const deleteMessage = async (req, res) => {
  try {
    let message = await groupMessagesModel.findById(req.params.groupMessageId);

    if (!message) {
      res.status(400).json({ message: 'Cant find message' });
      return;
    }

    if (message.senderId != req.user.id) {
      res.status(400).json({ message: 'You cant perform this operation' });
      return;
    }

    if (message.attachment) {
      let attachmentFolder = null;
      if (message.type == 'image') {
        attachmentFolder = process.env.GROUP_IMAGE_DIRECTORY + '/';
      } else if (message.type == 'video') {
        attachmentFolder = process.env.GROUP_VIDEO_DIRECTORY + '/';
      } else if (message.type == 'audio') {
        attachmentFolder = process.env.GROUP_AUDIO_DIRECTORY + '/';
      } else if (message.type == 'document') {
        attachmentFolder = process.env.GROUP_DOCUMENT_DIRECTORY + '/';
      }

      await bucket.file(attachmentFolder + message.attachment).delete();
    }

    let deletedMessage = JSON.parse(JSON.stringify(message));

    await message.deleteOne();
    var io = req.app.get('socketio');

    io.in('group-' + deletedMessage.groupId).emit('group-messages', {
      deletedMessage: true,
      messageId: deletedMessage._id,
    });

    res.status(200).json({ message: 'Deleted successfully' });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};

const searchGroupMessages = async (req, res) => {
  if (!req.body.search) {
    res.status(400).json({ message: 'Search field cannot be empty' });
    return;
  }

  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      populate: [
        {
          path: 'senderId',
          select: 'first_name last_name profile_picture',
          model: userModel,
        },
      ],
    };

    let messages = await groupMessagesModel.paginate(
      {
        $text: { $search: req.body.search },
        groupId: req.params.groupId,
        system: { $exists: false },
      },
      option,
    );

    messages = JSON.parse(JSON.stringify(messages));
    res.status(200).json(messages);
  } catch (error) {
    console.log(error);
  }
};

const searchGroups = async (req, res) => {
  if (!req.body.search) {
    res.status(400).json({ message: 'Search field cannot be empty' });
    return;
  }

  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      // populate: [
      //   {
      //     path: 'senderId',
      //     select: 'first_name last_name profile_picture',
      //     model: userModel,
      //   },
      // ],
    };

    let groups = await groupsModel.paginate(
      {
        $text: { $search: req.body.search },
      },
      option,
    );

    groups = JSON.parse(JSON.stringify(groups));
    res.status(200).json(groups);
  } catch (error) {
    console.log(error);
  }
};

module.exports = {
  getGroupChats,
  searchGroups,
  searchGroupMessages,
  createGroup,
  listGroups,
  suggestedGroups,
  myGroups,
  userGroups,
  joinGroup,
  updateGroup,
  makeGroupAdmin,
  removeGroupAdmin,
  removeFromGroup,
  leaveGroup,
  deleteMessage,
  getGroup,
  flagGroup,
  updateGroupPicture,
};

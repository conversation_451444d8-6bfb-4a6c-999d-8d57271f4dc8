const jwt = require('jsonwebtoken');
const { default: mongoose } = require('mongoose');
const handleChatNotification = require('../middleware/notifications/chatNotification');
const handleGroupNotification = require('../middleware/notifications/groupNotification');
const {
  chatMessagesModel,
  chatsModel,
  groupsModel,
  groupMessagesModel,
  userModel,
} = require('../models');
const { checkCredit, deductCredit } = require('../middleware/credit');

module.exports = function (http, app) {
  let user = {};
  const io = require('socket.io')(http, {
    cors: {
      origin: '*',
      credentials: true,
    },
  });

  app.set('socketio', io);

  io.use((socket, next) => {
    const token = socket.handshake.auth.token;

    try {
      user = jwt.verify(token, process.env.TOKEN_SECRET);

      next();
    } catch (error) {
      next(new Error('invalid'));
    }
  });

  //To listen to messages
  io.on('connection', (socket) => {
    //  console.log('user connected');

    //CHAT APPLICATION
    socket.on('join-chat', async function (chatRoom, callback) {
      let getChat = await chatsModel.findOne({
        participants: { $all: [chatRoom.accountId, user.id] },
      });

      if (getChat) {
        if (getChat.blocked.length > 0) {
          callback({
            success: false,
            message: 'Chat has been blocked',
          });
          return;
        }

        await socket.join(chatRoom.chatId);
        callback({
          success: true,
          message: 'Chat Connected',
        });
        return;
      } else {
        callback({
          success: false,
          message: 'Chat Connection Failed',
        });
        return;
      }
    });

    socket.on('Private Chat', async function (messagePayload, callback) {
      try {
        if (!messagePayload.token) {
          throw 'Token required';
        }

        if (!messagePayload.message) {
          throw 'Message cannot be empty';
        }

        //messageData is message payload
        let sender = jwt.verify(messagePayload.token, process.env.TOKEN_SECRET);

        await checkCredit({
          source: 'PRIVATE_CHAT_TEXT',
          userId: sender.id,
        });

        let getChat = await chatsModel.findOne({
          participants: { $all: [messagePayload.accountId, sender.id] },
        });

        if (getChat) {
          try {
            let createData = {
              chatId: getChat._id,
              message: messagePayload.message,
              senderId: sender.id,
              type: messagePayload.type,
            };

            if (messagePayload.quotedData) {
              createData.quotedReply = messagePayload.quotedData;
            }

            let data = await chatMessagesModel.create(createData);

            if (getChat.activeChat.length != 2) {
              await chatsModel.updateOne(
                { _id: getChat._id },
                {
                  $set: { activeChat: [sender.id, messagePayload.accountId] },
                },
              );
            }

            let participantIndex = 0;
            if (getChat.participants[0] == sender.id) {
              participantIndex = 0;
            } else {
              participantIndex = 1;
            }

            await chatsModel.updateOne(
              { _id: getChat._id },
              {
                $set: {
                  [`lastRead.${participantIndex}`]: data.createdAt,
                },
              },
            );

            await deductCredit({
              source: 'PRIVATE_CHAT_TEXT',
              userId: sender.id,
            });

            // notify the receiver's device
            handleChatNotification(messagePayload, sender);

            // socket.broadcast.emit('received', data);
            io.in(messagePayload.chatId).emit('chat-messages', data);

            callback({
              success: true,
              message: 'Message delivered Successfully',
            });

            return;
          } catch (err) {
            throw err;
          }
        } else {
          throw 'Invalid Request, Something went wrong';
          return;
        }
      } catch (error) {
        callback({
          success: false,
          message: error && error.message ? error.message : error,
        });
        return;
      }
    });

    //GROUPS
    socket.on('join-group', async function (groupId, callback) {
      try {
        // let group = 'group-' + groupId;
        await socket.join('group-' + groupId);
        callback({
          success: true,
          message: `Group Chat Connected`,
        });
        return;
      } catch (err) {
        callback({
          success: false,
          message: 'Group Chat Connection Failed',
        });
        return;
      }
    });

    socket.on('Group Chat', async function (messagePayload, callback) {
      try {
        if (!messagePayload.token) {
          throw 'Token required';
        }
        if (!messagePayload.message) {
          throw 'Message cannot be empty';
        }

        let sender = jwt.verify(messagePayload.token, process.env.TOKEN_SECRET);

        await checkCredit({
          source: 'GROUP_CHAT_TEXT',
          userId: sender.id,
        });

        //messageData is message payload
        let getGroupChat = await groupsModel.findOne({
          $and: [{ _id: messagePayload.groupId }, { participants: sender.id }],
        });

        if (getGroupChat) {
          try {
            let createData = {
              groupId: messagePayload.groupId,
              message: messagePayload.message,
              senderId: sender.id,
              type: messagePayload.type,
            };

            if (messagePayload.quotedData) {
              createData.quotedReply = messagePayload.quotedData;
            }

            let data = await groupMessagesModel.create(createData);

            let userData = await userModel.findById(sender.id);
            data = JSON.parse(JSON.stringify(data));
            userData = JSON.parse(JSON.stringify(userData));

            data.senderId = {
              _id: userData._id,
              first_name: userData.first_name,
              last_name: userData.last_name,
            };
            if (userData.profile_picture) {
              data.senderId.profile_picture = userData.profile_picture;
            }

            await deductCredit({
              source: 'GROUP_CHAT_TEXT',
              userId: sender.id,
            });

            // notify all group member devices
            handleGroupNotification(messagePayload, sender);

            // socket.broadcast.emit('received', data)
            //  console.log('bgr', messagePayload.groupId);
            io.in('group-' + messagePayload.groupId).emit(
              'group-messages',
              data,
            );
            callback({
              success: true,
              message: 'Message delivered Successfully',
            });
            return;
            // socket.emit('received', data);
          } catch (err) {
            throw 'error';
          }
        } else {
          throw 'You are not a participant of the group';
        }
      } catch (error) {
        callback({
          success: false,
          message: error && error.message ? error.message : error,
        });
        return;
      }
    });
  });
};

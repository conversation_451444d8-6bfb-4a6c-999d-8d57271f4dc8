const { TokenModel, userModel, RetriesModel } = require('../models');
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const serviceSid = process.env.TWILIO_SERVICE_SID;
// const client = require('twilio')(accountSid, authToken);
const sendEmail = require('../utils/sendEmail');
const bcrypt = require('bcryptjs');
const axios = require('axios');
const fetch = require('node-fetch');
const retries = require('../constants/retries');
const moment = require('moment');
const { formatTimeToDuration } = require('../common/time');

const generateAndSaveToken = async (type, detail) => {
  let payload = type == 'email' ? { email: detail } : { phone_number: detail };

  const checkToken = await TokenModel.findOne(payload);

  if (checkToken) {
    await checkToken.deleteOne();
  }

  const token = Math.floor(1000 + Math.random() * 9000);

  let data = {
    token,
    createdAt: Date.now(),
  };

  if (type == 'email') {
    data.email = detail;
  } else {
    data.phone_number = detail;
  }

  await new TokenModel(data).save();

  return token;
};

const sendPhoneNumberVerificationToken = async (req, res) => {
  try {
    throw 'OTP Verification has been disabled'
    if (req.userAccount.phone_number_verified) {
      throw 'Phone number has already been verified';
    }

    let checkRetries = await RetriesModel.findOne({
      source: req.body.type,
      userId: req.user.id,
    });

    let secLeft = 0;

    if (checkRetries) {
      const lastUpdated = moment(checkRetries.updatedAt);

      const lastRetrySeconds = moment().diff(lastUpdated, 'seconds');

      secLeft = retries[req.body.type][checkRetries.retries] - lastRetrySeconds;

      //Check  Last retry
      if (lastRetrySeconds < retries[req.body.type][checkRetries.retries]) {
        secLeft =
          retries[req.body.type][checkRetries.retries] - lastRetrySeconds;

        throw `Please retry after ${formatTimeToDuration(
          retries[req.body.type][checkRetries.retries] - lastRetrySeconds,
        )}`;
      }
    }

    let phoneNumber = `${req.userAccount.phone_number.code}${req.userAccount.phone_number.number}`;
    let token = await generateAndSaveToken('phone_number', phoneNumber);

    let data = {
      api_token: process.env.SMS_API_TOKEN,
      to: phoneNumber,
      from: 'Pennytots',
      body: `Your One Time Pass is ${token}`,
      gateway: 8,
    };

    if (req.body.type === 'sms') {
      await fetch(`https://www.bulksmsnigeria.com/api/v2/sms`, {
        method: 'POST',
        body: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json',
        },
      })
        .then((response) => response.json())
        .then(async () => {
          if (checkRetries) {
            await RetriesModel.updateOne(
              {
                source: req.body.type,
                userId: req.user.id,
              },
              {
                $inc: {
                  retries: 1,
                },
              },
            );

            secLeft = retries[req.body.type][checkRetries.retries + 1];
          } else {
            await RetriesModel.create({
              source: req.body.type,
              userId: req.user.id,
            });

            secLeft = retries[req.body.type][0];
          }

          res.status(200).json({
            message: 'An OTP has been sent to your phone number pls verify',
            secLeft,
          });
        })
        .catch((error) => {
          throw 'Something went wrong, cant send SMS OTP, Please try again later';
        });
    }
  } catch (error) {
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

const validatePhoneNumberToken = async (req, res) => {
  try {
    throw "OTP verification has been disabled"
    if (req.userAccount.phone_number_verified) {
      throw 'Phone number has already been verified';
    }

    let phoneNumber = `${req.userAccount.phone_number.code}${req.userAccount.phone_number.number}`;

    const getToken = await TokenModel.findOne({
      phone_number: phoneNumber,
      token: req.body.token,
    });

    if (getToken) {
      await userModel.findOneAndUpdate(
        { _id: req.user.id },
        {
          $set: {
            phone_number_verified: true,
          },
        },
        { new: true },
      );

      await RetriesModel.deleteOne({
        userId: req.user.id,
        source: 'sms',
      });

      res.status(200).json({
        message: 'Your phone number has been verified',
      });
    } else {
      throw 'Token validation failed, Token has either expired or wrong';
    }
  } catch (error) {
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

const sendEmailResetToken = async (req, res) => {
  try {
    const email = req.body.email.toLowerCase();

    const user = await userModel.findOne({ email });

    if (user) {
      const token = await generateAndSaveToken('email', req.body.email);

      let token5 = token.toString() + email;

      let tokenBase64 = Buffer.from(token5).toString('base64');
      let path = process.env.WEB_FRONTEND + '/reset-password/' + tokenBase64;

      sendEmail(
        req.body.email,
        'Reset Password Request',
        {
          name: user.first_name,
          path,
        },
        './template/requestResetPassword.handlebars',
      );

      res.status(200).json({
        message: 'A link to reset your password has been sent to your email',
      });
    } else {
      throw 'Cant find account';
    }
  } catch (error) {
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

const validateEmailToken = async (req, res) => {
  try {
    const email = req.body.email.toLowerCase();

    const getToken = await TokenModel.findOne({
      email,
      token: req.body.token,
    });

    if (getToken) {
      const hash = await bcrypt.hash(
        req.body.password,
        Number(process.env.BCRYPT_SALT),
      );

      await userModel.findOneAndUpdate(
        { email },
        {
          $set: {
            password: hash,
          },
        },
        { new: true },
      );

      res.status(200).json({
        message:
          'Your password has been changed. Please login to the app with your new password',
      });
    } else {
      throw 'Token validation failed, Token has either expired or wrong';
    }
  } catch (error) {
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

// const sendEmailVerificationToken = async (req, res) => {
//   const token = await generateAndSaveToken(req.account.email);

//   SendEmail(
//     req.account.email,
//     'Email Verification',
//     {
//       token,
//       name: req.account.first_name + req.account.last_name,
//     },
//     './templates/email-verification.handlebars',
//   );

//   res.status(200).json({
//     message: 'Email token has been sent successfully. Please check your email',
//   });
// };

// const sendPhoneNumberVerificationTokenOld = async (req, res) => {
//   try {
//     if (req.userAccount.phone_number_verified) {
//       throw 'Phone number has already been verified';
//     }

//     let checkRetries = await RetriesModel.findOne({
//       source: req.body.type,
//       userId: req.user.id,
//     });

//     let secLeft = 0;

//     if (checkRetries) {
//       const lastUpdated = moment(checkRetries.updatedAt);

//       const lastRetrySeconds = moment().diff(lastUpdated, 'seconds');

//       secLeft = retries[req.body.type][checkRetries.retries] - lastRetrySeconds;

//       //Check  Last retry
//       if (lastRetrySeconds < retries[req.body.type][checkRetries.retries]) {
//         secLeft =
//           retries[req.body.type][checkRetries.retries] - lastRetrySeconds;

//         throw `Please retry after ${formatTimeToDuration(
//           retries[req.body.type][checkRetries.retries] - lastRetrySeconds,
//         )}`;
//       }
//     }

//     let phoneNumber = `${req.userAccount.phone_number.code}${req.userAccount.phone_number.number}`;
//     let channel = req.body.type;

//     const data = new URLSearchParams({
//       To: phoneNumber,
//       Channel: channel,
//     });

//     await fetch(
//       `https://verify.twilio.com/v2/Services/${serviceSid}/Verifications`,
//       {
//         method: 'POST',
//         body: data,
//         headers: {
//           'Content-Type': 'application/x-www-form-urlencoded',
//           Authorization:
//             'Basic ' +
//             Buffer.from(accountSid + ':' + authToken).toString('base64'),
//         },
//       },
//     )
//       .then((response) => response.json())
//       .then(async (data) => {
//         if (data.sid) {
//           if (checkRetries) {
//             await RetriesModel.updateOne(
//               {
//                 source: req.body.type,
//                 userId: req.user.id,
//               },
//               {
//                 $inc: {
//                   retries: 1,
//                 },
//               },
//             );

//             secLeft = retries[req.body.type][checkRetries.retries + 1];
//           } else {
//             await RetriesModel.create({
//               source: req.body.type,
//               userId: req.user.id,
//             });

//             secLeft = retries[req.body.type][0];
//           }

//           res.status(200).json({
//             message: 'An OTP has been sent to your phone number pls verify',
//             secLeft,
//           });
//         } else {
//           throw data.message;
//         }
//       })
//       .catch((error) => {
//         throw error;
//       });
//   } catch (error) {
//     res.status(400).json({
//       message: error.message ? error.message : error,
//     });
//   }
// };

// const validatePhoneNumberTokenOld = async (req, res) => {
//   try {
//     if (req.userAccount.phone_number_verified) {
//       throw 'Phone number has already been verified';
//     }

//     let phoneNumber = `${req.userAccount.phone_number.code}${req.userAccount.phone_number.number}`;

//     const data = new URLSearchParams({
//       To: phoneNumber,
//       Code: req.body.token,
//     });

//     await fetch(
//       `https://verify.twilio.com/v2/Services/${serviceSid}/VerificationCheck`,
//       {
//         method: 'POST',
//         body: data,
//         headers: {
//           'Content-Type': 'application/x-www-form-urlencoded',
//           Authorization:
//             'Basic ' +
//             Buffer.from(accountSid + ':' + authToken).toString('base64'),
//         },
//       },
//     )
//       .then((response) => response.json())
//       .then(async (data) => {
//         if (data.valid === false) {
//           throw 'Validation failed, Token does not match';
//         }

//         if (data.status == 'approved') {
//           const user = await userModel.findOneAndUpdate(
//             { _id: req.user.id },
//             {
//               $set: {
//                 phone_number_verified: true,
//               },
//             },
//             { new: true },
//           );

//           await RetriesModel.deleteMany({
//             userId: req.user.id,
//             $or: [{ source: 'sms' }, { source: 'whatsapp' }],
//           });

//           return res.status(200).json({
//             message: 'Your number has been verified',
//             user,
//           });
//         }

//         throw 'Token Validation Failed, Pls Try Resending Token';
//       })
//       .catch((error) => {
//         throw error;
//       });
//   } catch (error) {
//     res.status(400).json({
//       message: error.message ? error.message : error,
//     });
//   }
// };

module.exports = {
  // sendEmailVerificationToken,
  generateAndSaveToken,
  sendPhoneNumberVerificationToken,
  validatePhoneNumberToken,
  sendEmailResetToken,
  validateEmailToken,
};

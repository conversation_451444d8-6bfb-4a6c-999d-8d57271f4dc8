const { MONTHLY_FREE_CREDIT,DAILY_FREE_CREDIT } = require('../constants/credit');
const { CreditModel, TransactionModel } = require('../models');
const FlutterwaveController = require('./flutterwave');
const moment = require('moment');

const getCredit = async (req, res) => {
  try {
    let credit = await CreditModel.findOne({
      userId: req.user.id,
    });

    if (!credit) {
      let newCredit = await CreditModel.create({
        userId: req.user.id,
      });

      res.status(200).json({
        credit: newCredit,
        message: 'Credit Loaded',
      });
    } else {
      res.status(200).json({
        credit,
        message: 'Credit Loaded',
      });
    }
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const getFreeCredit = async (req, res) => {
  try {
    let credit = await CreditModel.findOne({
      userId: req.user.id,
    });

    if (credit) {
      let time = moment(credit.lastFreeCreditDate);

      // Get the current date
      const currentDate = moment();

      // Calculate the difference in days
      const daysDiff = currentDate.diff(time, 'days');

      if (daysDiff > 1) {
        await CreditModel.updateOne(
          { userId: req.user.id },
          {
            $inc: { amount: DAILY_FREE_CREDIT },
            $set: {
              lastFreeCreditDate: new Date(),
            },
          },
        );

        return res.status(200).json({
          message: "Congratulations! You've been awarded Free Pennytots",
        });
      } else {
        throw "Looks like you've already recieved your free Pennytots for today! You can always grab more on the credits page";
      }
    } else {
      throw 'Cant find credit account';
    }
  } catch (error) {
    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

const buyCredit = async (req, res) => {
  try {
    if (req.body.amount < 100) {
      throw 'The minimum amount is N100';
    }

    const transaction = await TransactionModel.create({
      userId: req.user.id,
      source: 'flutterwave',
      action: 'buy-credit',
      amount: req.body.amount,
    });

    if (transaction) {
      const paymentLink = await FlutterwaveController.generatePaymentLink({
        req,
        transactionId: transaction._id,
        amount: req.body.amount,
      });

      return res.status(200).json(paymentLink);
    } else {
      throw 'An error occurred, payment failed to initiate';
    }
  } catch (error) {
    console.log(error, ', failed payment');

    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

module.exports = {
  getCredit,
  buyCredit,
  getFreeCredit,
};

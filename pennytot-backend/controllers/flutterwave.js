const { TransactionModel, UserModel, CreditModel } = require('../models');
const Flutterwave = require('flutterwave-node-v3');
const createPaymentLink = require('../constants/flutterwave');
const axios = require('axios');
const { CREDIT_PER_NAIRA } = require('../constants/credit');
//import { appLogoImg } from '../constants/content';

const flw = new Flutterwave(
  process.env.FLW_PUBLIC_KEY,
  process.env.FLW_SECRET_KEY,
);

const generatePaymentLink = async ({ req, transactionId, amount }) => {
  return new Promise((resolve, reject) => {
    const payload = {
      tx_ref: transactionId,
      amount: amount,
      currency: 'NGN',
      redirect_url: `https://${req.hostname}/success`,
      meta: {
        phone_number: `${req.userAccount.phone_number.code}${req.userAccount.phone_number.number}`,
      },
      customer: {
        email: req.userAccount.email,
        phone: `${req.userAccount.phone_number.code}${req.userAccount.phone_number.number}`,
        name: `${req.userAccount.first_name} ${req.userAccount.last_name}`,
      },
      customizations: {
        title: process.env.TITLE,
        //  logo: appLogoImg,
      },
    };

    axios
      .post(createPaymentLink, payload, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'Accept-Encoding': 'application/json',
          Authorization: `Bearer ${process.env.FLW_SECRET_KEY}`,
        },
      })
      .then((response) => {
        if (response.data && response.data.data && response.data.data.link) {
          resolve(response.data);
        } else {
          reject('Payment cannot be processed now. Try again soon');
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

const updateCredit = async (transactionDetails, flwTransaction) => {
  try {
    console.log('confirmed update credit was called');

    //Update Transaction Data
    await TransactionModel.updateOne(
      { _id: transactionDetails._id },
      {
        $set: {
          data: {
            ...flwTransaction,
            pennytots_added: flwTransaction.amount * CREDIT_PER_NAIRA,
          },
          status: 'success',
        },
      },
      { new: true },
    );

    console.log('confirmed transaction was updated');

    //Update Wallet Amount
    await CreditModel.updateOne(
      { userId: transactionDetails.userId },
      { $inc: { amount: flwTransaction.amount * CREDIT_PER_NAIRA } },
      { new: true },
    );

    console.log('confirmed all is done');
  } catch (error) {
    console.log(error);
    return;
  }
};

const cancelTransaction = async (transactionDetails, flwTransaction) => {
  try {
    console.log('confirmed cancel transaction was called');

    //Update Transaction to cancelled
    await TransactionModel.updateOne(
      { _id: transactionDetails._id },
      {
        $set: {
          data: flwTransaction,
          status: 'cancelled',
        },
      },
      { new: true },
    );

    console.log('confirmed all is done with cancelled');
  } catch (error) {
    console.log(error);
    return;
  }
};

const webhook = async (req, res) => {
  try {
    const secretHash = process.env.FLW_SECRET_HASH;
    const signature = req.headers['verif-hash'];

    let responseDetails = req.body.data;

    console.log('signature secret is ', signature);
    console.log('secretHash is ', secretHash);

    console.log(responseDetails, '=== response details');

    //Verify signature
    if (!signature || signature !== secretHash) {
      // This request isn't from Flutterwave; discard
      return res.status(401).end();
    }

    const response = await flw.Transaction.verify({
      id: responseDetails.id,
    });

    const transactionDetails = await TransactionModel.findOne({
      _id: responseDetails.tx_ref,
    });

    if (!transactionDetails) {
      console.log('no record');
      //no record of such transactions initiated
      return res.status(401).end();
    }

    if (responseDetails.status === 'successful') {
      if (
        response.data.status === 'successful' &&
        response.data.amount === transactionDetails.amount &&
        response.data.currency === 'NGN'
      ) {
        console.log(
          'confirmed transaction gateway was successfully from flutterwave',
        );
        // Success! Confirm the customer's payment for flutterwave

        res.status(200).json({
          message: 'success',
        });

        //Check if transaction status is already updated

        if (transactionDetails.status == 'success') {
          return;
        }

        console.log(
          'confirmed transaction in db has not been updated with transaction details',
        );

        if (transactionDetails.action === 'buy-credit') {
          console.log('confirmed flutterwave checks');
          updateCredit(transactionDetails, response.data);
        }
      } else {
        // Inform the customer their payment was unsuccessful
        // return res.status(401).end();
      }
    } else if (responseDetails.status === 'cancelled') {
      const response = await flw.Transaction.verify({
        id: responseDetails.id,
      });

      if (
        response.data.status === 'cancelled' &&
        response.data.amount === transactionDetails.amount &&
        response.data.currency === 'NGN'
      ) {
        console.log(
          'confirmed transaction gateway was successfully from flutterwave',
        );

        // Success! Confirm the customer's payment for flutterwave

        res.status(200).json({
          message: 'success',
        });

        cancelTransaction(transactionDetails, response.data);
      } else {
        return res.status(401).end();
      }
    } else {
      return res.status(401).end();
    }
  } catch (error) {
    console.log(error), '== webhook error';
    res.status(401).json({
      error,
      message: 'An error occurred',
    });
  }
};

module.exports = {
  webhook,
  generatePaymentLink,
};

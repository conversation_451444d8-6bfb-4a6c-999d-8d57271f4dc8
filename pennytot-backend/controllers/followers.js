const { FollowerModel }= require('../models/index'); // assuming you have a model for the follower schema
const { userModel } = require('../models/index');
const handleExpoNotification = require('../middleware/notifications/expoNotification'); // Import Expo notification handler

// Function to follow a user
const followUser = async (req, res) => {
  try {
    const followerId = req.user.id; // assuming you get the follower's ID from the request
    const followingId = req.params.followingId; // assuming the ID of the user to follow is in the request body

    // Check if already following
    const existingFollow = await FollowerModel.findOne({ follower: followerId, following: followingId });
    if (existingFollow) {
      return res.status(400).json({ message: 'Already following this user' });
    }

    // Create a new follow relationship
    const follow = new FollowerModel({
      follower: followerId,
      following: followingId
    });
    await follow.save();
    const followedUserInfo = await userModel.findById(followingId);

// Check if the followed user has a device token
    if (followedUserInfo.device_token) {
      await handleExpoNotification({
        token: followedUserInfo.device_token,
        title: 'New Follower',
        body: `You have a new follower!`,
        type: 'follow',
        screenData: { userId: followerId } // Include follower's ID for potential use in the frontend
      });
}

    res.status(200).json({ message: 'Followed the user successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error following the user' });
  }
};

const isFollowingUser = async (req, res) => {
  try {
    const followerId = req.user.id; // assuming you get the follower's ID from the request
    const followingId = req.params.followingId; // assuming the ID of the user being checked is passed as a URL parameter

    // Check if there is a follow relationship
    const followRelation = await FollowerModel.findOne({ follower: followerId, following: followingId });

    if (followRelation) {
      return res.status(200).json({ message: 'You are following this user', isFollowing: true });
    } else {
      return res.status(200).json({ message: 'You are not following this user', isFollowing: false });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error checking follow status' });
  }
};

// Function to unfollow a user
const unfollowUser = async (req, res) => {
  try {
    const followerId = req.user.id; // assuming you get the follower's ID from the request
    const followingId = req.params.followingId; // assuming the ID of the user to unfollow is in the request body

    const result = await FollowerModel.deleteOne({ follower: followerId, following: followingId });
    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'Follow relationship not found' });
    }

    res.status(200).json({ message: 'Unfollowed the user successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error unfollowing the user' });
  }
};

const getFollowingList = async (req, res) => {
  try {
    const followerId = req.user.id; // Assuming you get the user's ID from the request

    // Find all follow relationships where the user is the follower
    let followingList = await FollowerModel.find({ follower: followerId })
                           .populate('following', "first_name last_name email") // Populate the 'following' field. Adjust the fields ('name email') as necessary.
                           .exec();

    // Optionally, transform the list to return only the user details
    followingList = followingList.map(follow => follow.following);

    res.status(200).json({ data: followingList, message: 'Following list fetched successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error fetching following list' });
  }
};

module.exports = {
  isFollowingUser,
  followUser,
  unfollowUser,
  getFollowingList,
};

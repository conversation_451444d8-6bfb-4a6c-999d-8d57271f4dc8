const { tagsModel, userModel } = require('../models/index');

const createTag = async (req, res) => {
  // res.status(400).json({
  //   message: 'Creating new tags has been disabled currently',
  // });
  // return;

  try {
    const tag = new tagsModel({
      name: req.body.name,
    });

    await tag.save();

    res.status(200).json({
      message: 'Tag created Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error.message ? error.message : error,
    });
  }
};

const getTags = async (req, res) => {
  try {
    let tags = await tagsModel.find();

    res.status(200).json({ data: tags, message: 'Loaded Tags Successfully' });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const setUserTags = async (req, res) => {
  try {
    let tags = req.body;
    console.log(tags);
    if (tags.length === 0) {
      res.status(400).json({ message: 'Please select at least one interest' });
      return;
    }

    const update = await userModel.updateOne(
      { _id: req.user.id },
      {
        $set: {
          interests: req.body,
        },
      },
    );

    res.status(200).json({
      message: 'User Interest updated successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const deleteAllTags = async (req, res) => {
  try {
    await tagsModel.deleteMany({}); // Delete all documents in the tags collection

    res.status(200).json({
      message: 'All tags deleted successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  createTag,
  getTags,
  setUserTags,
  deleteAllTags,
};

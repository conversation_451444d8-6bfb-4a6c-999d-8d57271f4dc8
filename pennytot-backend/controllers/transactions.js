const { TransactionModel } = require('../models');

const getTransactions = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    // populate: {
    //   path: 'userId',
    //   select: 'first_name last_name profile_picture',
    //   model: userModel,
    // },
    sort: { updatedAt: -1 },
  };

  let query = {
    userId: req.user.id,
  };

  if (req.query && req.query.type == 'success') {
    query.status = 'success';
  }

  try {
    let transactions = await TransactionModel.paginate(query, option);

    res.status(200).json({
      transactions,
    });
  } catch (error) {
    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

module.exports = {
  getTransactions,
};

const User = require('../models/users'); // Adjust the path based on your project structure
const UserResponse = require('../models/questions/userResponse');
const { CreditModel, TransactionModel } = require('../models');
const { CREDIT_PER_NAIRA } = require('../constants/credit');
const Question = require('../models/questions/questions');
const { QuestionModel } = require('../models/index');
const csv = require('csv-parser');
const stream = require('stream');
const fs = require('fs');



const handleUserResponse = async (req, res) => {
  try {
    const { userId, questionId, selectedOption } = req.body;

    // Retrieve user and question details
    const user = await User.findById(userId);
    const question = await Question.findById(questionId);

    // Check if the user's response is correct
    const isCorrect = question.correctAnswer === selectedOption;

    // Update user's credit based on the correctness of the answer
    if (isCorrect) {
      user.credits += 5;
    } else {
      user.credits -= 10;
    }

    // Save user's response to the database
    const userResponse = await UserResponse.create({
      user: userId,
      question: questionId,
      selectedOption,
      isCorrect,
    });

    // Save updated user details
    await user.save();

    // Provide response to the frontend
    res.status(200).json({ isCorrect, userResponse });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const uploadQuestions = async (req, res) => {
  try {
    const csvData = req.file.buffer.toString();
    const questions = [];
    const { country } = req.params; // Get country from URL parameters

    const readableStream = stream.Readable.from(csvData);

    readableStream
      .pipe(csv())
      .on('data', (row) => {
        if (row.question && row.answer) {
          const question = {
            question: row.question,
            options: row.options ? row.options.split(';') : [],
            answer: row.answer,
            country  // Assign country from the URL parameter
          };

          console.log(question);

          questions.push(question);
        }
      })
      .on('end', async () => {
        // Add questions to the database
        await QuestionModel.create(questions);
        res.status(201).json({ message: 'Successfully added questions for ' + country });
      });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const uploadQuestionsOld = async (req, res) => {
  try {
    const csvData = req.file.buffer.toString();
    const questions = [];

    const readableStream = stream.Readable.from(csvData);

    readableStream
      .pipe(csv())
      .on('data', (row) => {
        if (row.question && row.answer) {
          const question = {
            question: row.question,
            options: row.options ? row.options.split(';') : [],
            answer: row.answer,
          };

          console.log(question);

          questions.push(question);
        }
      })
      .on('end', async () => {
        // Add questions to the database
        await QuestionModel.create(questions);
        res.status(201).json({ message: 'Successfully added questions from CSV' });
      });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const uploadQuestionsReplaceOld = async (req, res) => {
  try {
    const csvData = req.file.buffer.toString();
    const questions = [];

    const readableStream = stream.Readable.from(csvData);

    readableStream
      .pipe(csv())
      .on('data', (row) => {
        if (row.question && row.answer) {
          const question = {
            question: row.question,
            options: row.options ? row.options.split(';') : [],
            answer: row.answer,
          };

          console.log(question);

          questions.push(question);
        }
      })
      .on('end', async () => {
        try {
          // Remove all existing questions from the database
          await QuestionModel.deleteMany({});

          // Add questions to the database
          await QuestionModel.create(questions);

          res.status(201).json({ message: 'Successfully replaced questions in the database with CSV data' });
        } catch (error) {
          console.error('Error replacing questions:', error);
          res.status(500).json({ error: 'Internal Server Error' });
        }
      });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const uploadQuestionsReplace = async (req, res) => {
  try {
    const csvData = req.file.buffer.toString();
    const questions = [];
    const { country } = req.params; // Get country from URL parameters

    const readableStream = stream.Readable.from(csvData);

    readableStream
      .pipe(csv())
      .on('data', (row) => {
        if (row.question && row.answer) {
          const question = {
            question: row.question,
            options: row.options ? row.options.split(';') : [],
            answer: row.answer,
            country
          };

          console.log(question);

          questions.push(question);
        }
      })
      .on('end', async () => {
        try {
          await QuestionModel.deleteMany({ country: country });

          // Add questions to the database
          await QuestionModel.create(questions);

          res.status(201).json({ message: 'Successfully replaced questions in the database with CSV data for ' + country  });
        } catch (error) {
          console.error('Error replacing questions:', error);
          res.status(500).json({ error: 'Internal Server Error' });
        }
      });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};


const addBulkQuestions = async (req, res) => {
  try {
    const questions = req.body.questions; // Assuming an array of question objects in the request body
    const addedQuestions = await QuestionModel.create(questions);
    res.status(201).json({ message: 'Successfully added questions' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};
const getQuestionsWithLimit = async (req, res) => {
  try {
    const { limit } = req.params;
    const questions = await QuestionModel.find().limit(parseInt(limit));
    res.status(200).json(questions);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]; // Swap elements
  }
  return array;
}


const getQuestion = async (req, res) => {
  try {
    const { country } = req.params; // Access country as a URL parameter
    const questionCount = await QuestionModel.countDocuments({ country });
    const randomIndex = Math.floor(Math.random() * questionCount);
    const randomQuestion = await QuestionModel.findOne({ country }).skip(randomIndex);

    if (randomQuestion && randomQuestion.options) {
        randomQuestion.options = shuffleArray([...randomQuestion.options]);
    }

    res.status(200).json(randomQuestion);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const getQuestionOld = async (req, res) => {
  try {
    const questionCount = await QuestionModel.countDocuments();
    const randomIndex = Math.floor(Math.random() * questionCount);
    const randomQuestion = await QuestionModel.findOne().skip(randomIndex);

    // Check if the randomQuestion has options and shuffle them
    if (randomQuestion && randomQuestion.options) {
        randomQuestion.options = shuffleArray([...randomQuestion.options]);
    }

    res.status(200).json(randomQuestion);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
};

const changeUserPennyTot = async (req, res) => {
  try {
    let credit = await CreditModel.findOne({
      userId: req.user.id,
    });

    const { type, amount } = req.params;

    if (!credit) {
      throw 'Cannot find credit account';
    }

    let transactionType, actionMessage;

    if (type === 'reduce') {
      if (credit.amount < amount) {
        throw 'Insufficient funds';
      }

      transactionType = 'quiz';
      actionMessage = `Deducted ${amount} Pennytots from the user's account.`;

      await CreditModel.updateOne(
        { userId: req.user.id },
        {
          $inc: { amount: -amount },
        },
      );
    } else if (type === 'increase') {
      transactionType = 'quiz';
      actionMessage = `Added ${amount} Pennytots to the user's account.`;

      await CreditModel.updateOne(
        { userId: req.user.id },
        {
          $inc: { amount: amount },
        },
      );
    } else {
      throw 'Invalid type. Use "reduce" or "increase"';
    }

    await TransactionModel.create({
      userId: req.user.id,
      source: transactionType,
      action: "buy-credit",
      amount: amount * CREDIT_PER_NAIRA,
      status: 'success',
      data: {
        pennytots_changed: amount,
      },
    });

    return res.status(200).json({
      message: `Successfully ${actionMessage}`,
    });
  } catch (error) {
    console.error(error);
    res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};


module.exports = {
  uploadQuestionsOld,
  addBulkQuestions,
  getQuestionsWithLimit,
  handleUserResponse,
  uploadQuestions,
  uploadQuestionsReplace,
  uploadQuestionsReplaceOld,
  changeUserPennyTot,
  getQuestion,
  getQuestionOld,
};

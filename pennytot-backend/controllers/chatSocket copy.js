const jwt = require('jsonwebtoken');
const {
  chatMessagesModel,
  chatsModel,
  groupsModel,
  groupMessagesModel,
  userModel,
} = require('../models');
const express = require('express');
const app = express();
const io = '';

module.exports = function (http) {
  let user = {};
  const io = require('socket.io')(http, {
    cors: {
      origin: '*',
      credentials: true,
    },
  });

  app.set('io', io);

  io.use((socket, next) => {
    const token = socket.handshake.auth.token;

    try {
      user = jwt.verify(token, process.env.TOKEN_SECRET);

      next();
    } catch (error) {
      next(new Error('invalid'));
    }
  });

  //To listen to messages
  io.on('connection', (socket) => {
    //  console.log('user connected');

    //CHAT APPLICATION
    socket.on('join-chat', async function (chatRoom, callback) {
      let getChat = await chatsModel.findOne({
        participants: { $all: [chatRoom.accountId, user.id] },
      });

      if (getChat) {
        await socket.join(chatRoom.chatId);
        callback({
          success: true,
          message: 'Chat Connected',
        });
        return;
      } else {
        callback({
          success: false,
          message: 'Chat Connection Failed',
        });
        return;
      }
    });

    socket.on('Private Chat', async function (messagePayload, callback) {
      if (!messagePayload.token) {
        callback({
          success: false,
          message: 'Token required',
        });
        return;
      }

      //messageData is message payload
      let sender = jwt.verify(messagePayload.token, process.env.TOKEN_SECRET);

      let getChat = await chatsModel.findOne({
        participants: { $all: [messagePayload.accountId, sender.id] },
      });

      if (getChat) {
        try {
          let data = await chatMessagesModel.create({
            chatId: getChat._id,
            message: messagePayload.message,
            senderId: sender.id,
            type: messagePayload.type,
          });

          // socket.broadcast.emit('received', data);
          io.in(messagePayload.chatId).emit('chat-messages', data);
          callback({
            success: true,
            message: 'Message delivered Successfully',
          });
          return;

          // socket.emit('received', data);
        } catch (err) {
          callback({
            success: false,
            message: err,
          });
          return;
          // console.log(err);
        }
      } else {
        callback({
          success: false,
          message: 'Invalid Request, Something went wrong',
        });
        return;
      }
    });

    //GROUPS
    socket.on('join-group', async function (groupId, callback) {
      try {
        // let group = 'group-' + groupId;
        await socket.join('group-' + groupId);
        callback({
          success: true,
          message: `Group Chat Connected`,
        });
        return;
      } catch (err) {
        callback({
          success: false,
          message: 'Group Chat Connection Failed',
        });
        return;
      }
    });

    socket.on('Group Chat', async function (messagePayload, callback) {
      if (!messagePayload.token) {
        callback({
          success: false,
          message: 'Token required',
        });
        return;
      }

      let sender = jwt.verify(messagePayload.token, process.env.TOKEN_SECRET);

      //messageData is message payload
      let getGroupChat = await groupsModel.findOne({
        $and: [{ _id: messagePayload.groupId }, { participants: sender.id }],
      });

      if (getGroupChat) {
        try {
          let data = await groupMessagesModel.create({
            groupId: messagePayload.groupId,
            message: messagePayload.message,
            senderId: sender.id,
            type: messagePayload.type,
          });

          let userData = await userModel.findById(sender.id);
          data = JSON.parse(JSON.stringify(data));
          userData = JSON.parse(JSON.stringify(userData));

          data.senderId = {
            _id: userData._id,
            first_name: userData.first_name,
            last_name: userData.last_name,
          };
          if (userData.profile_picture) {
            data.senderId.profile_picture = userData.profile_picture;
          }

          // socket.broadcast.emit('received', data)
          //  console.log('bgr', messagePayload.groupId);
          io.in('group-' + messagePayload.groupId).emit('group-messages', data);
          callback({
            success: true,
            message: 'Message delivered Successfully',
          });
          return;
          // socket.emit('received', data);
        } catch (err) {
          console.log(err);
          callback({
            success: false,
            message: err,
          });
          return;
          // console.log(err);
        }
      } else {
        callback({
          success: false,
          message: 'You are not a participant of the group',
        });
        return;
      }
    });
  });
};

const {
  topicsModel,
  convosModel,
  userModel,
  commentLikesModel,
  topicLikesModel,
  topicCommentsModel,
  topicSubCommentsModel,
  flaggedTopicsModel,
  flaggedTopicCommentModel,
  mutedNotificationsModel,
  FollowerModel
} = require('../models/index');

const { handleExpoNotification } = require('../middleware/notifications/expoNotification');
const { dbConnection } = require('../utils/db');
const path = require('path');
const fs = require('fs');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);
const { error403 } = require('../utils/requestResponses');
const handleCommentNotification = require('../middleware/notifications/commentNotification');
const handleConvoUnreadNotifications = require('../events/convoUnreadNotification');
const { treatedContentsModel } = require('../models/admin');
const { checkCredit, deductCredit } = require('../middleware/credit');
const {
  GOOGLE_CLOUD_TOPIC_VIDEO_BASE_URL,
  GOOGLE_CLOUD_TOPIC_AUDIO_BASE_URL,
  GOOGLE_CLOUD_PROFILE_PICTURE_BASE_URL,
  GOOGLE_CLOUD_TOPIC_IMAGE_BASE_URL,
} = require('../constants/bucket');

const getTopicsOld = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    populate: {
      path: 'userId',
      select: 'first_name last_name profile_picture',
      model: userModel,
    },
    sort: { updatedAt: -1 },
  };

  try {
    let topics = await topicsModel.paginate(
      { tags: { $in: req.userAccount.interests }, appropriate: true },
      option,
    );

    topics = JSON.parse(JSON.stringify(topics));

    for (i in topics.docs) {
      let muted = await mutedNotificationsModel.findOne({
        userId: req.user.id,
        contentId: topics.docs[i]._id,
        contentType: 'topic',
      });

      if (muted) {
        topics.docs[i].muted = true;
      } else {
        topics.docs[i].muted = false;
      }

      let likes = await topicLikesModel
        .find({ topicId: topics.docs[i]._id })
        .count();

      topics.docs[i].likes = likes;

      let comments = await topicCommentsModel
        .find({ topicId: topics.docs[i]._id })
        .count();

      topics.docs[i].noOfComments = comments;

      let checkUserLike = await topicLikesModel.find({
        topicId: topics.docs[i]._id,
        userId: req.user.id,
      });

      if (checkUserLike && checkUserLike.length === 0) {
        topics.docs[i].isLiked = false;
      } else {
        topics.docs[i].isLiked = true;
      }
    }

    res.status(200).json({ topics, message: 'Loaded Topics Successfully' });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const getTopics = async (req, res) => {
  const option = {
    page: Number(req.query.page) || 1,
    limit: Number(req.query.limit) || Number(process.env.PAGING_LIMIT),
  };

  try {
    const topics = await topicsModel.aggregate([
      {
        $match: {
          tags: { $in: req.userAccount.interests },
          appropriate: true,
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'userId',
        },
      },
      {
        $addFields: {
          userId: {
            $arrayElemAt: ['$userId', 0],
          },
        },
      },
      {
        $project: {
          'userId._id': 1,
          'userId.first_name': 1,
          'userId.last_name': 1,
          'userId.company':1,
          'userId.bio':1,
          'userId.company_position':1,
          'userId.profile_picture': {
            $cond: [
              { $eq: ['$userId.profile_picture', null] },
              null,
              {
                $concat: [
                  GOOGLE_CLOUD_PROFILE_PICTURE_BASE_URL,
                  '$userId.profile_picture',
                ],
              },
            ],
          },
          title: 1,
          body: 1,
          tags: 1,
          deleted: 1,
          content: 1,
          appropriate: 1,
          attachmentSize: 1,
          document: 1,
          image: {
            $cond: [
              { $eq: ['$image', null] },
              null,
              {
                $concat: [GOOGLE_CLOUD_TOPIC_IMAGE_BASE_URL, '$image'],
              },
            ],
          },
          audio: {
            $cond: [
              { $eq: ['$audio', null] },
              null,
              {
                $concat: [GOOGLE_CLOUD_TOPIC_AUDIO_BASE_URL, '$audio'],
              },
            ],
          },
          video: {
            $cond: [
              { $eq: ['$video', null] },
              null,
              {
                $concat: [GOOGLE_CLOUD_TOPIC_VIDEO_BASE_URL, '$video'],
              },
            ],
          },
          createdAt: 1,
          updatedAt: 1,
        },
      },
      {
        $sort: { updatedAt: -1 },
      },
      {
        $facet: {
          topics: [
            { $skip: (option.page - 1) * option.limit },
            { $limit: option.limit },
            {
              $lookup: {
                from: 'topiccomments',
                localField: '_id',
                foreignField: 'topicId',
                as: 'comments',
              },
            },
            {
              $addFields: {
                muted: {
                  $cond: [
                    {
                      $in: [req.user.id, { $ifNull: ['$muted_users', []] }],
                    },
                    true,
                    false,
                  ],
                },
                likes: {
                  $size: { $ifNull: ['$likes', []] },
                },
                noOfComments: {
                  $size: { $ifNull: ['$comments', []] },
                },
                isLiked: {
                  $in: [req.user.id, { $ifNull: ['$likes.userId', []] }],
                },
              },
            },
          ],
          count: [
            {
              $count: 'total',
            },
          ],
        },
      },
    ]);

    const response = {
      topics: { docs: topics[0].topics },
      message: 'Loaded Topics Successfully',
    };

    if (topics[0].count && topics[0].count.length > 0) {
      response.total = topics[0].count[0].total;
    }

    res.status(200).json(response);
  } catch (error) {
    res.status(500).json({ error });
  }
};

const getUserTopics = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    populate: {
      path: 'userId',
      select: 'first_name last_name profile_picture',
      model: userModel,
    },
    sort: { updatedAt: -1 },
  };

  try {
    let topics = await topicsModel.paginate(
      {
        tags: { $in: req.userAccount.interests },
        appropriate: true,
        userId: req.params.userId,
      },
      option,
    );

    topics = JSON.parse(JSON.stringify(topics));

    for (i in topics.docs) {
      try {
        let likes = await topicLikesModel
          .find({ topicId: topics.docs[i]._id })
          .countDocuments();

        if (likes) {
          topics.docs[i].likes = likes;
        } else {
          topics.docs[i].likes = 0;
        }

        let comments = await topicCommentsModel
          .find({ topicId: topics.docs[i]._id })
          .countDocuments();

        topics.docs[i].noOfComments = comments;

        let checkUserLike = await topicLikesModel.find({
          topicId: topics.docs[i]._id,
          userId: req.user.id,
        });

        if (checkUserLike && checkUserLike.length === 0) {
          topics.docs[i].isLiked = false;
        } else {
          topics.docs[i].isLiked = true;
        }
      } catch (error) {
        console.log(error);
      }
    }

    res.status(200).json({ topics, message: 'Loaded Topics Successfully' });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const getConvos = async (req, res) => {
  const page = Number(req.query.page) || 1;
  const limit = Number(req.query.limit) || Number(process.env.PAGING_LIMIT);
  const skip = (page - 1) * limit;

  try {
    console.time('Total Time');
    console.time('Aggregation Pipeline');

    const topics = await convosModel.aggregate([
      { $match: { userId: req.user.id } },
      { $project: { topicId: { $toObjectId: '$topicId' } } },
      {
        $lookup: {
          from: 'topics',
          localField: 'topicId',
          foreignField: '_id',
          as: 'topicId',
        },
      },
      { $unwind: '$topicId' },
      { $sort: { 'topicId.updatedAt': -1 } },
      { $skip: skip },
      { $limit: limit },
    ]);

    console.timeEnd('Aggregation Pipeline');

    console.time('Count Documents');
    const totalDocs = await convosModel.countDocuments({ userId: req.user.id });
    console.timeEnd('Count Documents');

    const formattedTopics = JSON.parse(JSON.stringify(topics));

    console.time('Process Each Topic');

    const processedTopics = await Promise.all(
      formattedTopics.map(async (topic) => {
        const userPromise = userModel.findById(topic.topicId.userId).exec();
        const mutedPromise = mutedNotificationsModel.findOne({
          userId: req.user.id,
          contentId: topic.topicId._id,
          contentType: 'topic',
        }).exec();
        const likesPromise = topicLikesModel.countDocuments({ topicId: topic.topicId._id }).exec();
        const commentsPromise = topicCommentsModel.countDocuments({ topicId: topic.topicId._id }).exec();
        const checkUserLikePromise = topicLikesModel.findOne({
          topicId: topic.topicId._id,
          userId: req.user.id,
        }).exec();

        // Wait for all promises to resolve
        const [user, muted, likes, comments, checkUserLike] = await Promise.all([
          userPromise,
          mutedPromise,
          likesPromise,
          commentsPromise,
          checkUserLikePromise,
        ]);

        if (topic.topicId.image) {
          topic.topicId.image = bucket
            .file(`${process.env.TOPICS_IMAGE_DIRECTORY}/${topic.topicId.image}`)
            .publicUrl();
        }

        topic.topicId.userId = {
          first_name: user.first_name,
          last_name: user.last_name,
          profile_picture: user.profile_picture,
          company: user.company,
          company_position: user.company_position,
          _id: user._id,
        };

        topic.topicId.muted = !!muted;
        topic.topicId.likes = likes;
        topic.topicId.noOfComments = comments;
        topic.topicId.isLiked = !!checkUserLike;

        return topic;
      })
    );

    console.timeEnd('Process Each Topic');

    res.status(200).json({
      topics: { docs: processedTopics },
      total: totalDocs,
      page,
      limit,
      message: 'Loaded Convos Successfully',
    });

    console.timeEnd('Total Time');
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error.message });
  }
};



const removeFromConvo = async (req, res) => {
  let topicConvo = await convosModel.findOne({
    userId: req.user.id,
    topicId: req.params.topicId,
  });
  if (topicConvo) {
    await topicConvo.deleteOne();
  }

  res.status(200).json({
    message: 'Topic has been removed from Convo Successfully',
  });
};

const getTopic = async (req, res) => {
  try {
    let topic = await topicsModel
      .findById(req.params.topicId)
      .populate('userId', 'first_name last_name profile_picture', userModel);

    topic = JSON.parse(JSON.stringify(topic));

    let muted = await mutedNotificationsModel.findOne({
      userId: req.user.id,
      contentId: req.params.topicId,
      contentType: 'topic',
    });

    if (muted) {
      topic.muted = true;
    } else {
      topic.muted = false;
    }

    let likes = await topicLikesModel
      .find({ topicId: req.params.topicId })
      .count();

    topic.likes = likes;
    let checkUserLike = await topicLikesModel.find({
      topicId: topic._id,
      userId: req.user.id,
    });

    if (checkUserLike && checkUserLike.length === 0) {
      topic.isLiked = false;
    } else {
      topic.isLiked = true;
    }

    res.status(200).json({ topic, message: 'Loaded Topic Successfully' });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const createTopic = async (req, res) => {
  try {
    if (
      req.file &&
      !(
        (req.body.attachmentType == 'image') |
        (req.body.attachmentType == 'video') |
        (req.body.attachmentType == 'audio') |
        (req.body.attachmentType == 'document')
      )
    ) {
      res.status(400).json({
        message: 'Attachment type is required',
      });
      return;
    }

    let topicsFolder = '';

    if (req.body.attachmentType == 'image') {
      topicsFolder = process.env.TOPICS_IMAGE_DIRECTORY + '/';
    }
    if (req.body.attachmentType == 'audio') {
      topicsFolder = process.env.TOPICS_AUDIO_DIRECTORY + '/';
    }
    if (req.body.attachmentType == 'video') {
      topicsFolder = process.env.TOPICS_VIDEO_DIRECTORY + '/';
    }
    if (req.body.attachmentType == 'document') {
      topicsFolder = process.env.TOPICS_DOCUMENT_DIRECTORY + '/';
    }

    let tags = null;

    if (req.body.tags) {
      tags = JSON.parse(req.body.tags);
      if (tags.length === 0) {
        res
          .status(400)
          .json({ message: 'Please select at least one interest' });
        return;
      } else if (tags.length > 3) {
        res
          .status(400)
          .json({ message: 'You can only select three tags at once' });
        return;
      }
    }

    const topic = new topicsModel({
      userId: req.user.id,
      content: req.body.content,
      tags,
    });

    const source = req.file ? 'CREATE_MEDIA_TOPIC' : 'CREATE_TEXT_TOPIC';

    await checkCredit({
      source,
      userId: req.user.id,
    });

    const topicData = await topic.save();

    const addTopicToConvos = new convosModel({
      userId: req.user.id,
      topicId: topicData.id,
    });

    await addTopicToConvos.save();

    if (topicData) {
      await deductCredit({ source, userId: req.user.id });

      if (req.file) {
        let fileName =
          new Date().getTime() +
          req.user.id +
          path.extname(req.file.originalname);
        const blob = bucket.file(topicsFolder + req.file.originalname);
        fs.createReadStream(req.file.path)
          .pipe(blob.createWriteStream())
          .on('error', (err) => {
            console.log(err);
            //  res.status(400).json({ message: err });
            next(err);
          })
          .on('finish', async () => {
            try {
              await blob.rename(topicsFolder + fileName);
              await bucket.file(topicsFolder + fileName).makePublic();

              let update = null;

              if (req.body.attachmentType == 'image') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      image: fileName,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'audio') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      audio: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'video') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      video: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'document') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      document: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              }

              res.status(200).json({
                message: 'Topic Created Successfully',
                topic: update,
              });
            } catch (error) {
              console.log(error);
              res
                .status(400)
                .json({ message: 'An Error Occurred', data: error });
            }
          });
      } else {
        res.status(200).json({
          message: 'Topic Created Successfully',
          topic: topicData,
        });
      }
    }
    if (topicData) {
      const followers = await FollowerModel.find({ following: req.user.id }).distinct('follower');

      // Fetch device tokens of all followers
      const followerTokens = await userModel.find({
        _id: { $in: followers }
      }).distinct('device_token');

      // Send notifications to all followers
      for (const token of followerTokens) {
        if (token) {
          await handleExpoNotification({
            token: token,
            title: 'New Topic',
            body: `${req.user.firstName} ${req.user.lastName} just created a new topic!`,
            type: 'new_topic',
            screenData: { topicId: topicData._id } // Include new topic's ID
          });
        }
      }
    }
  
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

const editTopic = async (req, res) => {
  try {
    if (!req.body.content) {
      throw 'Content is required';
    }

    if (!req.params.topicId) {
      throw 'topicId is required';
    }

    let topic = await topicsModel.findById(req.params.topicId);

    if (topic) {
      if (topic.userId.toString() != req.userAccount._id.toString()) {
        throw "You don't have permission to edit this topic";
      }

      await topicsModel.updateOne(
        { _id: req.params.topicId },
        {
          $set: {
            content: req.body.content,
          },
        },
      );

      return res.status(200).json({
        message: 'Topic was updated successfully',
      });
    } else {
      return res.status(400).json({
        message: 'Topic does not exist',
      });
    }
  } catch (error) {
    console.log(error);

    res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

const likeTopic = async (req, res) => {
  try {
    await topicsModel.updateOne(
      { _id: req.params.topicId },
      {
        $set: {
          updatedAt: new Date(),
        },
      },
    );

    let checkTopicLike = await topicLikesModel.findOne({
      userId: req.user.id,
      topicId: req.params.topicId,
    });

    if (checkTopicLike) {
      await checkTopicLike.deleteOne();
      res.status(200).json({
        message: 'Topic like has been reverted Successfully',
      });
      return;
    }

    const like = new topicLikesModel({
      userId: req.user.id,
      topicId: req.params.topicId,
    });

    await like.save();

    let checkConvos = await convosModel.findOne({
      userId: req.user.id,
      topicId: req.params.topicId,
    });

    if (!checkConvos) {
      const addTopicToConvos = new convosModel({
        userId: req.user.id,
        topicId: req.params.topicId,
      });

      await addTopicToConvos.save();
    }

    res.status(200).json({
      message: 'Topic Liked Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const likeComment = async (req, res) => {
  try {
    let checkCommentLike = await commentLikesModel.findOne({
      userId: req.user.id,
      commentId: req.params.commentId,
    });

    if (checkCommentLike) {
      await checkCommentLike.deleteOne();
      res.status(200).json({
        message: 'Comment like has been reverted Successfully',
      });
      return;
    }

    const like = new commentLikesModel({
      userId: req.user.id,
      commentId: req.params.commentId,
      type: req.query.type,
    });

    await like.save();

    res.status(200).json({
      message: 'Topic Comment Liked Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const searchTopics = async (req, res) => {
  try {
    if (!req.body.search) {
      res.status(400).json({ message: 'Search field cannot be empty' });
      return;
    }

    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      populate: {
        path: 'userId',
        select: 'first_name last_name profile_picture',
        model: userModel,
      },
      sort: { id: -1 },
    };

    let topics = await topicsModel.paginate(
      {
        content: new RegExp(req.body.search, 'i'),
        appropriate: true,
      },
      option,
    );

    topics = JSON.parse(JSON.stringify(topics));

    for (i in topics.docs) {
      let likes = await topicLikesModel
        .find({ topicId: topics.docs[i]._id })
        .count();

      topics.docs[i].likes = likes;

      let comments = await topicCommentsModel
        .find({ topicId: topics.docs[i]._id })
        .count();

      topics.docs[i].noOfComments = comments;

      let checkUserLike = await topicLikesModel.find({
        topicId: topics.docs[i]._id,
        userId: req.user.id,
      });

      if (checkUserLike && checkUserLike.length === 0) {
        topics.docs[i].isLiked = false;
      } else {
        topics.docs[i].isLiked = true;
      }
    }

    res.status(200).json({ topics, message: 'Loaded Topics Successfully' });
  } catch (error) {
    res.status(400).json({ message: error });
  }
};

const createTopicComment = async (req, res) => {
  if (
    req.file &&
    !(
      (req.body.attachmentType == 'image') |
      (req.body.attachmentType == 'video') |
      (req.body.attachmentType == 'audio') |
      (req.body.attachmentType == 'document')
    )
  ) {
    res.status(400).json({
      message: 'Attachment type is required',
    });
    return;
  }

  let commentsFolder = '';

  if (req.body.attachmentType == 'image') {
    commentsFolder = process.env.TOPICS_COMMENT_IMAGE_DIRECTORY + '/';
  }
  if (req.body.attachmentType == 'audio') {
    commentsFolder = process.env.TOPICS_COMMENT_AUDIO_DIRECTORY + '/';
  }
  if (req.body.attachmentType == 'video') {
    commentsFolder = process.env.TOPICS_COMMENT_VIDEO_DIRECTORY + '/';
  }
  if (req.body.attachmentType == 'document') {
    commentsFolder = process.env.TOPICS_COMMENT_DOCUMENT_DIRECTORY + '/';
  }

  try {
    const commentData = new topicCommentsModel({
      userId: req.user.id,
      topicId: req.params.topicId,
      comment: req.body.comment,
    });

    await commentData.save();

    if (commentData) {
      await topicsModel.updateOne(
        { _id: req.params.topicId },
        {
          $set: {
            updatedAt: new Date(),
          },
        },
      );

      let checkConvos = await convosModel.findOne({
        userId: req.user.id,
        topicId: req.params.topicId,
      });

      if (!checkConvos) {
        const addTopicToConvos = new convosModel({
          userId: req.user.id,
          topicId: req.params.topicId,
        });

        await addTopicToConvos.save();
      }

      if (req.file) {
        let fileName =
          new Date().getTime() +
          req.user.id +
          path.extname(req.file.originalname);
        const blob = bucket.file(commentsFolder + req.file.originalname);
        fs.createReadStream(req.file.path)
          .pipe(blob.createWriteStream())
          .on('error', (err) => {
            console.log(err);
            res.status(400).json({ message: 'File attachment failed' });
            next(err);
          })
          .on('finish', async () => {
            try {
              await blob.rename(commentsFolder + fileName);
              await bucket.file(commentsFolder + fileName).makePublic();

              let update = null;
              if (req.body.attachmentType == 'image') {
                update = await topicCommentsModel.findOneAndUpdate(
                  { _id: commentData.id },
                  {
                    $set: {
                      image: fileName,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'audio') {
                update = await topicCommentsModel.findOneAndUpdate(
                  { _id: commentData.id },
                  {
                    $set: {
                      audio: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'video') {
                update = await topicCommentsModel.findOneAndUpdate(
                  { _id: commentData.id },
                  {
                    $set: {
                      video: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'document') {
                update = await topicCommentsModel.findOneAndUpdate(
                  { _id: commentData.id },
                  {
                    $set: {
                      document: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              }

              //Get all users that have the topic in their convos
              let topicSubscribers = await convosModel
                .find({
                  topicId: req.params.topicId,
                })
                .distinct('userId');

              // Comment push notification
              handleCommentNotification(req, topicSubscribers);
              //update unread for all user
              handleConvoUnreadNotifications(
                req.params.topicId,
                topicSubscribers,
              );

              res.status(200).json({
                message: 'Comment Submitted Successfully',
                topic: update,
              });
            } catch (error) {
              console.log(error);
              res
                .status(400)
                .json({ message: 'An Error Occurred', data: error });
            }
          });
      } else {
        let topicSubscribers = await convosModel
          .find({
            topicId: req.params.topicId,
          })
          .distinct('userId');

        // Comment push notification
        handleCommentNotification(req, topicSubscribers);
        //update unread for all user
        handleConvoUnreadNotifications(req.params.topicId, topicSubscribers);

        res.status(200).json({
          message: 'Comment Submitted Successfully',
          topic: commentData,
        });
      }
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const createTopicSubComment = async (req, res) => {
  try {
    const commentData = new topicSubCommentsModel({
      userId: req.user.id,
      commentId: req.params.commentId,
      comment: req.body.comment,
    });

    await commentData.save();

    //handleCommentNotification(req);

    res.status(200).json({
      message: 'Comment Submitted Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const getTopicComments = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    populate: {
      path: 'userId',
      select: 'first_name last_name profile_picture',
      model: userModel,
    },
    sort: { _id: -1 },
  };

  try {
    let TopicComments = await topicCommentsModel.paginate(
      { topicId: req.params.topicId, appropriate: true },
      option,
    );

    TopicComments = JSON.parse(JSON.stringify(TopicComments));

    for (i in TopicComments.docs) {
      let subCommentsCount = await topicSubCommentsModel
        .find({ commentId: TopicComments.docs[i]._id })
        .countDocuments();
      TopicComments.docs[i].subCommentsCount = subCommentsCount;

      let likes = await commentLikesModel
        .find({ commentId: TopicComments.docs[i]._id })
        .count();

      TopicComments.docs[i].likes = likes;

      let checkUserLike = await commentLikesModel.find({
        commentId: TopicComments.docs[i]._id,
        userId: req.user.id,
      });

      if (checkUserLike && checkUserLike.length === 0) {
        TopicComments.docs[i].isLiked = false;
      } else {
        TopicComments.docs[i].isLiked = true;
      }
    }

    res.status(200).json({
      comments: TopicComments,
      message: 'Loaded Topic Comments Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const getTopicSubComments = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    populate: {
      path: 'userId',
      select: 'first_name last_name profile_picture',
      model: userModel,
    },
    sort: { _id: -1 },
  };

  try {
    let TopicSubComments = await topicSubCommentsModel.paginate(
      { commentId: req.params.commentId, appropriate: true },
      option,
    );

    TopicSubComments = JSON.parse(JSON.stringify(TopicSubComments));

    for (i in TopicSubComments.docs) {
      let likes = await commentLikesModel
        .find({ commentId: TopicSubComments.docs[i]._id })
        .count();

      TopicSubComments.docs[i].likes = likes;

      let checkUserLike = await commentLikesModel.find({
        commentId: TopicSubComments.docs[i]._id,
        userId: req.user.id,
      });

      if (checkUserLike && checkUserLike.length === 0) {
        TopicSubComments.docs[i].isLiked = false;
      } else {
        TopicSubComments.docs[i].isLiked = true;
      }
    }

    res.status(200).json({
      comments: TopicSubComments,
      message: 'Loaded Topic Sub Comments Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const getTopicComment = async (req, res) => {
  try {
    let topicComment = await topicCommentsModel
      .findById(req.params.topicCommentId)
      .populate('userId', 'first_name last_name profile_picture', userModel);

    res.status(200).json({
      comment: topicComment,
      message: 'Loaded Topic Comment Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const flagTopic = async (req, res) => {
  try {
    const flag = new flaggedTopicsModel({
      reporterId: req.user.id,
      topicId: req.params.topicId,
    });

    await flag.save();

    let countFlag = await flaggedTopicsModel
      .find({ topicId: req.params.topicId })
      .count();

    let flagLimit = process.env.TOPIC_FLAG_LIMIT;

    if (countFlag >= flagLimit) {
      const update = await topicsModel.updateOne(
        { _id: req.params.topicId },
        {
          $set: {
            appropriate: false,
          },
        },
      );

      const checkTreated = await treatedContentsModel.findOne({
        contentId: req.params.topicId,
        contentType: 'topic',
      });

      if (!checkTreated) {
        await treatedContentsModel.create({
          contentId: req.params.topicId,
          contentType: 'topic',
        });
      }
    }

    res.status(200).json({
      message:
        'Topic has been reported, thank you, you will get a feedback soon',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const flagTopicComment = async (req, res) => {
  try {
    const flag = new flaggedTopicCommentModel({
      reporterId: req.user.id,
      commentId: req.params.commentId,
    });

    await flag.save();

    let countFlag = await flaggedTopicCommentModel
      .find({ commentId: req.params.commentId })
      .count();

    let flagLimit = process.env.TOPIC_FLAG_LIMIT;

    if (countFlag >= flagLimit) {
      const update = await topicCommentsModel.updateOne(
        { _id: req.params.commentId },
        {
          $set: {
            appropriate: false,
          },
        },
      );

      const checkTreated = await treatedContentsModel.findOne({
        contentId: req.params.commentId,
        contentType: 'topic-comment',
      });

      if (!checkTreated) {
        await treatedContentsModel.create({
          contentId: req.params.commentId,
          contentType: 'topic-comment',
        });
      }
    }

    res.status(200).json({
      message:
        'Comment has been reported, thank you, you will get a feedback soon',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

module.exports = {
  getTopic,
  getTopics,
  getTopicsOld,
  getUserTopics,
  getConvos,
  removeFromConvo,
  createTopic,
  editTopic,
  likeTopic,
  likeComment,
  searchTopics,
  createTopicComment,
  createTopicSubComment,
  getTopicComments,
  getTopicSubComments,
  getTopicComment,
  flagTopic,
  flagTopicComment,
};

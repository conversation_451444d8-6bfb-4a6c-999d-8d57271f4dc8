const {
  chatsModel,
  chatMessagesModel,
  userModel,
  groupsModel,
  groupMessagesModel,
  mutedNotificationsModel,
} = require('../models/index');
const fs = require('fs');
const path = require('path');
const { Storage } = require('@google-cloud/storage');
const { checkCredit, deductCredit } = require('../middleware/credit');
const {
  GOOGLE_CLOUD_PROFILE_PICTURE_BASE_URL,
} = require('../constants/bucket');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const getChatMessages = async (req, res) => {
  try {
    let getChat = await chatsModel.findOne({
      participants: { $all: [req.params.accountId, req.user.id] },
    });

    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { createdAt: -1 },
    };

    if (getChat) {
      // getChat = JSON.parse(JSON.stringify(getChat));

      let chats = await chatMessagesModel.paginate(
        {
          chatId: getChat._id,
        },
        option,
      );

      chats = JSON.parse(JSON.stringify(chats));
      chats.chatDetails = getChat;

      //Update last seen for chat
      let participantIndex = 0;
      if (getChat.participants[0] == req.user.id) {
        participantIndex = 0;
      } else {
        participantIndex = 1;
      }

      if (
        chats.docs.length > 0 &&
        chats.docs[0].createdAt !== getChat.lastRead[participantIndex]
      ) {
        await chatsModel.updateOne(
          { _id: getChat._id },
          {
            $set: { [`lastRead.${participantIndex}`]: chats.docs[0].createdAt },
          },
        );
      }

      for (i in chats.docs) {
        if (chats.docs[i].senderId === req.user.id) {
          chats.docs[i].receiverId = req.params.accountId;
        } else if (chats.docs[i].senderId === req.user.accountId) {
          chats.docs[i].receiverId = req.user.id;
        }
      }

      return res.status(200).json({ chats });
    } else {
      let checkUser = await userModel.findById(req.params.accountId);

      if (checkUser) {
        let newChats = await chatsModel.create({
          participants: [req.user.id, req.params.accountId],
        });

        let chats = await chatMessagesModel.paginate(
          {
            chatId: newChats._id,
          },
          option,
        );

        chats = JSON.parse(JSON.stringify(chats));
        chats.chatDetails = newChats;

        return res.status(200).json({ chats });
      } else {
        return res.status(400).json({ message: 'invalid user' });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(400).json({ message: error });
  }
};

const loadChats = async (req, res) => {
  res.status(200).json({ message: 'g' });
};

const searchChatMessages = async (req, res) => {
  if (!req.body.search) {
    res.status(400).json({ message: 'Search field cannot be empty' });
    return;
  }

  try {
    let getChat = await chatsModel.findById(req.params.chatId);
    if (getChat) {
      let option = {
        page: req.query.page || 1,
        limit: req.query.limit || process.env.PAGING_LIMIT,
        // populate: [
        //   {
        //     path: 'senderId',
        //     select: 'first_name last_name profile_picture',
        //     model: userModel,
        //   },
        // ],
      };

      let chats = await chatMessagesModel.paginate(
        {
          $text: { $search: req.body.search },
          chatId: req.params.chatId,
          system: { $exists: false },
        },
        option,
      );

      chats = JSON.parse(JSON.stringify(chats));
      chats.chatDetails = getChat;

      let accountId = getChat.participants.filter((data) => {
        return data !== req.user.id;
      });

      accountId = accountId[0];

      for (i in chats.docs) {
        if (chats.docs[i].senderId === req.user.id) {
          chats.docs[i].receiverId = accountId;
        } else if (chats.docs[i].senderId === accountId) {
          chats.docs[i].receiverId = req.user.id;
        }
      }

      res.status(200).json(chats);
    } else {
      res
        .status(400)
        .json({ message: "Something went wrong, can't find conversation" });
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: 'Something went wrong..', error });
  }
};

const getChats = async (req, res) => {
  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { updatedAt: -1 },
    };

    let query = {};

    if (req.query.blocked) {
      query = {
        participants: req.user.id,
        blocked: req.user.id,
      };
    } else {
      query = {
        participants: req.user.id,
        activeChat: req.user.id,
      };
    }

    let chats = await chatsModel.paginate(query, option);
    chats = JSON.parse(JSON.stringify(chats));

    const processedChats = await Promise.all(
      chats.docs.map(async (chat) => {
        const participantId =
          chat.participants[0] === req.user.id
            ? chat.participants[1]
            : chat.participants[0];

        const userPromise = userModel.findById(participantId).exec();
        const mutedPromise = mutedNotificationsModel
          .findOne({
            userId: req.user.id,
            contentId: participantId,
            contentType: 'user',
          })
          .exec();
        const lastMessagePromise = chatMessagesModel
          .findOne({ chatId: chat._id })
          .sort({ createdAt: -1 })
          .exec();

        const [user, muted, lastMessage] = await Promise.all([
          userPromise,
          mutedPromise,
          lastMessagePromise,
        ]);

        chat.accountId = {
          first_name: user.first_name,
          last_name: user.last_name,
          profile_picture: user.profile_picture
            ? GOOGLE_CLOUD_PROFILE_PICTURE_BASE_URL + user.profile_picture
            : null,
          _id: user._id,
        };

        chat.muted = !!muted;

        if (lastMessage) {
          chat.lastMessage =
            lastMessage.message.length >= 30
              ? `${lastMessage.message.substring(0, 30)}...`
              : lastMessage.message;
          chat.lastMessageDate = lastMessage.createdAt;

          const participantIndex =
            chat.participants[0] === req.user.id ? 0 : 1;

          if (chat.lastRead[participantIndex]) {
            const unreadMessagesCount = await chatMessagesModel
              .countDocuments({
                chatId: chat._id,
                createdAt: {
                  $gt: new Date(chat.lastRead[participantIndex]),
                },
              })
              .exec();

            chat.unreadMessages = unreadMessagesCount || 0;
          }
        } else {
          chat.unreadMessages = 0;
          chat.lastMessage = '';
        }

        return chat;
      })
    );

    res.status(200).json({ chats: { docs: processedChats }, message: 'Loaded Chats Successfully!' });
  } catch (error) {
    console.log(error, 'why?');
    res.status(400).json({ message: error.message });
  }
};


const getBotChats = async (req, res) => {
  res.status(200).json({
    chats: {
      docs: [
        {
          message:
            'This is a update message for all user for all features coming to pennytots our next update',
          type: 'message',
          _id: '1',
          chatId: '10',
          senderId: '11',
          createdAt: '2022-04-14T14:16:33.637Z',
          updatedAt: '2022-04-14T14:16:33.637Z',
          __v: 0,
          receiverId: req.user.id,
        },
      ],
      totalDocs: 8,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
      chatDetails: {
        participants: ['61bb5cbb4986c1083f8957d4', '61bb611339335909ca058e52'],
        blocked: [],
        isCleared: false,
        activeChat: ['61bb611339335909ca058e52', '61bb5cbb4986c1083f8957d4'],
        _id: '62581022791bb9975da8e074',
        createdAt: '2022-04-14T12:14:26.483Z',
        updatedAt: '2022-04-14T19:08:13.501Z',
        __v: 0,
      },
    },
  });
};

const blockChat = async (req, res) => {
  try {
    let getChat = await chatsModel.findOne({
      participants: { $all: [req.params.accountId, req.user.id] },
    });

    if (getChat) {
      if (!getChat.blocked.includes(req.user.id)) {
        await chatsModel.updateOne(
          { _id: getChat._id },
          { $push: { blocked: req.user.id } },
        );
      }

      res.status(200).json({
        message: 'Blocked successfully',
      });
    } else {
      res.status(400).json({
        message: 'Cannot find chat',
      });
      return;
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error,
    });
    return;
  }
};

const unblockChat = async (req, res) => {
  try {
    let getChat = await chatsModel.findOne({
      participants: { $all: [req.params.accountId, req.user.id] },
    });

    if (getChat) {
      if (getChat.blocked.includes(req.user.id)) {
        await chatsModel.updateOne(
          { _id: getChat._id },
          { $pull: { blocked: req.user.id } },
        );
      }

      res.status(200).json({
        message: 'Chat has been unblocked successfully',
      });
    } else {
      res.status(400).json({
        message: 'Cannot find chat',
      });
      return;
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error,
    });
    return;
  }
};

const removeFromActiveChat = async (req, res) => {
  try {
    let getChat = await chatsModel.findOne({
      participants: { $all: [req.params.accountId, req.user.id] },
    });

    if (getChat) {
      await chatsModel.updateOne(
        { _id: getChat._id },
        { $pull: { activeChat: req.user.id } },
      );

      res.status(200).json({
        message: 'Chat has been removed from list',
      });
    } else {
      res.status(400).json({
        message: 'Cannot find chat',
      });
      return;
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error,
    });
    return;
  }
};

const uploadChatImageAttachment = async (req, res) => {
  try {
    if (!req.params.chatId) {
      throw 'ChatId is required';
    }
    let imageFolder = '';
    let chatType = req.body.type;

    if (chatType == 'chat') {
      imageFolder = process.env.CHAT_IMAGE_DIRECTORY + '/';
    } else if (chatType == 'group') {
      imageFolder = process.env.GROUP_IMAGE_DIRECTORY + '/';
    } else {
      throw 'Please add a type of either chat or group';
    }

    let getData = null;

    if (chatType == 'chat') {
      getData = await chatsModel.findById(req.params.chatId);
    } else if (chatType == 'group') {
      getData = await groupsModel.findById(req.params.chatId);
    }

    if (!getData) {
      throw 'Cant find group or chat';
    }

    const source =
      chatType == 'chat'
        ? 'PRIVATE_CHAT_MEDIA'
        : chatType == 'group'
        ? 'GROUP_CHAT_MEDIA'
        : null;

    await checkCredit({
      source,
      userId: req.user.id,
    });

    let fileName =
      new Date().getTime() +
      getData._id +
      req.user.id +
      path.extname(req.file.originalname);

    const blob = bucket.file(imageFolder + req.file.originalname);
    fs.createReadStream(req.file.path)
      .pipe(blob.createWriteStream())
      .on('error', (err) => {
        console.log(err);
        // res.status(400).json({ message: err });
        next(err);
      })
      .on('finish', async () => {
        await blob.rename(imageFolder + fileName);
        await bucket.file(imageFolder + fileName).makePublic();
        var io = req.app.get('socketio');

        if (chatType == 'chat') {
          let data = await chatMessagesModel.create({
            chatId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'image',
          });

          // socket.broadcast.emit('received', data);
          io.in(req.params.chatId).emit('chat-messages', data);
        } else if (chatType == 'group') {
          let data = await groupMessagesModel.create({
            groupId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'image',
          });

          data = JSON.parse(JSON.stringify(data));

          data.senderId = {
            _id: req.userAccount._id,
            first_name: req.userAccount.first_name,
            last_name: req.userAccount.last_name,
          };
          if (req.userAccount.profile_picture) {
            data.senderId.profile_picture = req.userAccount.profile_picture;
          }
          io.in('group-' + req.params.chatId).emit('group-messages', data);
        }

        await deductCredit({
          source,
          userId: req.user.id,
        });

        res.status(200).json({
          message: 'Picture Uploaded Successfully',
        });
      });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }

  return;
};

const uploadChatVideoAttachment = async (req, res) => {
  try {
    if (!req.params.chatId) {
      throw 'ChatId is required';
    }
    let videoFolder = '';
    let chatType = req.body.type;

    if (chatType == 'chat') {
      videoFolder = process.env.CHAT_VIDEO_DIRECTORY + '/';
    } else if (chatType == 'group') {
      videoFolder = process.env.GROUP_VIDEO_DIRECTORY + '/';
    } else {
      throw 'Please add a type of either chat or group';
    }

    let getData = null;

    if (chatType == 'chat') {
      getData = await chatsModel.findById(req.params.chatId);
    } else if (chatType == 'group') {
      getData = await groupsModel.findById(req.params.chatId);
    }

    if (!getData) {
      throw 'Cant find group or chat';
    }

    const source =
      chatType == 'chat'
        ? 'PRIVATE_CHAT_MEDIA'
        : chatType == 'group'
        ? 'GROUP_CHAT_MEDIA'
        : null;

    await checkCredit({
      source,
      userId: req.user.id,
    });

    let fileName =
      new Date().getTime() +
      getData._id +
      req.user.id +
      path.extname(req.file.originalname);

    const blob = bucket.file(videoFolder + req.file.originalname);
    fs.createReadStream(req.file.path)
      .pipe(blob.createWriteStream())
      .on('error', (err) => {
        console.log(err);
        // res.status(400).json({ message: err });
        next(err);
      })
      .on('finish', async () => {
        await blob.rename(videoFolder + fileName);
        await bucket.file(videoFolder + fileName).makePublic();
        var io = req.app.get('socketio');

        if (chatType == 'chat') {
          let data = await chatMessagesModel.create({
            chatId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'video',
          });

          // socket.broadcast.emit('received', data);
          io.in(req.params.chatId).emit('chat-messages', data);
        } else if (chatType == 'group') {
          let data = await groupMessagesModel.create({
            groupId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'video',
          });

          data = JSON.parse(JSON.stringify(data));

          data.senderId = {
            _id: req.userAccount._id,
            first_name: req.userAccount.first_name,
            last_name: req.userAccount.last_name,
          };
          if (req.userAccount.profile_picture) {
            data.senderId.profile_picture = req.userAccount.profile_picture;
          }
          io.in('group-' + req.params.chatId).emit('group-messages', data);
        }

        await deductCredit({
          source,
          userId: req.user.id,
        });

        res.status(200).json({
          message: 'Video Uploaded Successfully',
        });
      });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }

  return;
};

const uploadChatDocumentAttachment = async (req, res) => {
  try {
    if (!req.params.chatId) {
      throw 'ChatId is required';
    }
    let documentFolder = '';
    let chatType = req.body.type;

    if (chatType == 'chat') {
      documentFolder = process.env.CHAT_DOCUMENT_DIRECTORY + '/';
    } else if (chatType == 'group') {
      documentFolder = process.env.GROUP_DOCUMENT_DIRECTORY + '/';
    } else {
      throw 'Please add a type of either chat or group';
    }

    let getData = null;

    if (chatType == 'chat') {
      getData = await chatsModel.findById(req.params.chatId);
    } else if (chatType == 'group') {
      getData = await groupsModel.findById(req.params.chatId);
    }

    if (!getData) {
      throw 'Cant find group or chat';
    }

    const source =
      chatType == 'chat'
        ? 'PRIVATE_CHAT_MEDIA'
        : chatType == 'group'
        ? 'GROUP_CHAT_MEDIA'
        : null;

    await checkCredit({
      source,
      userId: req.user.id,
    });

    let fileName =
      new Date().getTime() +
      getData._id +
      req.user.id +
      path.extname(req.file.originalname);

    const blob = bucket.file(documentFolder + req.file.originalname);
    fs.createReadStream(req.file.path)
      .pipe(blob.createWriteStream())
      .on('error', (err) => {
        console.log(err);
        // res.status(400).json({ message: err });
        next(err);
      })
      .on('finish', async () => {
        await blob.rename(documentFolder + fileName);
        await bucket.file(documentFolder + fileName).makePublic();
        var io = req.app.get('socketio');

        if (chatType == 'chat') {
          let data = await chatMessagesModel.create({
            chatId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'document',
          });

          io.in(req.params.chatId).emit('chat-messages', data);
        } else if (chatType == 'group') {
          let data = await groupMessagesModel.create({
            groupId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'document',
          });

          data = JSON.parse(JSON.stringify(data));

          data.senderId = {
            _id: req.userAccount._id,
            first_name: req.userAccount.first_name,
            last_name: req.userAccount.last_name,
          };
          if (req.userAccount.profile_picture) {
            data.senderId.profile_picture = req.userAccount.profile_picture;
          }
          io.in('group-' + req.params.chatId).emit('group-messages', data);
        }

        await deductCredit({
          source,
          userId: req.user.id,
        });

        res.status(200).json({
          message: 'Document Uploaded Successfully',
        });
      });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }

  return;
};

const uploadChatAudioAttachment = async (req, res) => {
  try {
    if (!req.params.chatId) {
      throw 'ChatId is required';
    }
    let audioFolder = '';
    let chatType = req.body.type;

    if (chatType == 'chat') {
      audioFolder = process.env.CHAT_AUDIO_DIRECTORY + '/';
    } else if (chatType == 'group') {
      audioFolder = process.env.GROUP_AUDIO_DIRECTORY + '/';
    } else {
      throw 'Please add a type of either chat or group';
    }

    let getData = null;

    if (chatType == 'chat') {
      getData = await chatsModel.findById(req.params.chatId);
    } else if (chatType == 'group') {
      getData = await groupsModel.findById(req.params.chatId);
    }

    if (!getData) {
      throw 'Cant find group or chat';
    }

    const source =
      chatType == 'chat'
        ? 'PRIVATE_CHAT_MEDIA'
        : chatType == 'group'
        ? 'GROUP_CHAT_MEDIA'
        : null;

    await checkCredit({
      source,
      userId: req.user.id,
    });

    let fileName =
      new Date().getTime() +
      getData._id +
      req.user.id +
      path.extname(req.file.originalname);

    const blob = bucket.file(audioFolder + req.file.originalname);
    fs.createReadStream(req.file.path)
      .pipe(blob.createWriteStream())
      .on('error', (err) => {
        console.log(err);
        // res.status(400).json({ message: err });
        next(err);
      })
      .on('finish', async () => {
        await blob.rename(audioFolder + fileName);
        await bucket.file(audioFolder + fileName).makePublic();
        var io = req.app.get('socketio');

        if (chatType == 'chat') {
          let data = await chatMessagesModel.create({
            chatId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'audio',
          });

          io.in(req.params.chatId).emit('chat-messages', data);
        } else if (chatType == 'group') {
          let data = await groupMessagesModel.create({
            groupId: req.params.chatId,
            senderId: req.user.id,
            message: req.body.message,
            attachment: fileName,
            attachmentSize: req.file.size,
            type: 'audio',
          });

          data = JSON.parse(JSON.stringify(data));
          data.senderId = {
            _id: req.userAccount._id,
            first_name: req.userAccount.first_name,
            last_name: req.userAccount.last_name,
          };
          if (req.userAccount.profile_picture) {
            data.senderId.profile_picture = req.userAccount.profile_picture;
          }
          io.in('group-' + req.params.chatId).emit('group-messages', data);
        }

        await deductCredit({
          source,
          userId: req.user.id,
        });

        res.status(200).json({
          message: 'Audio Uploaded Successfully',
        });
      });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

const uploadChatContactAttachment = async (req, res) => {
  try {
    if (!req.params.chatId) {
      throw 'ChatId is required';
    }

    let chatType = req.body.type;

    if (chatType != 'chat' && chatType != 'group') {
      res.status(400).json({
        message: 'Please add a type of either chat or group',
      });
      return;
    }

    let getData = null;

    if (chatType == 'chat') {
      getData = await chatsModel.findById(req.params.chatId);
    } else if (chatType == 'group') {
      getData = await groupsModel.findById(req.params.chatId);
    }

    if (!getData) {
      throw 'Cant find group or chat';
    }

    const source =
      chatType == 'chat'
        ? 'PRIVATE_CHAT_MEDIA'
        : chatType == 'group'
        ? 'GROUP_CHAT_MEDIA'
        : null;

    await checkCredit({
      source,
      userId: req.user.id,
    });

    var io = req.app.get('socketio');

    if (chatType == 'chat') {
      let data = await chatMessagesModel.create({
        chatId: req.params.chatId,
        senderId: req.user.id,
        message: req.body.message,
        attachment: req.body.contact,
        type: 'contact',
      });

      // socket.broadcast.emit('received', data);
      io.in(req.params.chatId).emit('chat-messages', data);
    } else if (chatType == 'group') {
      let data = await groupMessagesModel.create({
        groupId: req.params.chatId,
        senderId: req.user.id,
        message: req.body.message,
        attachment: req.body.contact,
        type: 'contact',
      });

      data = JSON.parse(JSON.stringify(data));

      data.senderId = {
        _id: req.userAccount._id,
        first_name: req.userAccount.first_name,
        last_name: req.userAccount.last_name,
      };
      if (req.userAccount.profile_picture) {
        data.senderId.profile_picture = req.userAccount.profile_picture;
      }
      io.in('group-' + req.params.chatId).emit('group-messages', data);
    }

    await deductCredit({
      source,
      userId: req.user.id,
    });

    res.status(200).json({
      message: 'Contact Uploaded Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }

  return;
};

const deleteMessage = async (req, res) => {
  try {
    let message = await chatMessagesModel.findById(req.params.chatMessageId);

    if (!message) {
      res.status(400).json({ message: 'Cant find message' });
      return;
    }

    if (message.senderId != req.user.id) {
      res.status(400).json({ message: 'You cant perform this operation' });
      return;
    }

    if (message.attachment) {
      let attachmentFolder = null;
      if (message.type == 'image') {
        attachmentFolder = process.env.CHAT_IMAGE_DIRECTORY + '/';
      } else if (message.type == 'audio') {
        attachmentFolder = process.env.CHAT_AUDIO_DIRECTORY + '/';
      } else if (message.type == 'video') {
        attachmentFolder = process.env.CHAT_VIDEO_DIRECTORY + '/';
      } else if (message.type == 'document') {
        attachmentFolder = process.env.CHAT_DOCUMENT_DIRECTORY + '/';
      }

      await bucket.file(attachmentFolder + message.attachment).delete();
    }

    let deletedMessage = JSON.parse(JSON.stringify(message));

    await message.deleteOne();
    var io = req.app.get('socketio');

    io.in(deletedMessage.chatId).emit('chat-messages', {
      deletedMessage: true,
      messageId: deletedMessage._id,
    });

    res.status(200).json({ message: 'Deleted successfully' });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};

module.exports = {
  getChatMessages,
  getBotChats,
  loadChats,
  getChats,
  searchChatMessages,
  removeFromActiveChat,
  blockChat,
  unblockChat,
  uploadChatImageAttachment,
  uploadChatVideoAttachment,
  uploadChatDocumentAttachment,
  uploadChatAudioAttachment,
  uploadChatContactAttachment,
  deleteMessage,
};

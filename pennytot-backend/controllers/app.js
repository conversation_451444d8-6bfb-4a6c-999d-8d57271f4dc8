const {
  userModel,
  topicsModel,
  topicCommentsModel,
  topicLikesModel,
  groupsModel,
  groupMessagesModel,
  chatsModel,
  mutedNotificationsModel,
  unreadNotificationsModel,
  helpdeskModel,
  helpdeskMessageModel,
  topicSubCommentsModel,
} = require('../models');
const sendEmail = require('../utils/sendEmail');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);
var postmark = require('postmark');
var client = new postmark.ServerClient('************************************');

const search = async (req, res) => {
  if (!req.body.search) {
    res.status(400).json({ message: 'Search field cannot be empty' });
    return;
  }

  if (!req.body.searchType) {
    res.status(400).json({ message: 'Please define a search type' });
    return;
  }

  if (req.body.searchType === 'topics') {
    try {
      const option = {
        page: req.query.page || 1,
        limit: req.query.limit || process.env.PAGING_LIMIT,
        populate: {
          path: 'userId',
          select: 'first_name last_name profile_picture',
          model: userModel,
        },
      };
  
      const topics = await topicsModel.paginate(
        {
          $text: { $search: req.body.search },
          appropriate: true,
        },
        option,
      );
  
      const processedTopics = await Promise.all(
        topics.docs.map(async (topic) => {
          const [likes, comments, checkUserLike] = await Promise.all([
            topicLikesModel.countDocuments({ topicId: topic._id }).exec(),
            topicCommentsModel.countDocuments({ topicId: topic._id }).exec(),
            topicLikesModel.findOne({ topicId: topic._id, userId: req.user.id }).exec(),
          ]);
  
          topic.likes = likes;
          topic.noOfComments = comments;
          topic.isLiked = !!checkUserLike;
  
          return topic;
        })
      );
  
      topics.docs = processedTopics;
  
      res.status(200).json({ topics, message: 'Loaded Topics Successfully' });
    } catch (error) {
      console.log(error);
      res.status(400).json({ message: error.message });
    }
  }

  if (req.body.searchType === 'topic-comments' || req.body.searchType === 'topic-sub-comments') {
  try {
    const model = req.body.searchType === 'topic-comments' ? topicCommentsModel : topicSubCommentsModel;
    
    const option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      populate: {
        path: 'userId',
        select: 'first_name last_name profile_picture',
        model: userModel,
      },
    };

    const topicComments = await model.paginate(
      {
        $text: { $search: req.body.search },
      },
      option,
    );

    res.status(200).json({ topicComments, message: `Loaded ${req.body.searchType.replace('-', ' ')} Successfully` });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error.message });
  }
}
  
  else if (req.body.searchType === 'users') {
    try {
      let option = {
        page: req.query.page || 1,
        limit: req.query.limit || process.env.PAGING_LIMIT,
      };

      let users = await userModel.paginate(
        {
          $text: { $search: req.body.search },
        },
        option,
      );

      users = JSON.parse(JSON.stringify(users));

      res.status(200).json({ users, message: 'Users record found' });
    } catch (error) {
      res.status(400).json({ message: error });
    }
  } 
  if (req.body.searchType === 'groups') {
    try {
      const option = {
        page: req.query.page || 1,
        limit: req.query.limit || process.env.PAGING_LIMIT,
      };
  
      const groups = await groupsModel.paginate(
        {
          $text: { $search: req.body.search },
        },
        option,
      );
  
      const processedGroups = await Promise.all(
        groups.docs.map(async (group) => {
          const lastMessage = await groupMessagesModel
            .findOne({ groupId: group._id })
            .sort({ createdAt: -1 })
            .exec();
  
          if (lastMessage) {
            group.lastMessage = lastMessage.message.length >= 30
              ? `${lastMessage.message.substring(0, 30)}...`
              : lastMessage.message;
          } else {
            group.lastMessage = '';
          }
  
          return group;
        })
      );
  
      groups.docs = processedGroups;
  
      res.status(200).json({ groups, message: 'Groups record found' });
    } catch (error) {
      console.log(error);
      res.status(400).json({ message: error.message });
    }
  }
  
  if (req.body.searchType === 'chats') {
    try {
      const chatQuery = await chatsModel.aggregate([
        {
          $match: { participants: req.user.id },
        },
        { $unwind: '$participants' },
        {
          $group: {
            _id: '$participants',
          },
        },
        { $match: { _id: { $ne: req.user.id } } },
        { $project: { _id: { $toObjectId: '$_id' } } },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'users',
          },
        },
        {
          $match: {
            $or: [
              {
                'users.first_name': {
                  $regex: `.*${req.body.search}.*`,
                  $options: 'i', 
                },
              },
              {
                'users.last_name': {
                  $regex: `.*${req.body.search}.*`,
                  $options: 'i',  
                },
              },
            ],
          },
        },
        {
          $project: {
            _id: 1,
            'users.first_name': 1,
            'users.last_name': 1,
            'users.profile_picture': 1,
          },
        },
      ]);
  
      const chats = chatQuery.map((chat) => ({
        user: {
          ...chat.users[0],
          profile_picture: chat.users[0].profile_picture
            ? bucket.file(`${process.env.PROFILE_DIRECTORY}/${chat.users[0].profile_picture}`).publicUrl()
            : null,
        },
      }));
  
      res.status(200).json(chats);
    } catch (error) {
      console.log(error);
      res.status(400).json({ message: error.message });
    }
  }
  
  else {
    res.status(400).json({ message: 'Bad request, something went wrong' });
  }
};

const sendToHelpDesk = async (req, res) => {
  try {
    await helpdeskModel.create({
      userId: req.user.id,
      title: req.user.title,
    });

    // sendEmail(
    //   '<EMAIL>',
    //   'Support Help Ticket',
    //   {
    //     first_name: req.userAccount.first_name,
    //     last_name: req.userAccount.last_name,
    //     email: req.userAccount.email,
    //   },
    //   './template/helpdesk.handlebars',
    // );

    res.status(200).json({
      message:
        'Thank you for your feedback, you will get a response from us soon',
    });
  } catch (error) {
    res.status(400).json({ message: 'An error occurred' });
    return;
  }
};

const mutedNotifications = async (req, res) => {
  try {
    if (!req.body.type) {
      res.status(400).json({ message: 'Content Type is required' });
      return;
    }

    if (!req.body.contentId) {
      res.status(400).json({ message: 'ContentId is required' });
      return;
    }

    if (!req.body.action) {
      res.status(400).json({ message: 'Action type is required' });
      return;
    }

    let checkMutedContent = await mutedNotificationsModel.find({
      userId: req.user.id,
      contentId: req.body.contentId,
      contentType: req.body.type,
    });

    if (req.body.action == 'mute') {
      if (checkMutedContent && checkMutedContent.length != 0) {
        return res.status(400).json({
          message: `${req.body.type} has already been muted`,
        });
      }

      await mutedNotificationsModel.create({
        userId: req.user.id,
        contentId: req.body.contentId,
        contentType: req.body.type,
      });

      res.status(201).json({
        message: `${req.body.type} has been muted successfully`,
      });
    } else if (req.body.action == 'unmute') {
      if (checkMutedContent && checkMutedContent.length != 0) {
        await mutedNotificationsModel.remove({ _id: checkMutedContent[0]._id });

        return res.status(200).json({
          message: `${req.body.type} has been unmuted`,
        });
      } else {
        return res.status(400).json({
          message: `${req.body.type} is already unmuted`,
        });
      }
    } else {
      return res.status(400).json({
        message: "Action type is not valid, must be 'mute' or 'unmute'",
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      message: 'An Error Occurred',
    });
  }
};

const unreadNotifications = async (req, res) => {
  try {
    let notifications = await unreadNotificationsModel.findOne({
      userId: req.user.id,
    });

    if (!notifications) {
      let newNotification = await unreadNotificationsModel.create({
        userId: req.user.id,
      });
      return res.status(200).json({
        message: 'success',
        notifications: newNotification,
      });
    }

    res.status(200).json({
      message: 'success',
      notifications,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};

const clearReadNotifications = async (req, res) => {
  let readQuery = {};

  if (req.params.source == 'convos') {
    readQuery = { convos: [] };
  } else {
    return res.status(400).json({ message: 'source is required' });
  }

  try {
    await unreadNotificationsModel.updateOne(
      { userId: req.user.id },
      {
        $set: readQuery,
      },
    );
    return res.status(200).json({ message: 'success' });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ message: error });
  }
};

const reportAnIssue = async (req, res) => {
  try {
    sendEmail(
      // '<EMAIL>',
      '<EMAIL>',
      'New Issue Report',
      {
        name: req.body.name,
        email: req.body.email,
        content: req.body.content,
      },
      './template/report-an-issue.handlebars',
    );

    //   client
    //     .sendEmail({
    //       From: '<EMAIL>',
    //       To: '<EMAIL>',
    //       Subject: 'New App Issue Report!',
    //       HtmlBody: `<html>
    //     <head>
    //     </head>
    //     <body>
    //         <p>Full Name:<b> ${req.body.name} , </b></p>
    //         <p>Email: ${req.body.email},</p>
    //         <p>Message Content: ${req.body.content}</p>
    //     </body>
    // </html>`,
    //       TextBody: 'New App Issue Report!',
    //       MessageStream: 'outbound',
    //     })
    //     .then((result) => {
    //       console.log('done');
    //     })
    //     .catch((err) => {
    //       console.log(err);
    //     });

    // console.log(x, '= checking');

    res.status(200).json({ message: 'Sent Successfully' });
  } catch (error) {
    return res.status(400).json({ message: error ? error.message : error });
  }
};

module.exports = {
  search,
  sendToHelpDesk,
  mutedNotifications,
  unreadNotifications,
  clearReadNotifications,
  reportAnIssue,
};

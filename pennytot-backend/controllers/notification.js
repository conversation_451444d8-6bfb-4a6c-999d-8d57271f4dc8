// NotificationController.js
const { Expo } = require('expo-server-sdk');
const {QuestionModel} = require('../models/index'); // Ensure this path is correct
const {userModel} = require('../models'); // Ensure this path is correct

let expo = new Expo();

const sendNotification = async (req, res) => {
  try {
    // Fetch a random question
    const questionCount = await QuestionModel.countDocuments();
    const randomIndex = Math.floor(Math.random() * questionCount);
    const randomQuestion = await QuestionModel.findOne().skip(randomIndex);

    if (!randomQuestion) {
      return res.status(404).json({ error: 'No question found' });
    }

    const { token } = req.body; // Assuming the token is sent in the request body

    if (!Expo.isExpoPushToken(token)) {
      return res.status(400).send({ error: 'Invalid Expo push token' });
    }
    console.log(randomQuestion)

    let messages = [{
      to: token,
      sound: 'default',
      title: 'New Quiz Question!',
      body: randomQuestion.question, // Generic message indicating a new question
      data: { 
        type: 'quiz',
        questionData: randomQuestion // Send the entire question object as part of the data
      },
    }];

    let chunks = expo.chunkPushNotifications(messages);
    let tickets = [];



    for (let chunk of chunks) {
      let ticketChunk = await expo.sendPushNotificationsAsync(chunk);
      tickets.push(...ticketChunk);
    }

    res.status(200).send({ message: 'Notification sent successfully', tickets });
  } catch (error) {
    console.error(error);
    res.status(500).send({ error: 'Error sending notification' });
  }
};

const sendAllNotification = async () => {
  
  try {
  const questionCount = await QuestionModel.countDocuments();
  const randomIndex = Math.floor(Math.random() * questionCount);
  const randomQuestion = await QuestionModel.findOne().skip(randomIndex);

  if (!randomQuestion) {
    console.error('No question found');
    return;
  }

  // Fetch all user tokens
  const users = await userModel.find();
  let messages = [];

  for (let user of users) {
    const { device_token } = user;
    if (device_token && Expo.isExpoPushToken(device_token)) {
      messages.push({
        to: device_token,
        sound: 'default',
        title: 'Pennytots',
        body: randomQuestion.question,
        data: {
          type: 'quiz',
          questionData: randomQuestion
        },
      });
    }
  }

  let chunks = expo.chunkPushNotifications(messages);
  let tickets = [];

  for (let chunk of chunks) {
    let ticketChunk = await expo.sendPushNotificationsAsync(chunk);
    tickets.push(...ticketChunk);
  }

  console.log('Notifications sent with tickets:', tickets);
} catch (error) {
  console.error('Error during notification sending:', error);
}
}

const sendDailyNotifications = async (req, res) => {
  try {
      // Log the start of the process
      const questionCount = await QuestionModel.countDocuments();
      if (questionCount === 0) {
          console.error('No questions available');
          return res.status(404).json({ error: 'No questions available' });
      }
      const randomIndex = Math.floor(Math.random() * questionCount);
      const randomQuestion = await QuestionModel.findOne().skip(randomIndex);

      if (!randomQuestion) {
          console.error('No question found');
          return res.status(404).json({ error: 'No question found' });
      }

      // Fetch all user tokens
      const users = await userModel.find();
      console.log(users,"users",users.length)
      let messages = [];
      for (let user of users) {
          const { device_token } = user;
          console.log(device_token,"device token")
          if (device_token && Expo.isExpoPushToken(device_token)) {
              messages.push({
                  to: device_token,
                  sound: 'default',
                  title: 'New Quiz Question!',
                  body: randomQuestion.question,
                  data: {
                      type: 'quiz',
                      questionData: randomQuestion
                  },
              });
          }
      }

      // Log the prepared messages count
      console.log(`Prepared ${messages.length} messages for sending`);

      let chunks = expo.chunkPushNotifications(messages);
      let tickets = [];
      for (let chunk of chunks) {
          let ticketChunk = await expo.sendPushNotificationsAsync(chunk);
          tickets.push(...ticketChunk);
      }

      // Log the result of sent notifications
      console.log('Notifications sent with tickets:', tickets);
      return res.status(200).json({ message: 'Notifications sent successfully', tickets });
  } catch (error) {
      console.error('Error during notification sending:', error);
      return res.status(500).json({ error: 'Internal Server Error' });
  }
};

module.exports = {
  sendNotification,
  sendAllNotification,
  sendDailyNotifications
};

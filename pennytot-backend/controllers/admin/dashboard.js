const { userModel, groupsModel, topicsModel } = require('../../models');

const dashboard = async (req, res) => {
  try {
    let totalTopics = await topicsModel.countDocuments();

    let totalUsers = await userModel.countDocuments();

    let totalGroups = await groupsModel.countDocuments();

    return res.status(200).json({
      totalTopics,
      totalUsers,
      totalGroups,
    });
  } catch (error) {
    console.log(error);

    return res.status(400).json({
      message: error,
    });
  }
};

module.exports = {
  dashboard,
};

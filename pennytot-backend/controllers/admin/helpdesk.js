const {
  helpdeskModel,
  userModel,
  helpdeskMessageModel,
} = require('../../models');
const helpdeskCategories = require('../../constants/helpdesk-categories');
const path = require('path');
const fs = require('fs');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const getHelpDeskTickets = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    populate: {
      path: 'userId',
      select: 'first_name last_name profile_picture',
      model: userModel,
    },
    sort: { _id: -1 },
  };

  let query = {};

  if (req.query.category) {
    query.category = req.query.category;
  }

  if (req.query.closed) {
    query.closed = req.query.closed;
  }

  if (req.query.search) {
    query.$text = { $search: req.query.search };
  }

  try {
    let tickets = await helpdeskModel.paginate(query, option);

    tickets = JSON.parse(JSON.stringify(tickets));

    res.status(200).json({
      tickets,
      message: 'Loaded Tickets Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const getHelpDeskMessages = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    //   populate: {
    //     path: 'userId',
    //     select: 'first_name last_name profile_picture',
    //     model: userModel,
    //   },
    sort: { _id: -1 },
  };

  try {
    let messages = await helpdeskMessageModel.paginate(
      { helpdeskId: req.params.helpdeskId },
      option,
    );

    messages = JSON.parse(JSON.stringify(messages));

    for (i in messages.docs) {
      let message = messages.docs[i];

      if (message.image) {
        const options = {
          version: 'v4',
          action: 'read',
          expires: Date.now() + 1000 * 60 * 60, // one hour
        };

        const [url] = await bucket
          .file(process.env.HELDPESK_IMAGE_DIRECTORY + '/' + message.image)
          .getSignedUrl(options);

        message.image = url;
      }
    }

    res.status(200).json({
      messages,
      message: 'Loaded Messages Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const getHelpDeskCategories = async (req, res) => {
  try {
    res.status(200).json({
      helpdeskCategories,
      message: 'Loaded Helpdesk Categories Successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const replyHelpDeskMessage = async (req, res) => {
  try {
    imageFolder = process.env.HELDPESK_IMAGE_DIRECTORY + '/';

    let helpdeskMessage = await helpdeskMessageModel.create({
      helpdeskId: req.params.helpdeskId,
      adminId: req.user.id,
      senderId: req.user.id,
      message: req.body.message,
    });

    if (req.file) {
      let fileName =
        new Date().getTime() +
        helpdeskMessage._id +
        path.extname(req.file.originalname);
      const blob = bucket.file(imageFolder + req.file.originalname);
      fs.createReadStream(req.file.path)
        .pipe(blob.createWriteStream())
        .on('error', (err) => {
          console.log(err);
          //  res.status(400).json({ message: err });
          next(err);
        })
        .on('finish', async () => {
          try {
            await blob.rename(imageFolder + fileName);
            // await bucket.file(imageFolder + fileName).makePublic();

            let update = await helpdeskMessageModel.findOneAndUpdate(
              { _id: helpdeskMessage._id },
              {
                $set: {
                  image: fileName,
                },
              },
              { new: true },
            );

            res.status(200).json({
              message: 'Reply sent successfully',
              helpdeskMessage: update,
            });
          } catch (error) {
            console.log(error);

            res.status(400).json({
              message: 'An Error Occurred, failed to upload image',
              data: error,
            });
          }
        });
    } else {
      res.status(200).json({
        message: 'Reply sent successfully',
      });
    }
  } catch (error) {
    res.status(400).json({ message: 'An error occurred' });
    return;
  }
};

module.exports = {
  getHelpDeskMessages,
  getHelpDeskTickets,
  getHelpDeskCategories,
  replyHelpDeskMessage,
};

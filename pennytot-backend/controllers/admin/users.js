const { CREDIT_PER_NAIRA } = require('../../constants/credit');
const {
  userModel,
  flaggedUsersModel,
  chatsModel,
  chatMessagesModel,
  topicsModel,
  groupsModel,
  topicCommentsModel,
  CreditModel,
  TransactionModel,
} = require('../../models');

const getUsers = async (req, res) => {
  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { updatedAt: -1 },
    };

    let query = {};

    if (req.query.search) {
      query = { $text: { $search: req.query.search } };
    }

    if (req.query.type === 'active') {
      query.suspended = false;
    } else if (req.query.type === 'suspended') {
      query.suspended = true;
    } else if (req.query.type === 'flagged') {
      query.reportedAndFlagged = true;
    }

    console.log(query, 'checking query');

    let users = await userModel.paginate(query, option);

    users = JSON.parse(JSON.stringify(users));

    res.status(200).json({ users, message: 'Users Loaded Successfully!' });
  } catch (error) {
    res.status(400).json({ message: error });
  }
};

const updateUser = async (req, res) => {
  if (!req.params.userId) {
    return res.status(400).json({
      message: 'userId is required',
    });
  }

  if (!req.query.type) {
    return res.status(400).json({
      message: 'type is required',
    });
  }

  try {
    let user = await userModel.findById(req.params.userId);

    if (user) {
      let query = {};

      if (req.query.type === 'suspend') {
        query = { suspended: true };
      } else if (req.query.type === 'unsuspend') {
        query = { suspended: false };
      } else if (req.query.type === 'unflag') {
        query = { suspended: false, reportedAndFlagged: false };
      } else if (req.query.type === 'flag') {
        query = { suspended: true, reportedAndFlagged: true };
      }

      await userModel.updateOne(
        { _id: req.params.userId },
        {
          $set: query,
        },
      );

      return res.status(200).json({
        message: 'User has been updated successfully',
      });
    } else {
      return res.status(400).json({
        message: 'User does not exist',
      });
    }
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const getUserFlaggedReports = async (req, res) => {
  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { updatedAt: -1 },
      populate: {
        path: 'reporterId',
        select: 'first_name last_name profile_picture',
        model: userModel,
      },
    };

    let query = {
      userId: req.params.userId,
    };

    let flagged = await flaggedUsersModel.paginate(query, option);

    flaggedReports = JSON.parse(JSON.stringify(flagged));

    for (i in flaggedReports.docs) {
      try {
        let getChat = await chatsModel.findOne({
          participants: {
            $all: [req.params.userId, flaggedReports.docs[i].reporterId._id],
          },
        });

        flaggedReports.docs[i].chats = getChat;
      } catch (error) {
        console.log(error);
        res.status(400).json({ message: error });
      }
    }

    res.status(200).json({
      flaggedReports,
      message: 'User Flag Reports Loaded Successfully!',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error });
  }
};

const getChatById = async (req, res) => {
  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { createdAt: -1 },
      populate: {
        path: 'senderId',
        select: 'first_name last_name profile_picture',
        model: userModel,
      },
    };

    let chats = await chatMessagesModel.paginate(
      {
        chatId: req.params.chatId,
      },
      option,
    );

    chats = JSON.parse(JSON.stringify(chats));

    //Update last seen for chat
    // let participantIndex = 0;
    // if (getChat.participants[0] == req.user.id) {
    //   participantIndex = 0;
    // } else {
    //   participantIndex = 1;
    // }

    // if (
    //   chats.docs.length > 0 &&
    //   chats.docs[0].createdAt !== getChat.lastRead[participantIndex]
    // ) {
    //   await chatsModel.updateOne(
    //     { _id: getChat._id },
    //     {
    //       $set: { [`lastRead.${participantIndex}`]: chats.docs[0].createdAt },
    //     },
    //   );
    // }

    // for (i in chats.docs) {
    //   if (chats.docs[i].senderId === req.user.id) {
    //     chats.docs[i].receiverId = req.params.accountId;
    //   } else if (chats.docs[i].senderId === req.user.accountId) {
    //     chats.docs[i].receiverId = req.user.id;
    //   }
    // }

    return res.status(200).json({ chats });
  } catch (error) {
    console.log(error);
    return res.status(400).json({ message: error });
  }
};

const getUserContents = async (req, res) => {
  try {
    if (req.query.contentType == 'topics') {
      let option = {
        page: req.query.page || 1,
        limit: req.query.limit || process.env.PAGING_LIMIT,
        sort: { updatedAt: -1 },
        populate: [
          // {
          //   path: 'reporterId',
          //   select: 'first_name last_name profile_picture',
          //   model: userModel,
          // },
          {
            path: 'userId',
            select: 'first_name last_name profile_picture',
            model: userModel,
          },
        ],
      };

      let query = {
        userId: req.params.userId,
      };

      let topics = await topicsModel.paginate(query, option);

      topics = JSON.parse(JSON.stringify(topics));

      res.status(200).json({ message: 'loaded topics', topics });
    } else if (req.query.contentType == 'groups') {
      let option = {
        page: req.query.page || 1,
        limit: req.query.limit || process.env.PAGING_LIMIT,
        sort: { updatedAt: -1 },
        // populate: {
        //   path: 'reporterId',
        //   select: 'first_name last_name profile_picture',
        //   model: userModel,
        // },
      };

      let query = {
        createdBy: req.params.userId,
      };

      let groups = await groupsModel.paginate(query, option);

      groups = JSON.parse(JSON.stringify(groups));

      res.status(200).json({ message: 'loaded groups', groups });
    } else if (req.query.contentType == 'topic-comments') {
      let option = {
        page: req.query.page || 1,
        limit: req.query.limit || process.env.PAGING_LIMIT,
        sort: { updatedAt: -1 },
        // populate: {
        //   path: 'reporterId',
        //   select: 'first_name last_name profile_picture',
        //   model: userModel,
        // },
      };

      let query = {
        userId: req.params.userId,
      };

      let comments = await topicCommentsModel.paginate(query, option);

      comments = JSON.parse(JSON.stringify(comments));

      res.status(200).json({ message: 'loaded comments', comments });
    } else {
      return res.status(400).json({ message: 'content type not set' });
    }
  } catch (error) {
    console.log(error);
    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

const getUserPennytots = async (req, res) => {
  try {
    const credit = await CreditModel.findOne({
      userId: req.params.userId,
    });

    if (!credit) {
      throw 'Could not fetch user pennytots';
    }

    res.status(200).json({
      credit,
      message: 'Success',
    });
  } catch (error) {
    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

const updateUserPennytots = async (req, res) => {
  try {
    await CreditModel.updateOne(
      {
        userId: req.params.userId,
      },
      {
        $inc: {
          amount: req.body.amount,
        },
      },
    );

    await TransactionModel.create({
      userId: req.params.userId,
      source: 'admin',
      action: 'buy-credit',
      amount: req.body.amount * CREDIT_PER_NAIRA,
      status: 'success',
      data: {
        pennytots_added: req.body.amount,
      },
    });

    res.status(200).json({
      message: 'User Pennytots has been updated successfully',
    });
  } catch (error) {
    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

module.exports = {
  getUsers,
  updateUser,
  getUserFlaggedReports,
  getChatById,
  getUserContents,
  getUserPennytots,
  updateUserPennytots,
};

const { adminModel, adminConfigurationsModel } = require('../../models/admin');
const jwt = require('jsonwebtoken');
const sendEmail = require('../../utils/sendEmail');
const bcrypt = require('bcryptjs');
const { userModel } = require('../../models');

const setupAdminConfigurations = async (req, res) => {
  try {
    const getAdminConfiguration = await adminConfigurationsModel.findOne();

    if (!getAdminConfiguration) {
      let user = await userModel.create({
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        email: req.body.admin_email,
        phone_number: req.body.phone_number,
        country: req.body.country,
        password: req.body.admin_password,
        admin: true,
      });

      await adminModel.create({
        email: req.body.admin_email,
        permissions: [],
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        password: req.body.admin_password,
        active: true,
        super_admin: true,
      });

      await adminConfigurationsModel.create({
        activated: true,
        defaultAdminAppUserId: user._id,
      });

      res.status(200).json({ message: 'success' });
      return;
    }

    res.status(200).json({ message: 'Admin has been configured' });
  } catch (error) {
    console.log(error);

    return res.status(400).json({
      message: error,
    });
  }
};

const register = async (req, res) => {
  try {
    let admin = await adminModel.findOne({ email: req.body.email });

    if (admin && admin.active) {
      throw 'Account already activated';
    }

    let password = await bcrypt.hash(
      req.body.password,
      Number(process.env.BCRYPT_SALT),
    );

    await adminModel.updateOne(
      { email: req.body.email },
      {
        $set: {
          first_name: req.body.first_name,
          last_name: req.body.last_name,
          password: password,
          active: true,
        },
      },
    );

    res.status(200).json({
      message:
        'You account has been created successfully. pls login to your account',
    });
  } catch (error) {
    return res.status(400).json({
      message: error && error.message ? error.message : error,
    });
  }
};

const login = (req, res) => {
  let email = req.body.email;
  let password = req.body.password;

  adminModel.findOne({ email: email }).then((user) => {
    if (user) {
      user.comparePassword(password, async function (err, result) {
        if (err) {
          res.json({
            message: err,
          });
          return;
        }
        if (result) {
          let token = jwt.sign(
            { id: user._id },
            process.env.ADMIN_TOKEN_SECRET,
            {
              expiresIn: process.env.ADMIN_TOKEN_LIFE,
            },
          );

          user = JSON.parse(JSON.stringify(user));

          if (user.suspended) {
            res.status(403).json({ message: 'Your account is suspended' });
            return;
          }

          res.json({
            id: user._id,
            ...user,
            message: 'Login Successful!',
            token,
          });
        } else {
          res.status(403).json({ message: 'Invalid Password' });
        }
      });
    } else {
      res.status(404).json({
        message: 'User does not exist',
      });
    }
  });
};

const getAdmins = async (req, res) => {
  try {
    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { updatedAt: -1 },
    };

    let admins = await adminModel.paginate({}, option);

    admins = JSON.parse(JSON.stringify(admins));

    res.status(200).json({ admins, message: 'Admin Loaded Successfully!' });
  } catch (error) {
    console.log(error, 'why?');
    res.status(400).json({ message: error });
  }
};

const invite = async (req, res) => {
  try {
    let user = new adminModel({
      email: req.body.email,
      permissions: req.body.permissions,
    });

    user.save();

    sendEmail(
      req.body.email,
      'Invitation as a Pennytots Admin',
      {
        email: req.body.email,
      },
      './template/adminInvitation.handlebars',
    );

    res.status(200).json({
      message: 'User have been successfully invited',
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({
      message: 'error',
    });
  }
};

module.exports = {
  register,
  login,
  getAdmins,
  invite,
  setupAdminConfigurations,
};

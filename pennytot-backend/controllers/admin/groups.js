const { groupsModel, userModel, groupMessagesModel } = require('../../models');
const { treatedContentsModel, adminModel } = require('../../models/admin');

const treatReportedGroup = async (req, res) => {
  try {
    await groupsModel.updateOne(
      { _id: req.params.groupId },
      { $set: { appropriate: req.body.appropriate } },
    );

    await treatedContentsModel.create({
      adminId: req.user.id,
      contentId: req.params.groupId,
      contentType: 'group',
    });

    res.status(200).json({
      message: 'Success',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const flaggedGroups = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    sort: { createdAt: -1 },
  };

  let groups = await groupsModel.paginate({ appropriate: false }, option);

  groups = JSON.parse(JSON.stringify(groups));

  return res.status(200).json({ groups, message: 'Loaded Successfully' });
};

const treatedGroups = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    sort: { createdAt: -1 },
    populate: [
      {
        path: 'contentId',
        select: 'name image',
        model: groupsModel,
      },
      {
        path: 'adminId',
        model: adminModel,
        select: 'first_name last_name',
      },
    ],
  };

  let groups = await treatedContentsModel.paginate(
    { contentType: 'group' },
    option,
  );

  groups = JSON.parse(JSON.stringify(groups));

  return res.status(200).json({ groups, message: 'Loaded Successfully' });
};

const getGroupMessages = async (req, res) => {
  try {
    let getGroup = await groupsModel.findById(req.params.groupId);

    let option = {
      page: req.query.page || 1,
      limit: req.query.limit || process.env.PAGING_LIMIT,
      sort: { createdAt: -1 },
      populate: [
        {
          path: 'senderId',
          select: 'first_name last_name profile_picture',
          model: userModel,
        },
      ],
    };

    if (getGroup) {
      let chats = await groupMessagesModel.paginate(
        {
          groupId: req.params.groupId,
        },
        option,
      );
      chats = JSON.parse(JSON.stringify(chats));
      chats.groupId = req.params.groupId;

      return res.status(200).json({ groupChats: chats });
    } else {
      return res
        .status(400)
        .json({ message: 'Group does not exist or have been deleted' });
    }
  } catch (err) {
    console.log(err);
    return res.status(400).json({ err });
  }
};

module.exports = {
  treatReportedGroup,
  flaggedGroups,
  getGroupMessages,
  treatedGroups,
};

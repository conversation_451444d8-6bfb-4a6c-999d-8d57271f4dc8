const {
  userModel,
  topicsModel,
  topicLikesModel,
  topicCommentsModel,
} = require('../../models');
const {
  adminConfigurationsModel,
  treatedContentsModel,
  adminModel,
} = require('../../models/admin');
const path = require('path');
const fs = require('fs');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const getTopics = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    populate: [
      {
        path: 'contentId',
        // select: 'first_name last_name profile_picture',
        model: topicsModel,
      },
      {
        path: 'adminId',
        model: adminModel,
        select: 'first_name last_name',
      },
    ],
    sort: { updatedAt: -1 },
  };

  try {
    let topics = await treatedContentsModel.paginate(
      {
        contentType: 'topic',
        treated:
          req.query.type === 'untreated'
            ? false
            : req.query.type === 'treated'
            ? true
            : false,
      },
      option,
    );

    topics = JSON.parse(JSON.stringify(topics));

    for (i in topics.docs) {
      let user = await userModel.findById(topics.docs[i].contentId.userId);

      if (user) {
        topics.docs[i].contentId.user = user;
      }

      // if (topics.docs[i].treated) {
      //   topics.docs[i].admin = 'yes';
      //   let admin = await userModel.findById(topics.docs[i].contentId.userId);
      // }
    }

    res.status(200).json({ topics, message: 'Loaded Successfully' });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const updateFlaggedTopic = async (req, res) => {
  if (!req.params.treatedContentId) {
    return res.status(400).json({
      message: 'treatedContentId is required',
    });
  }

  if (!req.query.appropriate) {
    return res.status(400).json({
      message: 'appropriate is required',
    });
  }

  try {
    let treatedContent = await treatedContentsModel.findById(
      req.params.treatedContentId,
    );

    if (!treatedContent) {
      res.status(400).json({ message: 'Treated content does not exist' });
      return;
    }

    await topicsModel.updateOne(
      { _id: treatedContent.contentId },
      {
        $set: {
          appropriate: req.query.appropriate,
        },
      },
    );

    await treatedContentsModel.updateOne(
      { _id: req.params.treatedContentId },
      {
        $set: {
          adminId: req.user.id,
          appropriate: req.query.appropriate,
          treated: true,
        },
      },
    );

    return res.status(200).json({
      message: 'Topic has been treated successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

const createAppTopic = async (req, res) => {
  try {
    if (
      req.file &&
      !(
        (req.body.attachmentType == 'image') |
        (req.body.attachmentType == 'video') |
        (req.body.attachmentType == 'audio') |
        (req.body.attachmentType == 'document')
      )
    ) {
      res.status(400).json({
        message: 'Attachment type is required',
      });
      return;
    }

    let topicsFolder = '';

    if (req.body.attachmentType == 'image') {
      topicsFolder = process.env.TOPICS_IMAGE_DIRECTORY + '/';
    }
    if (req.body.attachmentType == 'audio') {
      topicsFolder = process.env.TOPICS_AUDIO_DIRECTORY + '/';
    }
    if (req.body.attachmentType == 'video') {
      topicsFolder = process.env.TOPICS_VIDEO_DIRECTORY + '/';
    }
    if (req.body.attachmentType == 'document') {
      topicsFolder = process.env.TOPICS_DOCUMENT_DIRECTORY + '/';
    }

    let tags = null;

    if (req.body.tags) {
      tags = JSON.parse(JSON.stringify(req.body.tags));
      tags = tags.split(',');

      if (tags.length === 0) {
        res
          .status(400)
          .json({ message: 'Please select at least one interest' });
        return;
      }
      // else if (tags.length > 3) {
      //   res
      //     .status(400)
      //     .json({ message: 'You can only select three tags at once' });
      //   return;
      // }
    } else {
      res.status(400).json({ message: 'You can only select at least one tag' });
      return;
    }

    const adminConfigurations = await adminConfigurationsModel.findOne();

    if (!adminConfigurations) {
      res.status(400).json({ message: 'Admin has not been Configuration' });
    }

    const topicData = await topicsModel.create({
      userId: adminConfigurations.defaultAdminAppUserId,
      content: req.body.content,
      tags,
    });

    if (topicData) {
      if (req.file) {
        let fileName =
          new Date().getTime() +
          req.user.id +
          path.extname(req.file.originalname);
        const blob = bucket.file(topicsFolder + req.file.originalname);
        fs.createReadStream(req.file.path)
          .pipe(blob.createWriteStream())
          .on('error', (err) => {
            console.log(err);
            //  res.status(400).json({ message: err });
            next(err);
          })
          .on('finish', async () => {
            try {
              await blob.rename(topicsFolder + fileName);
              await bucket.file(topicsFolder + fileName).makePublic();

              let update = null;

              if (req.body.attachmentType == 'image') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      image: fileName,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'audio') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      audio: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'video') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      video: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              } else if (req.body.attachmentType == 'document') {
                update = await topicsModel.findOneAndUpdate(
                  { _id: topicData.id },
                  {
                    $set: {
                      document: fileName,
                      attachmentSize: req.file.size,
                    },
                  },
                  { new: true },
                );
              }

              res.status(200).json({
                message: 'Topic Created Successfully',
                topic: update,
              });
            } catch (error) {
              console.log(error);
              res
                .status(400)
                .json({ message: 'An Error Occurred', data: error });
            }
          });
      } else {
        res.status(200).json({
          message: 'Topic Created Successfully',
          topic: topicData,
        });
      }
    }
  } catch (error) {
    console.log(error, 'err');
    res.status(400).json({ message: error });
  }
};

const getTopicComments = async (req, res) => {
  let option = {
    page: req.query.page || 1,
    limit: req.query.limit || process.env.PAGING_LIMIT,
    populate: [
      {
        path: 'contentId',
        model: topicCommentsModel,
      },
      {
        path: 'adminId',
        model: adminModel,
        select: 'first_name last_name',
      },
    ],
    sort: { updatedAt: -1 },
  };

  try {
    let topicComments = await treatedContentsModel.paginate(
      {
        contentType: 'topic-comment',
        treated:
          req.query.type === 'untreated'
            ? false
            : req.query.type === 'treated'
            ? true
            : false,
      },
      option,
    );

    topicComments = JSON.parse(JSON.stringify(topicComments));

    for (i in topicComments.docs) {
      let user = await userModel.findById(
        topicComments.docs[i].contentId.userId,
      );

      if (user) {
        topicComments.docs[i].contentId.user = user;
      }
    }

    res.status(200).json({ topicComments, message: 'Loaded Successfully' });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: error });
  }
};

const updateFlaggedTopicComments = async (req, res) => {
  if (!req.params.treatedContentId) {
    return res.status(400).json({
      message: 'treatedContentId is required',
    });
  }

  if (!req.query.appropriate) {
    return res.status(400).json({
      message: 'appropriate is required',
    });
  }

  try {
    let treatedContent = await treatedContentsModel.findById(
      req.params.treatedContentId,
    );

    if (!treatedContent) {
      res.status(400).json({ message: 'Treated content does not exist' });
      return;
    }

    await topicCommentsModel.updateOne(
      { _id: treatedContent.contentId },
      {
        $set: {
          appropriate: req.query.appropriate,
        },
      },
    );

    await treatedContentsModel.updateOne(
      { _id: req.params.treatedContentId },
      {
        $set: {
          adminId: req.user.id,
          appropriate: req.query.appropriate,
          treated: true,
        },
      },
    );

    return res.status(200).json({
      message: 'Topic Comment has been treated successfully',
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: 'An error occurred',
    });
  }
};

module.exports = {
  getTopics,
  updateFlaggedTopic,
  createAppTopic,
  getTopicComments,
  updateFlaggedTopicComments,
};

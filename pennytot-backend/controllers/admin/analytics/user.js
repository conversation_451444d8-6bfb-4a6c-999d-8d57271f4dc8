const { userModel, groupsModel, topicsModel } = require('../../../models');
const { analyticsModel } = require('../../../models/admin');
const moment = require('moment');

const userAnalytics = async (req, res) => {
  try {
    let storedData = await analyticsModel.findOne({
      type: 'users',
    });

    if (!storedData) {
      let totalUsers = await userModel.countDocuments();

      let normalUsers = await userModel
        .find({
          suspended: false,
          reportedAndFlagged: false,
        })
        .countDocuments();

      let suspendedUsers = await userModel
        .find({
          suspended: true,
        })
        .countDocuments();

      let flaggedUsers = await userModel
        .find({
          reportedAndFlagged: true,
        })
        .countDocuments();

      let premiumUsers = await userModel
        .find({
          subscription: 'premium',
        })
        .countDocuments();

      let maleUsers = await userModel
        .find({
          gender: 'Male',
        })
        .countDocuments();

      let femaleUsers = await userModel
        .find({
          gender: 'Female',
        })
        .countDocuments();

      let allActiveMaleUsers = await userModel
        .find({
          gender: 'Male',
          last_seen: {
            $gte: moment().add(-30, 'days'),
          },
        })
        .countDocuments();

      let allActiveFemaleUsers = await userModel
        .find({
          gender: 'Female',
          last_seen: {
            $gte: moment().add(-30, 'days'),
          },
        })
        .countDocuments();

      let allNewMaleUsers = await userModel
        .find({
          gender: 'Male',
          createdAt: {
            $gte: moment().add(-30, 'days'),
          },
        })
        .countDocuments();

      let allNewFemaleUsers = await userModel
        .find({
          gender: 'Female',
          createdAt: {
            $gte: moment().add(-30, 'days'),
          },
        })
        .countDocuments();

      let tagUsage = await userModel.aggregate([
        { $unwind: '$interests' },
        { $group: { _id: '$interests', count: { $sum: 1 } } },
        {
          $group: {
            _id: null,
            counts: {
              $push: {
                k: '$_id',
                v: '$count',
              },
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: { $arrayToObject: '$counts' },
          },
        },
      ]);

      let maleTagUsage = await userModel.aggregate([
        {
          $match: { gender: 'Male' },
        },
        { $unwind: '$interests' },
        { $group: { _id: '$interests', count: { $sum: 1 } } },
        {
          $group: {
            _id: null,
            counts: {
              $push: {
                k: '$_id',
                v: '$count',
              },
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: { $arrayToObject: '$counts' },
          },
        },
      ]);

      let femaleTagUsage = await userModel.aggregate([
        {
          $match: { gender: 'Female' },
        },
        { $unwind: '$interests' },
        { $group: { _id: '$interests', count: { $sum: 1 } } },
        {
          $group: {
            _id: null,
            counts: {
              $push: {
                k: '$_id',
                v: '$count',
              },
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: { $arrayToObject: '$counts' },
          },
        },
      ]);

      let generalUsers = {
        totalUsers,
        normalUsers,
        suspendedUsers,
        flaggedUsers,
        maleUsers,
        femaleUsers,
        premiumUsers,
        allActiveMaleUsers,
        allActiveFemaleUsers,
        allNewMaleUsers,
        allNewFemaleUsers,
        tags: {
          tagUsage,
          maleTagUsage,
          femaleTagUsage,
        },
      };

      let data = await analyticsModel.create({
        type: 'users',
        data: generalUsers,
      });

      return res.status(200).json({
        generalUsers: data,
      });
    } else {
      // Data still exists
      return res.status(200).json({
        generalUsers: storedData,
      });
    }
  } catch (error) {
    console.log(error);

    return res.status(400).json({
      message: error,
    });
  }
};

module.exports = {
  userAnalytics,
};

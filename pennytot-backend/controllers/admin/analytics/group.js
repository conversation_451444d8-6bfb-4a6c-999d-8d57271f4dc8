const { groupsModel, groupMessagesModel } = require('../../../models');
const { analyticsModel } = require('../../../models/admin');
const moment = require('moment');

const groupAnalytics = async (req, res) => {
  try {
    let storedData = await analyticsModel.findOne({
      type: 'groups',
    });

    if (!storedData) {
      let totalGroups = await groupsModel.countDocuments();

      let appropriateGroups = await groupsModel
        .find({
          appropriate: true,
        })
        .countDocuments();

      let groupTagsCount = await groupsModel.aggregate([
        {
          $group: {
            _id: '$tag',
            count: {
              $sum: 1,
            },
          },
        },
        {
          $sort: {
            count: -1,
          },
        },
        {
          $limit: 3,
        },
      ]);

      let groupWithMostChats = await groupMessagesModel.aggregate([
        {
          $match: {
            system: {
              $exists: false,
            },
          },
        },
        {
          $group: {
            _id: '$groupId',
            count: {
              $sum: 1,
            },
          },
        },
        {
          $sort: {
            count: -1,
          },
        },
        {
          $limit: 3,
        },
        {
          $lookup: {
            from: 'groups',
            localField: '_id',
            foreignField: '_id',
            as: 'groupData',
          },
        },
        {
          $project: {
            _id: 0,
          },
        },
      ]);

      let groups = {
        totalGroups,
        appropriateGroups,
        groupTagsCount,
        groupWithMostChats,
      };

      let data = await analyticsModel.create({
        type: 'groups',
        data: groups,
      });

      return res.status(200).json({
        groups: data,
      });
    } else {
      // Data still exists
      return res.status(200).json({
        groups: storedData,
      });
    }
  } catch (error) {
    console.log(error, '== error');
    return res.status(400).json({
      message: error,
    });
  }
};

module.exports = {
  groupAnalytics,
};

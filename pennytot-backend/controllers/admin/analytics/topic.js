const {
  topicsModel,
  topicCommentsModel,
  userModel,
  topicLikesModel,
} = require('../../../models');
const { analyticsModel } = require('../../../models/admin');

const topicAnalytics = async (req, res) => {
  try {
    let storedData = await analyticsModel.findOne({
      type: 'topics',
    });

    if (!storedData) {
      let totalTopics = await topicsModel.countDocuments();

      let appropriateTopics = await topicsModel
        .find({
          appropriate: true,
        })
        .countDocuments();

      let topicsWithImages = await topicsModel
        .find({ image: { $exists: true } })
        .countDocuments();

      let topicsWithVideo = await topicsModel
        .find({ video: { $exists: true } })
        .countDocuments();

      let topicsWithDocuments = await topicsModel
        .find({ documents: { $exists: true } })
        .countDocuments();

      let topicsWithAudio = await topicsModel
        .find({ audio: { $exists: true } })
        .countDocuments();

      let topicsWithOnlyText = await topicsModel
        .find({
          audio: { $exists: false },
          video: { $exists: false },
          image: { $exists: false },
          document: { $exists: false },
        })
        .countDocuments();

      let topicTagsCount = await topicsModel.aggregate([
        { $unwind: '$tags' },
        { $group: { _id: '$tags', count: { $sum: 1 } } },
        {
          $group: {
            _id: null,
            counts: {
              $push: {
                k: '$_id',
                v: '$count',
              },
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: { $arrayToObject: '$counts' },
          },
        },
      ]);

      let topicsWithMostComments = await topicCommentsModel.aggregate([
        {
          $group: {
            _id: {
              topicId: '$topicId', //group all topic comments by topicId
            },
            // ids: {
            //   $push: "$_id"   // Put all topic comments id in an array
            // },
            count: {
              $sum: 1, //count them
            },
          },
        },
        {
          $sort: {
            count: -1, //sort the result from top to bottom
          },
        },
        {
          $limit: 5, // limit result to top 5
        },
        {
          $lookup: {
            from: 'topics',
            localField: '_id.topicId',
            foreignField: '_id',
            as: 'topicData',
          }, // Find Topic Data from topicID
        },
        {
          $project: {
            _id: 0, // remove id from group array of _id,
          },
        },
      ]);

      if (topicsWithMostComments && topicsWithMostComments.length > 0) {
        for (i in topicsWithMostComments) {
          let topic = topicsWithMostComments[i].topicData[0];

          let user = await userModel.findById(topic.userId);

          if (user) {
            user = JSON.parse(JSON.stringify(user));

            topic.user = {
              first_name: user.first_name,
              last_name: user.last_name,
              profile_picture: user.profile_picture,
            };
          }
        }
      }

      let topicsWithMostLikes = await topicLikesModel.aggregate([
        {
          $group: {
            _id: {
              topicId: '$topicId', //group all topic comments by topicId
            },
            // ids: {
            //   $push: "$_id"   // Put all topic comments id in an array
            // },
            count: {
              $sum: 1, //count them
            },
          },
        },
        {
          $sort: {
            count: -1, //sort the result from top to bottom
          },
        },
        {
          $limit: 5, // limit result to top 5
        },
        {
          $lookup: {
            from: 'topics',
            localField: '_id.topicId',
            foreignField: '_id',
            as: 'topicData',
          }, // Find Topic Data from topicID
        },
        {
          $project: {
            _id: 0, // remove id from group array of _id,
          },
        },
      ]);

      if (topicsWithMostLikes && topicsWithMostLikes.length > 0) {
        for (i in topicsWithMostLikes) {
          let topic = topicsWithMostLikes[i].topicData[0];

          let user = await userModel.findById(topic.userId);

          if (user) {
            user = JSON.parse(JSON.stringify(user));

            topic.user = {
              first_name: user.first_name,
              last_name: user.last_name,
              profile_picture: user.profile_picture,
            };
          }
        }
      }

      let topics = {
        totalTopics,
        appropriateTopics,
        contentStat: {
          topicsWithOnlyText,
          topicsWithAudio,
          topicsWithDocuments,
          topicsWithImages,
          topicsWithVideo,
        },
        topicTagsCount,
        topicsWithMostComments,
        topicsWithMostLikes,
      };

      let data = await analyticsModel.create({
        type: 'topics',
        data: topics,
      });

      return res.status(200).json({
        topics: data,
      });
    } else {
      // Data still exists
      return res.status(200).json({
        topics: storedData,
      });
    }
  } catch (error) {
    console.log(error, 'error');

    return res.status(400).json({
      message: error,
    });
  }
};

module.exports = {
  topicAnalytics,
};

const { unreadNotificationsModel } = require('../models');

async function handleConvoUnreadNotifications(topicId, topicSubscribers) {
  if (topicSubscribers.length > 0) {
    console.log('topicsubscribers----', topicSubscribers);
    console.log('topicId', topicId);
    try {
      await unreadNotificationsModel.updateMany(
        { userId: { $in: topicSubscribers } },
        // { $set: { convos: [topicId] } },
        { $addToSet: { convos: topicId } },
      );
    } catch (error) {
      console.log('err', error);
    }
  }
}

module.exports = handleConvoUnreadNotifications;

const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');
const CreditController = require('../controllers/credit');
const TransactionController = require('../controllers/transactions');

router.get('/get-credit', jsonParser, userAuth, CreditController.getCredit);

router.get(
  '/get-free-credit',
  jsonParser,
  userAuth,
  CreditController.getFreeCredit,
);

router.get(
  '/transaction-history',
  jsonParser,
  userAuth,
  TransactionController.getTransactions,
);

router.post('/buy', jsonParser, userAuth, CreditController.buyCredit);

module.exports = router;

const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');
const HelpDeskController = require('../controllers/helpdesk');
const documentRequest = require('../middleware/uploads/document');

router.post(
  '/create-ticket',
  jsonParser,
  userAuth,
  HelpDeskController.createHelpDeskTicket,
);

router.get(
  '/get-tickets',
  jsonParser,
  userAuth,
  HelpDeskController.getHelpDeskTickets,
);

router.get(
  '/get-messages/:helpdeskId',
  jsonParser,
  userAuth,
  HelpDeskController.getHelpDeskMessages,
);

router.get(
  '/categories',
  jsonParser,
  userAuth,
  HelpDeskController.getHelpDeskCategories,
);

router.post(
  '/reply-message/:helpdeskId',
  json<PERSON>arser,
  documentRequest,
  userA<PERSON>,
  HelpDeskController.replyHelpDeskMessage,
);

module.exports = router;

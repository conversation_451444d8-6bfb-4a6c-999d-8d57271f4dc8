const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');
const ChatsController = require('../controllers/chats');
const {
  ImageFileCheck,
  DocumentFileCheck,
  AudioFileCheck,
  VideoFileCheck,
} = require('../middleware/uploads/fileMIMEType');
const profileImage = require('../middleware/uploads/profileImage');
const documentRequest = require('../middleware/uploads/document');
const contact = require('../middleware/uploads/contact');
const audio = require('../middleware/uploads/audio');
const video = require('../middleware/uploads/video');

router.get('/my-chats', jsonParser, userAuth, ChatsController.getChats);
router.get('/bot-chats', json<PERSON><PERSON><PERSON>, user<PERSON>uth, ChatsController.getBotChats);

router.post(
  '/search/:chatId',
  json<PERSON>arser,
  user<PERSON>uth,
  ChatsController.searchChatMessages,
);

router.post(
  '/upload-image-attachment/:chatId',
  jsonParser,
  profileImage,
  ImageFileCheck,
  userAuth,
  ChatsController.uploadChatImageAttachment,
);

router.post(
  '/upload-document-attachment/:chatId',
  jsonParser,
  documentRequest,
  userAuth,
  ChatsController.uploadChatDocumentAttachment,
);

router.post(
  '/upload-audio-attachment/:chatId',
  jsonParser,
  audio,
  AudioFileCheck,
  userAuth,
  ChatsController.uploadChatAudioAttachment,
);

router.post(
  '/upload-video-attachment/:chatId',
  jsonParser,
  video,
  VideoFileCheck,
  userAuth,
  ChatsController.uploadChatVideoAttachment,
);

router.post(
  '/upload-contact-attachment/:chatId',
  jsonParser,
  contact,
  userAuth,
  ChatsController.uploadChatContactAttachment,
);

router.delete(
  '/delete-message/:chatMessageId',
  jsonParser,
  userAuth,
  ChatsController.deleteMessage,
);

router.get(
  '/:accountId',
  jsonParser,
  userAuth,
  ChatsController.getChatMessages,
);

router.patch(
  '/remove-from-chat/:accountId',
  jsonParser,
  userAuth,
  ChatsController.removeFromActiveChat,
);

router.patch(
  '/block/:accountId',
  jsonParser,
  userAuth,
  ChatsController.blockChat,
);

router.patch(
  '/unblock/:accountId',
  jsonParser,
  userAuth,
  ChatsController.unblockChat,
);

module.exports = router;

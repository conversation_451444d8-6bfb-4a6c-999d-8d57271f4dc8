const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');
const TagsController = require('../controllers/tags');

router.post('/create', jsonParser, userAuth, TagsController.createTag);

router.get('/list', jsonParser, userAuth, TagsController.getTags);

router.post('/set-user-tags', jsonParser, userAuth, TagsController.setUserTags);

router.get('/delete-tags', jsonParser, userAuth, TagsController.deleteAllTags);

module.exports = router;

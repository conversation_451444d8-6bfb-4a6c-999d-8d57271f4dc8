const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');
const GroupChatsController = require('../controllers/groups');
const profileImage = require('../middleware/uploads/profileImage');
const { ImageFileCheck } = require('../middleware/uploads/fileMIMEType');
const validateGroupAdmin = require('../middleware/groups/validateGroupAdmin');

router.get(
  '/chats/:groupId',
  jsonParser,
  userAuth,
  GroupChatsController.getGroupChats,
);

router.get('/my-groups', jsonParser, userAuth, GroupChatsController.myGroups);

router.get(
  '/user-groups/:userId',
  jsonParser,
  userAuth,
  GroupChatsController.userGroups,
);

router.get(
  '/suggested-groups',
  json<PERSON>arser,
  userAuth,
  GroupChatsController.suggestedGroups,
);

router.patch(
  '/update-group-picture/:groupId',
  jsonParser,
  userAuth,
  profileImage,
  ImageFileCheck,
  validateGroupAdmin,
  GroupChatsController.updateGroupPicture,
);

router.post(
  '/search/:groupId',
  jsonParser,
  userAuth,
  GroupChatsController.searchGroupMessages,
);

router.patch(
  '/update/:groupId',
  jsonParser,
  userAuth,
  validateGroupAdmin,
  GroupChatsController.updateGroup,
);

router.patch(
  '/make-admin/:groupId/:userId',
  jsonParser,
  userAuth,
  validateGroupAdmin,
  GroupChatsController.makeGroupAdmin,
);

router.patch(
  '/remove-admin/:groupId/:userId',
  jsonParser,
  userAuth,
  validateGroupAdmin,
  GroupChatsController.removeGroupAdmin,
);

router.patch(
  '/remove/:groupId/:userId',
  jsonParser,
  userAuth,
  validateGroupAdmin,
  GroupChatsController.removeFromGroup,
);

router.delete(
  '/leave/:groupId',
  jsonParser,
  userAuth,
  GroupChatsController.leaveGroup,
);

router.delete(
  '/delete-message/:groupMessageId',
  jsonParser,
  userAuth,
  profileImage,
  ImageFileCheck,
  GroupChatsController.deleteMessage,
);

router.patch(
  '/join/:groupId',
  jsonParser,
  userAuth,
  GroupChatsController.joinGroup,
);
router.get(
  '/view/:groupId',
  jsonParser,
  userAuth,
  GroupChatsController.getGroup,
);

router.post(
  '/flag/:groupId',
  jsonParser,
  userAuth,
  GroupChatsController.flagGroup,
);

router.get('/all', jsonParser, userAuth, GroupChatsController.listGroups);
router.post(
  '/create',
  jsonParser,
  profileImage,
  ImageFileCheck,
  userAuth,
  GroupChatsController.createGroup,
);

module.exports = router;

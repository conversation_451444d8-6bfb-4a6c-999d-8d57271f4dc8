const express = require('express');
const jsonParser = express.json();
const router = express.Router();
const questionController = require('../controllers/quiz'); // Adjust the path based on your project structure
const userController = require('../controllers/user'); // Adjust the path based on your project structure
const userAuth = require('../middleware/userAuth');
const bodyParser = require('body-parser');
const multer = require('multer');
const upload = multer();


// Endpoint for adding bulk questions
router.post('/questions/bulk',jsonParser,userAuth, questionController.addBulkQuestions);

// Endpoint for getting questions with a limit
router.get('/questions/:limit',jsonParser,userAuth, questionController.getQuestionsWithLimit);

// Endpoint to get random question
router.get('/random',jsonParser,userAuth, questionController.getQuestionOld);

router.post('/response',json<PERSON>arser,userAuth, questionController.handleUserResponse);

router.get('/random/:country', json<PERSON>arser, userAuth, questionController.getQuestion);

router.get('/:type/:amount',jsonParser,userAuth, questionController.changeUserPennyTot);

router.post('/uploadQuestions/:country', upload.single("questions"), jsonParser, userAuth, questionController.uploadQuestions);

router.post('/uploadQuestions',upload.single("questions"),jsonParser,userAuth, questionController.uploadQuestionsOld);

router.post('/replaceQuestions',upload.single("questions"),jsonParser,userAuth, questionController.uploadQuestionsReplaceOld);
router.post('/replaceQuestions/:country',upload.single("questions"),jsonParser,userAuth, questionController.uploadQuestionsReplace);

module.exports = router;

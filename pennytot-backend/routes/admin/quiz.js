const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const questionController = require('../../controllers/quiz'); // Adjust the path based on your project structure
const multer = require('multer');
const upload = multer();
const adminAuth = require('../../middleware/adminAuth');

router.post('/uploadQuestions',upload.single("questions"),json<PERSON><PERSON><PERSON>,admin<PERSON><PERSON>({ permissions: [] }), questionController.uploadQuestions);

router.post('/replaceQuestions',upload.single("questions"),json<PERSON><PERSON><PERSON>,admin<PERSON><PERSON>({ permissions: [] }), questionController.uploadQuestionsReplace);

router.get('/questions/:limit',json<PERSON><PERSON><PERSON>,admin<PERSON><PERSON>({ permissions: [] }), questionController.getQuestionsWithLimit);

router.post('/replaceQuestions/:country',upload.single("questions"),json<PERSON><PERSON><PERSON>,admin<PERSON><PERSON>({ permissions: [] }), questionController.uploadQuestionsReplace);

module.exports = router;

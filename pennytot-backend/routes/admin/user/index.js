const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const AdminUserController = require('../../../controllers/admin/users');
const adminAuth = require('../../../middleware/adminAuth');
const UserValidations = require('./validator');
const RequestValidator = require('../../../middleware/requestValidator');

router.get(
  '/get-users',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_USERS'] }),
  AdminUserController.getUsers,
);

router.post(
  '/update/:userId',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_USERS'] }),
  AdminUserController.updateUser,
);

router.get(
  '/get-user-flagged-reports/:userId',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_USERS'] }),
  AdminUserController.getUserFlaggedReports,
);

router.get(
  '/get-user-chats/:chatId',
  json<PERSON>arser,
  admin<PERSON>uth({ permissions: ['CAN_MANAGE_USERS'] }),
  AdminUserController.getChatById,
);

router.get(
  '/get-user-contents/:userId',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_USERS'] }),
  AdminUserController.getUserContents,
);

router.get(
  '/get-user-pennytots/:userId',
  jsonParser,
  adminAuth({ permissions: ['ONLY_SUPER_ADMIN'] }),
  AdminUserController.getUserPennytots,
);

router.post(
  '/update-user-pennytots/:userId',
  jsonParser,
  RequestValidator(UserValidations.updateUserPennytots, 'body'),
  adminAuth({ permissions: ['ONLY_SUPER_ADMIN'] }),
  AdminUserController.updateUserPennytots,
);

module.exports = router;

const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const AdminGroupController = require('../../controllers/admin/groups');
const adminAuth = require('../../middleware/adminAuth');

router.post(
  '/treat-group/:groupId',
  json<PERSON>arser,
  adminAuth({ permissions: ['CAN_MANAGE_GROUPS'] }),
  AdminGroupController.treatReportedGroup,
);

router.get(
  '/flagged-groups',
  json<PERSON>arser,
  adminAuth({ permissions: ['CAN_MANAGE_GROUPS'] }),
  AdminGroupController.flaggedGroups,
);

router.get(
  '/treated-groups',
  json<PERSON>arser,
  adminAuth({ permissions: ['CAN_MANAGE_GROUPS'] }),
  AdminGroupController.treatedGroups,
);

router.get(
  '/get-group-messages/:groupId',
  json<PERSON>ars<PERSON>,
  adminAuth({ permissions: ['CAN_MANAGE_GROUPS'] }),
  AdminGroupController.getGroupMessages,
);

module.exports = router;

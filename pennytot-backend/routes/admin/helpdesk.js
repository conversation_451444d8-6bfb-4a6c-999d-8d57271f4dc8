const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const AdminHelpdeskController = require('../../controllers/admin/helpdesk');
const adminAuth = require('../../middleware/adminAuth');
const documentRequest = require('../../middleware/uploads/document');

router.get(
  '/get-helpdesk-messages',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_HELPDESK'] }),
  AdminHelpdeskController.getHelpDeskMessages,
);

router.get(
  '/get-tickets',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_HELPDESK'] }),
  AdminHelpdeskController.getHelpDeskTickets,
);

router.get(
  '/get-messages/:helpdeskId',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_HELPDESK'] }),
  AdminHelpdeskController.getHelpDeskMessages,
);

router.post(
  '/reply-message/:helpdeskId',
  jsonParser,
  documentRequest,
  adminAuth({ permissions: ['CAN_MANAGE_HELPDESK'] }),
  AdminHelpdeskController.replyHelpDeskMessage,
);

router.get(
  '/categories',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_HELPDESK'] }),
  AdminHelpdeskController.getHelpDeskCategories,
);

module.exports = router;

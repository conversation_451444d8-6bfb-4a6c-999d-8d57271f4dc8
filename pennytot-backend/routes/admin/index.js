const accountRoute = require('./account');
const dashboardRoute = require('./dashboard');
const helpdeskRoute = require('./helpdesk');
const topicsRoute = require('./topics');
const groupRoute = require('./group');
const userRoute = require('./user');
const tagRoute = require('./tags');
const quizRoute = require('./quiz');


module.exports = function (app) {
  app.use('/admin/account', accountRoute);
  app.use('/admin/dashboard', dashboardRoute);
  app.use('/admin/helpdesk', helpdeskRoute);
  app.use('/admin/topics', topicsRoute);
  app.use('/admin/groups', groupRoute);
  app.use('/admin/users', userRoute);
  app.use('/admin/tags', tagRoute);
  app.use('/admin/quiz', quizRoute);
};

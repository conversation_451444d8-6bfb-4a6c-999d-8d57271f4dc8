const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const AdminTopicController = require('../../controllers/admin/topics');
const adminAuth = require('../../middleware/adminAuth');
const documentRequest = require('../../middleware/uploads/document');

router.get(
  '/get-topics',
  json<PERSON>arser,
  adminAuth({ permissions: ['CAN_MANAGE_TOPICS'] }),
  AdminTopicController.getTopics,
);

router.get(
  '/update-topic/:treatedContentId',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_TOPICS'] }),
  AdminTopicController.updateFlaggedTopic,
);

router.post(
  '/create-app-topic',
  jsonParser,
  documentRequest,
  adminAuth({ permissions: ['CAN_MANAGE_TOPICS'] }),
  AdminTopicController.createAppTopic,
);

router.get(
  '/get-topic-comments',
  json<PERSON>arser,
  admin<PERSON>uth({ permissions: ['CAN_MANAGE_TOPICS'] }),
  AdminTopicController.getTopicComments,
);

router.get(
  '/update-topic-comment/:treatedContentId',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_TOPICS'] }),
  AdminTopicController.updateFlaggedTopicComments,
);

module.exports = router;

const express = require('express');
const router = express.Router();
const jsonParser = express.json();

const AdminDashboardController = require('../../controllers/admin/dashboard');
const UserAnalyticsController = require('../../controllers/admin/analytics/user');
const TopicAnalyticsController = require('../../controllers/admin/analytics/topic');
const GroupAnalyticsController = require('../../controllers/admin/analytics/group');
const adminAuth = require('../../middleware/adminAuth');

router.get(
  '/',
  jsonParser,
  adminAuth({ permissions: ['CAN_VIEW_DASHBOARD'] }),
  AdminDashboardController.dashboard,
);

router.get(
  '/user-analytics',
  jsonParser,
  adminAuth({ permissions: ['CAN_VIEW_DASHBOARD'] }),
  UserAnalyticsController.userAnalytics,
);

router.get(
  '/topic-analytics',
  json<PERSON>arser,
  admin<PERSON>uth({ permissions: ['CAN_VIEW_DASHBOARD'] }),
  TopicAnalyticsController.topicAnalytics,
);

router.get(
  '/group-analytics',
  jsonParser,
  adminAuth({ permissions: ['CAN_VIEW_DASHBOARD'] }),
  GroupAnalyticsController.groupAnalytics,
);

module.exports = router;

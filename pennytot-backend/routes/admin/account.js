const express = require('express');
const router = express.Router();
const jsonParser = express.json();

const AdminController = require('../../controllers/admin/admin');

const adminAuth = require('../../middleware/adminAuth');

router.post('/login', json<PERSON><PERSON><PERSON>, AdminController.login);

router.post('/register', jsonParser, AdminController.register);

router.get(
  '/get-admins',
  jsonParser,
  adminAuth({ permissions: ['CAN_MANAGE_APP_ADMIN'] }),
  AdminController.getAdmins,
);

router.post(
  '/invite',
  jsonParser,
  adminAuth({ permissions: ['ONLY_SUPER_ADMIN'] }),
  AdminController.invite,
);

router.post(
  '/setup-admin-configurations',
  jsonParser,
  AdminController.setupAdminConfigurations,
);

module.exports = router;

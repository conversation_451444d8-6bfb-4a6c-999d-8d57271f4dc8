const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');
const TopicController = require('../controllers/topics');
const documentRequest = require('../middleware/uploads/document');

router.post(
  '/create',
  jsonParser,
  documentRequest,
  userAuth,
  TopicController.createTopic,
);

router.post('/search', jsonParser, userAuth, TopicController.searchTopics);

router.get('/getTopics', jsonParser, userAuth, TopicController.getTopics);

router.get('/getTopicsOld', jsonParser, userAuth, TopicController.getTopicsOld);

router.get(
  '/get-user-topics/:userId',
  jsonParser,
  userAuth,
  TopicController.getUserTopics,
);

router.get(
  '/viewTopic/:topicId',
  json<PERSON><PERSON><PERSON>,
  user<PERSON><PERSON>,
  TopicController.getTopic,
);

router.post(
  '/edit-topic/:topicId',
  json<PERSON>arser,
  user<PERSON>uth,
  TopicController.editTopic,
);

router.post(
  '/likeTopic/:topicId',
  jsonParser,
  userAuth,
  TopicController.likeTopic,
);

router.post(
  '/like-topic-comment/:commentId',
  jsonParser,
  userAuth,
  TopicController.likeComment,
);

router.post(
  '/create-comment/:topicId',
  jsonParser,
  documentRequest,
  userAuth,
  TopicController.createTopicComment,
);

router.post(
  '/create-topic-sub-comment/:commentId',
  jsonParser,
  userAuth,
  TopicController.createTopicSubComment,
);

router.get(
  '/view-topic-comments/:topicId',
  jsonParser,
  userAuth,
  TopicController.getTopicComments,
);

router.get(
  '/view-topic-sub-comments/:commentId',
  jsonParser,
  userAuth,
  TopicController.getTopicSubComments,
);

router.get(
  '/view-topic-comment/:topicCommentId',
  jsonParser,
  userAuth,
  TopicController.getTopicComment,
);

router.get('/get-convos', jsonParser, userAuth, TopicController.getConvos);

router.delete(
  '/remove-topic-from-convos/:topicId',
  jsonParser,
  userAuth,
  TopicController.removeFromConvo,
);

router.post('/flag/:topicId', jsonParser, userAuth, TopicController.flagTopic);

router.post(
  '/flag-comment/:commentId',
  jsonParser,
  userAuth,
  TopicController.flagTopicComment,
);

module.exports = router;

const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const profileImage = require('../../middleware/uploads/profileImage');
const { ImageFileCheck } = require('../../middleware/uploads/fileMIMEType');
const userAuth = require('../../middleware/userAuth');
const UserController = require('../../controllers/user');
const TokenController = require('../../controllers/token');
const RequestValidator = require('../../middleware/requestValidator');
const UserValidations = require('./validator');

router.post('/login', jsonParser, UserController.login);

router.post(
  '/register',
  jsonParser,
  RequestValidator(UserValidations.register, 'body'),
  UserController.register,
);

router.post('/device-token', jsonParser, UserController.deviceToken);

router.get('/view-profile', jsonParser, userAuth, UserController.viewProfile);

router.post(
  '/send-verify-phone-number-token',
  jsonParser,
  RequestValidator(UserValidations.sendPhoneNumberVerificationToken, 'body'),
  userAuth,
  TokenController.sendPhoneNumberVerificationToken,
);

router.post(
  '/verify-phone-number-token',
  jsonParser,
  userAuth,
  TokenController.validatePhoneNumberToken,
);

router.post(
  '/send-verify-email-token',
  jsonParser,
  TokenController.sendEmailResetToken,
);

router.post(
  '/verify-email-token',
  jsonParser,
  TokenController.validateEmailToken,
);

router.post(
  '/update-profile',
  jsonParser,
  userAuth,
  UserController.updateProfile,
);

router.patch(
  '/update-profile-picture',
  jsonParser,
  userAuth,
  profileImage,
  ImageFileCheck,
  UserController.updateProfilePicture,
);

router.patch(
  '/update-header-image',
  jsonParser,
  userAuth,
  profileImage,
  ImageFileCheck,
  UserController.updateHeaderImage,
);

router.patch(
  '/reset-password',
  jsonParser,
  userAuth,
  UserController.resetPassword,
);

router.patch(
  '/report/:accountId',
  jsonParser,
  userAuth,
  UserController.reportUser,
);

router.get('/:userId', jsonParser, userAuth, UserController.getAccount);

router.post('/deactivate-account', jsonParser, userAuth, UserController.deactivateAccount);
router.post('/delete-account', jsonParser, userAuth, UserController.deleteAccount);

module.exports = router;

const Joi = require('joi');

const register = (user) => {
  const schema = Joi.object({
    first_name: Joi.string().min(2).max(20).required(),
    last_name: Joi.string().min(2).max(20).required(),
    email: Joi.string().email().required(),
    phone_number: Joi.object({
      code: Joi.string()
        .regex(/^\+\d{1,3}$/)
        .messages({
          'string.pattern.base': `Phone number code must be of country format +1.`,
        })
        .required(),
      number: Joi.string()
        .regex(/^[0-9]{7,10}$/)
        .messages({
          'string.pattern.base': `Phone number must have between 7 and 10 digits.`,
        })
        .required()
    }),
    country: Joi.string().required(),
    password: Joi.string()
      // .pattern(
      //   new RegExp(
      //     '^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$',
      //   ),
      // )
      // .messages({
      //   'string.pattern.base': `Password must be minimum of eight characters, at least one uppercase and lowercase letter, one number and one special character`,
      // })
      .required(),
  });
  return schema.validate(user);
};

const sendPhoneNumberVerificationToken = (user) => {
  const schema = Joi.object({
    type: Joi.string().valid('sms', 'whatsapp'),
  });

  return schema.validate(user);
};

module.exports = {
  register,
  sendPhoneNumberVerificationToken,
};

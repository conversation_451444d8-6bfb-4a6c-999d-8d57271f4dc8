const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');

const AppController = require('../controllers/app');
const verifyRecaptcha = require('../middleware/recaptcha');

router.post('/helpdesk', jsonParser, userAuth, AppController.sendToHelpDesk);

router.post(
  '/report-an-issue',
  jsonParser,
  verifyRecaptcha,
  AppController.reportAnIssue,
);

router.post('/search', jsonParser, userAuth, AppController.search);

router.post(
  '/mute-notifications',
  jsonParser,
  userAuth,
  AppController.mutedNotifications,
);

router.get(
  '/unread-notifications',
  json<PERSON>arser,
  userAuth,
  AppController.unreadNotifications,
);

router.get(
  '/clear-unread-notifications/:source',
  json<PERSON><PERSON><PERSON>,
  user<PERSON><PERSON>,
  AppController.clearReadNotifications,
);

module.exports = router;

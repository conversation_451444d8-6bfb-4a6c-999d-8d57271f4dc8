const express = require('express');
const router = express.Router();
const jsonParser = express.json();
const userAuth = require('../middleware/userAuth');
const FollowController = require('../controllers/followers');

// Endpoint to follow a user
router.get('/follow/:followingId', json<PERSON><PERSON><PERSON>, userAuth, FollowController.followUser);

router.get('/isFollowing/:followingId',  jsonParser, userAuth,  FollowController.isFollowingUser);

// Endpoint to unfollow a user
router.get('/unfollow/:followingId', jsonParser, userAuth, FollowController.unfollowUser);

// Endpoint to get the list of users a user is following
router.get('/following', json<PERSON>arser, userAuth, FollowController.getFollowingList);

module.exports = router;

const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const transactionSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'user',
    },
    source: {
      type: String,
      required: true,
      enum: ['flutterwave', 'admin','quiz'],
    },
    action: {
      type: String,
      required: true,
      enum: ['buy-credit'],
    },
    amount: {
      type: Number,
      required: true,
    },
    data: {
      type: Object,
      default: {},
      required: true,
    },
    status: {
      type: String,
      required: true,
      default: 'initiated',
      enum: ['initiated', 'pending', 'cancelled', 'success'],
    },
  },
  { timestamps: true },
);

transactionSchema.plugin(mongoosePaginate);

module.exports = transactionSchema;

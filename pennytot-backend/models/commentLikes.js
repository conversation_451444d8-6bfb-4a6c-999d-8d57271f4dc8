const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const commentLikesSchema = mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: ['comment', 'subcomment'],
      required: true,
    },
    commentId: {
      type: String,
      required: true,
    },
  },
  { timestamps: true },
);

commentLikesSchema.plugin(mongoosePaginate);
module.exports = commentLikesSchema;

const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const uniqueValidator = require('mongoose-unique-validator');
const bcrypt = require('bcryptjs');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const usersSchema = mongoose.Schema(
  {
    first_name: {
      type: String,
      required: true,
      index: true,
    },
    last_name: {
      type: String,
      required: true,
      index: true,
    },
    bio: {
      type: String,
      index: true,
    },
    country: {
      type: String,
      required: true,
    },
    profile_picture: {
      type: String,
    },
    header_image: {
      type: String,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    phone_number: {
      type: Object,
      required: true,
    },
    device_token: {
      type: String,
    },
    gender: {
      type: String,
    },
    website: {
      type: String,
    },
    company: {
      type: String,
    },
    company_position: {
      type: String,
    },
    linkedin: {
      type: String,
    },
    facebook: {
      type: String,
    },
    age: {
      type: String,
    },
    city: {
      type: String,
    },
    state: {
      type: String,
    },
    deactivated: {
      type: Boolean,
      default: false,
    },
    verified: {
      type: Boolean,
      required: true,
      default: false,
    },
    suspended: {
      type: Boolean,
      default: false,
    },
    reportedAndFlagged: {
      type: Boolean,
      default: false,
    },
    interests: {
      type: Array,
      required: true,
      default: [],
    },
    password: {
      type: String,
      required: true,
    },
    admin: {
      type: Boolean,
      default: false,
    },
    phone_number_verified: {
      type: Boolean,
      default: false,
    },
    last_seen: {
      type: Date,
    },
  },
  { timestamps: true },
);

usersSchema.plugin(uniqueValidator, {
  message: '{PATH} already exist.',
  status: 'duplicate',
});

usersSchema.pre('save', function (next) {
  if (!this.isModified('password')) {
    return next();
  }

  this.password = bcrypt.hashSync(
    this.password,
    Number(process.env.BCRYPT_SALT),
  );

  next();
});

//hide password hash from response & set profile picture address
usersSchema.set('toJSON', {
  transform: function (doc, ret, opt) {
    delete ret['password'];
    // delete ret['admin'];

    if (ret['profile_picture']) {
      ret['profile_picture'] = bucket
        .file(process.env.PROFILE_DIRECTORY + '/' + ret['profile_picture'])
        .publicUrl();
    }

    if (ret['header_image']) {
      ret['header_image'] = bucket
        .file(process.env.HEADER_IMAGE_DIRECTORY + '/' + ret['header_image'])
        .publicUrl();
    }

    return ret;
  },
});

usersSchema.methods.comparePassword = function (plaintext, callback) {
  return callback(null, bcrypt.compareSync(plaintext, this.password));
};

usersSchema.index({
  first_name: 'text',
  last_name: 'text',
  bio: 'text',
  email: 'text',
  company_position: 'text',
  company:'text',
});

usersSchema.plugin(mongoosePaginate);

module.exports = usersSchema;

const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const chatsSchema = mongoose.Schema(
  {
    participants: {
      type: Array,
      required: true,
      default: [],
    },
    blocked: {
      type: Array,
      default: [],
    },
    isCleared: {
      type: Boolean,
      default: false,
    },
    activeChat: {
      type: Array,
      required: true,
      default: [],
    },
    lastRead: {
      type: Object,
      required: true,
      default: { 0: 0, 1: 0 },
    },
  },
  { timestamps: true },
);

chatsSchema.index({ participants: 1 });
chatsSchema.index({ blocked: 1 });
chatsSchema.index({ activeChat: 1 });

chatsSchema.plugin(mongoosePaginate);
chatsSchema.index({ updatedAt: -1 });

module.exports = chatsSchema;

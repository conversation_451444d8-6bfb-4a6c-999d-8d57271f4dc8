const { dbConnection } = require('../../utils/db');

const adminsSchema = require('./admin');
const treatedContentSchema = require('./treatedContent');
const adminConfigurationsSchema = require('./adminConfigurations');
const analyticsSchema = require('./analytics');

const adminModel = dbConnection.model('Admin Accounts', adminsSchema);
const treatedContentsModel = dbConnection.model(
  'Treated Contents',
  treatedContentSchema,
);
const adminConfigurationsModel = dbConnection.model(
  'Admin Configurations',
  adminConfigurationsSchema,
);

const analyticsModel = dbConnection.model('Analytics', analyticsSchema);

module.exports = {
  adminModel,
  treatedContentsModel,
  adminConfigurationsModel,
  analyticsModel,
};

const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const treatedContentSchema = mongoose.Schema(
  {
    contentId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      unique: true,
    },
    contentType: {
      type: String,
      required: true,
      enum: ['topic', 'group', 'topic-comment'],
    },
    adminId: {
      type: mongoose.Schema.Types.ObjectId,
    },
    appropriate: {
      type: Boolean,
      default: false,
    },
    treated: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true },
);

treatedContentSchema.plugin(mongoosePaginate);

module.exports = treatedContentSchema;

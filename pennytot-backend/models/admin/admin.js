const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const uniqueValidator = require('mongoose-unique-validator');
const bcrypt = require('bcryptjs');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const adminsSchema = mongoose.Schema(
  {
    first_name: {
      type: String,
      index: true,
    },
    last_name: {
      type: String,
      index: true,
    },
    profile_picture: {
      type: String,
    },
    super_admin: {
      type: Boolean,
      default: false,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    password: {
      type: String,
    },
    permissions: {
      type: Array,
      required: true,
    },
    active: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true },
);

adminsSchema.plugin(uniqueValidator, {
  message: '{PATH} already exist.',
  status: 'duplicate',
});

adminsSchema.pre('save', function (next) {
  if (!this.isModified('password')) {
    return next();
  }

  this.password = bcrypt.hashSync(
    this.password,
    Number(process.env.BCRYPT_SALT),
  );

  next();
});

//hide password hash from response & set profile picture address
adminsSchema.set('toJSON', {
  transform: function (doc, ret, opt) {
    delete ret['password'];

    if (ret['profile_picture']) {
      ret['profile_picture'] = bucket
        .file(process.env.PROFILE_DIRECTORY + '/' + ret['profile_picture'])
        .publicUrl();
    }

    return ret;
  },
});

adminsSchema.methods.comparePassword = function (plaintext, callback) {
  return callback(null, bcrypt.compareSync(plaintext, this.password));
};

// usersSchema.index({
//   first_name: 'text',
//   last_name: 'text',
//   bio: 'text',
//   company_position: 'text',
// });

adminsSchema.plugin(mongoosePaginate);

module.exports = adminsSchema;

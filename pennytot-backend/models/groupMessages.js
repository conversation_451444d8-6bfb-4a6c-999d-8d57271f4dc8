const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const groupMessagesSchema = mongoose.Schema(
  {
    groupId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    senderId: {
      type: mongoose.Schema.Types.ObjectId,
    },
    message: {
      type: String,
      default: '',
      index: true,
    },
    attachment: {
      type: String,
    },
    attachmentSize: {
      type: Number,
    },
    system: {
      type: String,
    },
    quotedReply: {
      type: Object,
    },
    type: {
      type: String,
      enum: ['message', 'image', 'audio', 'contact', 'document', 'video'],
      default: 'message',
      required: true,
    },
  },
  { timestamps: true },
);

groupMessagesSchema.set('toJSON', {
  transform: function (doc, ret, opt) {
    if (ret['attachment']) {
      if (ret['type'] == 'image') {
        ret['attachment'] = bucket
          .file(process.env.GROUP_IMAGE_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      } else if (ret['type'] == 'audio') {
        ret['attachment'] = bucket
          .file(process.env.GROUP_AUDIO_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      } else if (ret['type'] == 'document') {
        ret['attachment'] = bucket
          .file(process.env.GROUP_DOCUMENT_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      } else if (ret['type'] == 'video') {
        ret['attachment'] = bucket
          .file(process.env.GROUP_VIDEO_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      }
    }

    return ret;
  },
});

groupMessagesSchema.index({ message: 'text' });
groupMessagesSchema.plugin(mongoosePaginate);

module.exports = groupMessagesSchema;

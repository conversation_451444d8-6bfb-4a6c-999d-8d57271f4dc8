const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const groupsSchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      index: true,
    },
    description: {
      type: String,
    },
    admins: {
      type: Array,
      required: true,
      default: [],
    },
    participants: {
      type: Array,
      required: true,
      default: [],
    },
    image: {
      type: String,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    tag: {
      type: mongoose.Schema.Types.ObjectId,
      required: [true, 'Tag is required'],
    },
    appropriate: {
      type: Boolean,
      required: true,
      default: true,
    },
    type: {
      type: String,
      enum: ['public', 'private'],
      required: true,
      default: 'public',
    },
  },
  { timestamps: true },
);

groupsSchema.set('toJSON', {
  transform: function (doc, ret, opt) {
    if (ret['image']) {
      ret['image'] = bucket
        .file(process.env.GROUPS_HEADER_DIRECTORY + '/' + ret['image'])
        .publicUrl();
    }
    return ret;
  },
});

groupsSchema.index({
  name: 'text',
});

groupsSchema.plugin(mongoosePaginate);

module.exports = groupsSchema;

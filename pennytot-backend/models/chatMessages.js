const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const chatsSchema = mongoose.Schema(
  {
    chatId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    senderId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    message: {
      type: String,
      default: '',
      index: true,
    },
    attachment: {
      type: String,
    },
    thumbnail: {
      type: String,
    },
    quotedReply: {
      type: Object,
    },
    attachmentSize: {
      type: Number,
    },
    type: {
      type: String,
      enum: ['message', 'image', 'audio', 'contact', 'document', 'video'],
      default: 'message',
      required: true,
    },
  },
  { timestamps: true },
);

chatsSchema.set('toJSON', {
  transform: function (doc, ret, opt) {
    if (ret['attachment']) {
      if (ret['type'] == 'image') {
        ret['attachment'] = bucket
          .file(process.env.CHAT_IMAGE_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      } else if (ret['type'] == 'audio') {
        ret['attachment'] = bucket
          .file(process.env.CHAT_AUDIO_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      } else if (ret['type'] == 'document') {
        ret['attachment'] = bucket
          .file(process.env.CHAT_DOCUMENT_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      } else if (ret['type'] == 'video') {
        ret['attachment'] = bucket
          .file(process.env.CHAT_VIDEO_DIRECTORY + '/' + ret['attachment'])
          .publicUrl();
      }
    }

    return ret;
  },
});

chatsSchema.index({ message: 'text' });
chatsSchema.plugin(mongoosePaginate);
chatsSchema.index({ chatId: 1 });
chatsSchema.index({ createdAt: -1 });

module.exports = chatsSchema;

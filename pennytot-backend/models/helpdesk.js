const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const helpdeskSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    title: {
      type: String,
      required: true,
      index: true,
    },
    ticketNumber: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    closed: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true },
);

helpdeskSchema.index({ title: 'text' });

helpdeskSchema.plugin(mongoosePaginate);

module.exports = helpdeskSchema;

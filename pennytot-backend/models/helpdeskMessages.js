const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const helpdeskMessageSchema = mongoose.Schema(
  {
    helpdeskId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
    },
    adminId: {
      type: mongoose.Schema.Types.ObjectId,
    },
    senderId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    message: {
      type: String,
    },
    image: {
      type: String,
    },
  },
  { timestamps: true },
);

helpdeskMessageSchema.plugin(mongoosePaginate);

module.exports = helpdeskMessageSchema;

const mongoose = require('mongoose');

const mutedNotificationsSchema = mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
    },
    contentId: {
      type: String,
      required: true,
    },
    contentType: {
      type: String,
      enum: ['topic', 'user', 'group'],
      required: true,
    },
  },
  { timestamps: true },
);

mutedNotificationsSchema.index({ userId: 1 });
mutedNotificationsSchema.index({ contentId: 1 });
mutedNotificationsSchema.index({ contentType: 1 });
mutedNotificationsSchema.index({ userId: 1, contentId: 1, contentType: 1 });

module.exports = mutedNotificationsSchema;

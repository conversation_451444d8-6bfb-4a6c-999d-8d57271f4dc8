const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const topicsSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    content: {
      type: String,
      required: true,
      index: true,
    },
    image: {
      type: String,
    },
    video: {
      type: String,
    },
    audio: {
      type: String,
    },
    document: {
      type: String,
    },
    attachmentSize: {
      type: String,
    },
    tags: {
      type: Array,
      required: true,
      default: [],
    },
    appropriate: {
      type: Boolean,
      default: true,
    },
    deleted: {
      type: Boolean,
      default: false,
    },
  },

  { timestamps: true },
);

topicsSchema.set('toJSON', {
  transform: function (doc, ret, opt) {
    if (ret['image']) {
      ret['image'] = bucket
        .file(process.env.TOPICS_IMAGE_DIRECTORY + '/' + ret['image'])
        .publicUrl();
    }

    if (ret['video']) {
      ret['video'] = bucket
        .file(process.env.TOPICS_VIDEO_DIRECTORY + '/' + ret['video'])
        .publicUrl();
    }

    if (ret['audio']) {
      ret['audio'] = bucket
        .file(process.env.TOPICS_AUDIO_DIRECTORY + '/' + ret['audio'])
        .publicUrl();
    }

    if (ret['document']) {
      ret['document'] = bucket
        .file(process.env.TOPICS_DOCUMENT_DIRECTORY + '/' + ret['document'])
        .publicUrl();
    }
    return ret;
  },
});

topicsSchema.index({ content: 'text' });
topicsSchema.index({ userId: 1 });
topicsSchema.index({ updatedAt: -1 });
topicsSchema.index({ userId: 1, updatedAt: -1 });

topicsSchema.plugin(mongoosePaginate);
module.exports = topicsSchema;

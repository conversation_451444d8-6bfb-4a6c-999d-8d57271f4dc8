const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { Storage } = require('@google-cloud/storage');
const storage = new Storage();
const bucket = storage.bucket(process.env.GCLOUD_STORAGE_BUCKET);

const topicCommentsSchema = mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
    },
    topicId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    image: {
      type: String,
    },
    video: {
      type: String,
    },
    audio: {
      type: String,
    },
    document: {
      type: String,
    },
    comment: {
      type: String,
    },
    attachmentSize: {
      type: String,
    },
    appropriate: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true },
);

topicCommentsSchema.set('toJSON', {
  transform: function (doc, ret, opt) {
    if (ret['image']) {
      ret['image'] = bucket
        .file(process.env.TOPICS_COMMENT_IMAGE_DIRECTORY + '/' + ret['image'])
        .publicUrl();
    }
    if (ret['video']) {
      ret['video'] = bucket
        .file(process.env.TOPICS_COMMENT_VIDEO_DIRECTORY + '/' + ret['video'])
        .publicUrl();
    }

    if (ret['audio']) {
      ret['audio'] = bucket
        .file(process.env.TOPICS_COMMENT_AUDIO_DIRECTORY + '/' + ret['audio'])
        .publicUrl();
    }

    if (ret['document']) {
      ret['document'] = bucket
        .file(
          process.env.TOPICS_COMMENT_DOCUMENT_DIRECTORY + '/' + ret['document'],
        )
        .publicUrl();
    }

    return ret;
  },
});
topicCommentsSchema.index({ topicId: 1 });
topicCommentsSchema.index({ comment: 'text' });
topicCommentsSchema.index({ userId: 1 });
topicCommentsSchema.index({ topicId: 1, userId: 1 });
topicCommentsSchema.plugin(mongoosePaginate);
module.exports = topicCommentsSchema;

const { dbConnection } = require('../utils/db');

const usersSchema = require('./users');
const tagsSchema = require('./tags');
const topicsSchema = require('./topics');
const convosSchema = require('./convos');
const topicLikesSchema = require('./topicLikes');
const commentLikesSchema = require('./commentLikes');
const topicCommentsSchema = require('./topicComments');
const topicSubCommentsSchema = require('./topicSubComments');
const flaggedTopicsSchema = require('./flaggedTopics');
const mutedNotificationsSchema = require('./mutedNotifications');
const unReadNotificationSchema = require('./unreadNotifications');
const flaggedTopicCommentSchema = require('./flaggedTopicComment');
const flaggedGroupsSchema = require('./flaggedGroups');
const flaggedUsersSchema = require('./flaggedUsers');
const chatsSchema = require('./chats');
const chatMessagesSchema = require('./chatMessages');
const groupsSchema = require('./groups');
const groupMessagesSchema = require('./groupMessages');
const helpdeskSchema = require('./helpdesk');
const helpdeskMessageSchema = require('./helpdeskMessages');
const creditSchema = require('./credit');
const transactionSchema = require('./transaction');
const tokenSchema = require('./token');
const retrySchema = require('./retries');
const followerSchema = require('./followers');
const questionSchema = require('./questions/questions');
const userResponseSchema = require('./questions/userResponse');
const topicSchema = require('./questions/topic');


const userModel = dbConnection.model('Users', usersSchema);
const tagsModel = dbConnection.model('Tags', tagsSchema);
const topicsModel = dbConnection.model('Topics', topicsSchema);
const convosModel = dbConnection.model('Convos', convosSchema);
const topicLikesModel = dbConnection.model('TopicLikes', topicLikesSchema);
const commentLikesModel = dbConnection.model(
  'CommentLikes',
  commentLikesSchema,
);

const topicCommentsModel = dbConnection.model(
  'TopicComments',
  topicCommentsSchema,
);

const topicSubCommentsModel = dbConnection.model(
  'TopicSubComments',
  topicSubCommentsSchema,
);

const flaggedTopicsModel = dbConnection.model(
  'flaggedTopics',
  flaggedTopicsSchema,
);
const flaggedTopicCommentModel = dbConnection.model(
  'flaggedTopicComment',
  flaggedTopicCommentSchema,
);
const flaggedGroupsModel = dbConnection.model(
  'flaggedGroups',
  flaggedGroupsSchema,
);
const flaggedUsersModel = dbConnection.model(
  'flaggedUsers',
  flaggedUsersSchema,
);
const chatsModel = dbConnection.model('Chats', chatsSchema);
const chatMessagesModel = dbConnection.model(
  'ChatMessages',
  chatMessagesSchema,
);
const groupsModel = dbConnection.model('Groups', groupsSchema);
const groupMessagesModel = dbConnection.model(
  'GroupMessages',
  groupMessagesSchema,
);

const mutedNotificationsModel = dbConnection.model(
  'Muted Notifications',
  mutedNotificationsSchema,
);

const unreadNotificationsModel = dbConnection.model(
  'Unread Notifications',
  unReadNotificationSchema,
);

const helpdeskModel = dbConnection.model('Helpdesk', helpdeskSchema);

const helpdeskMessageModel = dbConnection.model(
  'Helpdesk Messages',
  helpdeskMessageSchema,
);

const CreditModel = dbConnection.model('Credits', creditSchema);
const TransactionModel = dbConnection.model('Transactions', transactionSchema);
const TokenModel = dbConnection.model('Tokens', tokenSchema);
const FollowerModel = dbConnection.model('Follower', followerSchema);
const QuestionModel = dbConnection.model('Questions', questionSchema);
const UserResponseModel = dbConnection.model('UserResponse', userResponseSchema);
const TopicModel = dbConnection.model('QuestionTopics', topicSchema);


const RetriesModel = dbConnection.model('Retries', retrySchema);

//Dropping an index
// userModel.collection.dropIndexes({ last_seen: -1 });

module.exports = {
  userModel,
  topicsModel,
  convosModel,
  topicLikesModel,
  commentLikesModel,
  topicCommentsModel,
  topicSubCommentsModel,
  flaggedTopicsModel,
  flaggedTopicCommentModel,
  tagsModel,
  chatsModel,
  chatMessagesModel,
  groupsModel,
  groupMessagesModel,
  flaggedGroupsModel,
  flaggedUsersModel,
  mutedNotificationsModel,
  unreadNotificationsModel,
  helpdeskModel,
  helpdeskMessageModel,
  CreditModel,
  TransactionModel,
  TokenModel,
  RetriesModel,
  QuestionModel,
  UserResponseModel,
  TopicModel,
  FollowerModel,
};

const mongoose = require('mongoose');
const { STARTING_FREE_CREDIT } = require('../constants/credit');

const creditSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    amount: {
      type: Number,
      default: STARTING_FREE_CREDIT,
    },
    lastFreeCreditDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
  },
  { timestamps: true },
);

module.exports = creditSchema;

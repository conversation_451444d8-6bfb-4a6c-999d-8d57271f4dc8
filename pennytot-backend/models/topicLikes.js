const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const topicLikesSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    topicId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
  },
  { timestamps: true },
);

topicLikesSchema.index({ topicId: 1 });
topicLikesSchema.index({ userId: 1 });
topicLikesSchema.index({ topicId: 1, userId: 1 });
topicLikesSchema.plugin(mongoosePaginate);
module.exports = topicLikesSchema;

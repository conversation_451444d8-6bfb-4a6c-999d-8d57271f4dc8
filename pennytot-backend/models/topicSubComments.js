const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const topicSubCommentsSchema = mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
    },
    commentId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    comment: {
      type: String,
    },
    appropriate: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true },
);

topicSubCommentsSchema.index({ comment: 'text' });

topicSubCommentsSchema.plugin(mongoosePaginate);
module.exports = topicSubCommentsSchema;

const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const convosSchema = mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
    },
    topicId: {
      type: String,
      required: true,
    },
  },
  { timestamps: true },
);

convosSchema.index({ userId: 1 });
convosSchema.index({ topicId: 1 });
convosSchema.plugin(mongoosePaginate);

module.exports = convosSchema;

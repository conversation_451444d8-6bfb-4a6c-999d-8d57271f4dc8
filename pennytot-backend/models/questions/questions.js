const mongoose = require('mongoose');

const questionSchema = mongoose.Schema({
    question: {
        type: String,
        required: true
    },
    options: [{
        type: String,
        required: true
    }],
    answer: {
        type: String,
        required: true
    },
    country: {
        type: String,
        required: true,
        default: 'Nigeria'
    }
    // Add other relevant fields as needed
});

module.exports = questionSchema;

const mongoose = require('mongoose');

const userResponseSchema = mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    question: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Question',
        required: true
    },
    selectedOption: {
        type: String,
        required: true
    },
    isCorrect: {
        type: Boolean,
        required: true
    },
    // Add other relevant fields as needed
});

module.exports = userResponseSchema;

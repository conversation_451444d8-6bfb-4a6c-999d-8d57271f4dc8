// require('@google-cloud/debug-agent').start({
//   serviceContext: { enableCanary: true },
// });
const express = require('express');
const app = express();
const cors = require('cors');
const cookieParser = require('cookie-parser');
const loaddb = require('./utils/db');
const environmentvariable = require('dotenv/config');
const fileUpload = require('express-fileupload');
const busboy = require('express-busboy');
const http = require('http').Server(app);
const cron = require('node-cron');
const { sendAllNotification } = require('./controllers/notification');
require('./controllers/chatSocket')(http, app);

app.use(cors());

// var corsOptions = {
//   origin: process.env.FRONTEND_URL,
//   optionsSuccessStatus: 200,  some legacy browsers (IE11, various SmartTVs) choke on 204
// };

// app.use(cors(corsOptions));

const port = process.env.PORT || 8000;

//Import Route
const appRoute = require('./routes/app');
const userRoute = require('./routes/user');
const topicsRoute = require('./routes/topics');
const tagsRoute = require('./routes/tags');
const chatsRoute = require('./routes/chats');
const groupsRoute = require('./routes/groups');
const helpdeskRoute = require('./routes/helpdesk');
const creditRoute = require('./routes/credit');
const flutterwaveRoute = require('./routes/flutterwave');
const followersRoute = require('./routes/followers');
const notificationsRoute = require('./routes/notification');
const quiz = require('./routes/quiz');

app.get('/', (req, res) => {
  res
    .status(200)
    .json('This is nothing to see here. Just an indication the server works.');
});

app.get('/success', (req, res) => {
  res.status(200).json({ status: 'success' });
});

app.use('/app', appRoute);
app.use('/user', userRoute);
app.use('/topics', topicsRoute);
app.use('/tags', tagsRoute);
app.use('/chats', chatsRoute);
app.use('/group', groupsRoute);
app.use('/helpdesk', helpdeskRoute);
app.use('/credit', creditRoute);
app.use('/flutterwave', flutterwaveRoute);
app.use('/followers', followersRoute);
app.use('/notifications', notificationsRoute);
app.use('/quiz', quiz);

cron.schedule('0 4 * * *', () => {
  console.log('Running a daily job at 4:00 West Africa Time (UTC+1)');
  sendAllNotification();
}, {
  scheduled: true,
  timezone: "Africa/Lagos"
});

cron.schedule('0 16 * * *', () => {
  console.log('Running a daily job at 16:00 West Africa Time (UTC+1)');
  sendAllNotification();
}, {
  scheduled: true,
  timezone: "Africa/Lagos"
});

//admin routes
require('./routes/admin')(app);

//Middlewares

app.use(express.json());
app.use(cookieParser());
app.use(fileUpload());
busboy.extend(app, {
  upload: true,
  path: '/',
  allowedPath: /./,
});

//start listener
http.listen(port, function () {
  console.log('Server started successfully on port: ' + port);
});

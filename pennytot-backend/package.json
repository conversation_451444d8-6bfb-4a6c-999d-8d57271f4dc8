{"name": "express", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app.js", "dev": "nodemon app.js"}, "license": "MIT", "dependencies": {"@google-cloud/storage": "^5.8.5", "bcrypt": "^5.0.0", "bcryptjs": "^2.4.3", "bluebird": "^3.7.2", "body-parser": "^1.19.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^8.2.0", "expo-server-sdk": "^3.7.0", "express": "^4.17.1", "express-busboy": "^8.0.0", "express-fileupload": "^1.2.1", "express-jwt": "^6.0.0", "express-recaptcha": "^5.1.0", "file-type": "^16.4.0", "firebase-admin": "^10.0.2", "flutterwave-node-v3": "^1.0.12", "handlebars": "^4.7.7", "joi": "^17.8.3", "jsonwebtoken": "^8.5.1", "jwks-rsa": "^1.10.1", "mime-types": "^2.1.30", "moment": "^2.29.4", "mongoose": "^6.5.0", "mongoose-paginate-v2": "^1.3.18", "mongoose-unique-validator": "^2.0.3", "multer": "^1.4.2", "node-cron": "^3.0.3", "nodemailer": "^6.4.13", "nodemon": "^2.0.4", "postmark": "^3.0.15", "qrcode": "^1.4.4", "socket.io": "^4.4.0", "speakeasy": "^2.0.0", "swagger-jsdoc": "^4.2.3", "swagger-ui-express": "^4.1.4"}, "engines": {"node": "14"}}
const moment = require('moment');

function formatTimeToDuration(seconds) {
  const duration = moment.duration(seconds, 'seconds');

  if (duration.asDays() >= 1) {
    return Math.round(duration.asDays()) + ' days';
  } else if (duration.asHours() >= 1) {
    return Math.round(duration.asHours()) + ' hours';
  } else if (duration.asMinutes() >= 1) {
    return Math.round(duration.asMinutes()) + ' minutes';
  } else {
    return Math.round(duration.asSeconds()) + ' seconds';
  }
}

module.exports = {
  formatTimeToDuration,
};

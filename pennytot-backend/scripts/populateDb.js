const mongoose = require('mongoose');
const { dbConnection } = require('../utils/db'); // Adjust the path as needed
const {
  topicsModel,
} = require('../models');

// Use the connection established by your dbConnection module
const db = dbConnection; // This is now the connection object from makeNewConnection

const createTestData = async () => {
  try {
    for (let i = 1; i <= 40; i++) {
      const newTopic = new topicsModel({
        userId: '6597c89efa94557375f18e1d', // Mock user ID
        content: `Test content ${i}`,
        image: `image_${i}.jpg`,
        video: `video_${i}.mp4`,
        audio: `audio_${i}.mp3`,
        document: `doc_${i}.pdf`,
        attachmentSize: `${Math.floor(Math.random() * 100)}MB`,
        tags: ['test', 'topic'],
        appropriate: true,
        deleted: false,
      });

      await newTopic.save();
    }
    console.log('Test data inserted successfully.');
  } catch (error) {
    console.error('Error inserting test data:', error);
  } finally {
    db.close();
  }
};

createTestData();
const jwt = require('jsonwebtoken');
const { userModel } = require('../models/index');

const userAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization.split(' ')[1];
    const decode = jwt.verify(token, process.env.TOKEN_SECRET);
    req.user = decode;

    let user = await userModel.findById(req.user.id);

    if (!user) {
      res.status(401).json({
        message: 'Account no longer exist or has been deactivated',
      });
      return;
    }

    if (user.suspended) {
      res.status(401).json({
        errorStatus: 'suspended',
        message: 'Your account has suspended, you cant perform this operation',
      });
      return;
    }

    req.userAccount = user;
    next();
  } catch (error) {
    res.status(403).json({
      message: 'Authentication Failed!',
    });
  }
};

module.exports = userAuth;

const { groupsModel } = require('../../models');

const validateGroupAdmin = async (req, res, next) => {
  try {
    // console.log(req.params.groupId, 'groupId');
    // console.log(req.user.id, 'userId');
    let group = await groupsModel.findOne({
      $and: [{ _id: req.params.groupId }, { admins: req.user.id }],
    });

    if (!group) {
      res.status(401).json({
        message: 'You dont have permission to perform this operation',
      });
      return;
    }

    req.groupDetails = group;
    next();
  } catch (error) {
    res.status(403).json({
      message: 'Group Validation Failed!',
    });
  }
};

module.exports = validateGroupAdmin;

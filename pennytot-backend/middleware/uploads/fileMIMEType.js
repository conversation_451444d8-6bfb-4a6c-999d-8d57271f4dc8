const FileType = require('file-type');

const ImageFileCheck = async (req, res, next) => {
  if (!req.file) {
    //  res.status(400).send({ message: "No file uploaded." });
    next();
    return;
  }

  // check file type
  let path = await FileType.fromFile(req.file.path);
  if (path) {
    let fileType = JSON.parse(JSON.stringify(path)).mime;

    if (!fileType.match(/(image\/png|image\/jpg|image\/jpeg)$/)) {
      res.status(400).send({ message: 'Only image files are allowed!' });
      return;
    }
  } else {
    res
      .status(400)
      .send({ message: 'Something went wrong, file is corrupt or unreadable' });
    return;
  }

  next();
};

const AudioFileCheck = async (req, res, next) => {
  if (!req.file) {
    //  res.status(400).send({ message: "No file uploaded." });
    next();
    return;
  }

  // check file type
  let path = await FileType.fromFile(req.file.path);
  if (path) {
    let fileType = JSON.parse(JSON.stringify(path)).mime;
    // console.log('see file', fileType);
    if (
      !fileType.match(
        /(audio\/basic|audio\/mpeg|audio\/mp4|audio\/x-aiff|audio\/mid|audio\/vnd.wav|audio\/vorbis|audio\/ogg|audio\/vnd.rn-realaudio|audio\/x-mpegurl|audio\/L24)$/,
      )
    ) {
      res.status(400).send({ message: 'Audio format not supported!' });
      return;
    }
  } else {
    res
      .status(400)
      .send({ message: 'Something went wrong, file is corrupt or unreadable' });
    return;
  }

  next();
};

const VideoFileCheck = async (req, res, next) => {
  if (!req.file) {
    //  res.status(400).send({ message: "No file uploaded." });
    next();
    return;
  }

  // check file type
  let path = await FileType.fromFile(req.file.path);
  if (path) {
    let fileType = JSON.parse(JSON.stringify(path)).mime;
    // console.log('see file', fileType);
    if (!fileType.match(/(video\/mp4|video\/3gpp|video\/x-msvideo)$/)) {
      res.status(400).send({ message: 'Video format not supported!' });
      return;
    }
  } else {
    res
      .status(400)
      .send({ message: 'Something went wrong, file is corrupt or unreadable' });
    return;
  }

  next();
};

const attachmentFileCheck = async (req, res, next) => {
  if (req.files) {
    if (req.files.length > 0) {
      for (let i = 0; i < req.files.length; i++) {
        let path = await FileType.fromFile(req.files[i].path);

        if (path) {
          let fileType = JSON.parse(JSON.stringify(path)).mime;

          if (
            !fileType.match(
              /(application\/pdf|image\/png|image\/jpg|image\/jpeg)$/,
            )
          ) {
            res
              .status(400)
              .send({ message: 'Only pdf and word file(s) are allowed!' });
            return;
          }
        } else {
          res.status(400).send({
            message: 'Something went wrong, file(s) is corrupt or unreadable',
          });
          return;
        }
      }
    }
  }

  next();
};

const DocumentFileCheck = async (req, res, next) => {
  if (!req.file) {
    //  res.status(400).send({ message: "No file uploaded." });
    next();
    return;
  }

  // check file type
  let path = await FileType.fromFile(req.file.path);
  if (path) {
    let fileType = JSON.parse(JSON.stringify(path)).mime;

    if (!fileType.match(/(image\/png|image\/jpg|image\/jpeg)$/)) {
      res.status(400).send({ message: 'Only image files are allowed!' });
      return;
    }
  } else {
    res
      .status(400)
      .send({ message: 'Something went wrong, file is corrupt or unreadable' });
    return;
  }

  next();
};

module.exports = {
  ImageFileCheck,
  AudioFileCheck,
  VideoFileCheck,
  attachmentFileCheck,
  DocumentFileCheck,
};

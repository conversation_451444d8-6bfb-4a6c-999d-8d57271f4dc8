const multer = require('multer');
const { storage } = require('./storage');

const documentUpload = multer({
  storage: storage,
  limits: { fileSize: process.env.DOCUMENT_ATTACHMENT_SIZE_LIMIT },
  // fileFilter: uploadType,
}).single('file');

const documentRequest = async (req, res, next) => {
  try {
    documentUpload(req, res, function (err) {
      if (err) {
        res.status(400).json({
          message: err.message,
        });
        return;
      }

      // Everything went fine.
      next();
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error,
    });
  }
};

module.exports = documentRequest;

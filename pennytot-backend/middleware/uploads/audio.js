const multer = require('multer');
const { storage } = require('./storage');
const { audioTypes } = require('./fileExtensionTypes');

const attachmentUpload = multer({
  storage: storage,
  limits: { fileSize: process.env.ATTACHMENT_SIZE_LIMIT },
  fileFilter: audioTypes,
}).single('file');

const attachmentRequest = async (req, res, next) => {
  try {
    attachmentUpload(req, res, function (err) {
      if (err) {
        res.status(400).json({
          message: err.message,
        });
        return;
      }

      // Everything went fine.
      next();
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      message: error,
    });
  }
};

module.exports = attachmentRequest;

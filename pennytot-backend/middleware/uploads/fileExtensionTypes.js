const audioTypes = async function (req, file, cb) {
  //check audio extension

  console.log(file.originalname, 'audio check mo');
  if (
    !file.originalname.match(
      /\.(3g2|3gp|aac|adt|adts|aif|aifc|aiff|asf|au|m3u|m4a|m4b|mid|midi|mp2|mp3|mp4|rmi|snd|wav|wax|wma)$/,
    )
  ) {
    req.fileValidationError = 'Invalid Extension!';
    return cb(new Error('Invalid Extension!'), false);
  }

  cb(null, true);
};

const imageTypes = async function (req, file, cb) {
  // Accept images only
  //check image extension
  if (!file.originalname.match(/\.(jpg|JPG|jpeg|JPEG|png|PNG)$/)) {
    req.fileValidationError = 'Invalid Extension!';
    return cb(new Error('Invalid Extension!'), false);
  }

  cb(null, true);
};

const videoTypes = async function (req, file, cb) {
  // Accept images only
  //check image extension
  console.log(file, 'make i see first');
  if (!file.originalname.match(/\.(mp4|3gp|avi|mov|mkv|wmv)$/)) {
    req.fileValidationError = 'Invalid Extension!';
    return cb(new Error('Invalid Extension!'), false);
  }

  cb(null, true);
};

module.exports = {
  audioTypes,
  videoTypes,
  imageTypes,
};

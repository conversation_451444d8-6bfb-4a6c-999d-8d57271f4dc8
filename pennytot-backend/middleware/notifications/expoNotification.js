const { Expo } = require('expo-server-sdk');
let expo = new Expo();

async function handleExpoNotification({ token, title, body, data }) {
  let messages = [];
  if (!Expo.isExpoPushToken(token)) {
    console.error(`Push token ${token} is not a valid Expo push token`);
    return;
  }

  messages.push({
    to: token,
    sound: 'default',
    title: title,
    body: body,
    data: data,
  });

  let chunks = expo.chunkPushNotifications(messages);
  for (let chunk of chunks) {
    try {
      await expo.sendPushNotificationsAsync(chunk);
      // Handle the receipts if necessary
    } catch (error) {
      console.error(error);
    }
  }
}
module.exports = handleExpoNotification;

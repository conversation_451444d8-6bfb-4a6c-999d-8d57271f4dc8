const { userModel, mutedNotificationsModel, groupsModel } = require('../../models');
const handleExpoNotification = require('./expoNotification'); // Import Expo notification handler

async function handleGroupNotification(payload, sender) {
  let group = await groupsModel.findById(payload.groupId);
  let participants = group.participants.filter((data) => data != sender.id);
  let participantsToBeNotified = [];

  if (group && group.participants && group.participants.length > 0) {
    for (let i in participants) {
      let checkReceiverMutedList = await mutedNotificationsModel.find({
        contentType: 'group',
        contentId: payload.groupId,
        userId: participants[i],
      });

      if (checkReceiverMutedList.length == 0) {
        participantsToBeNotified.push(participants[i]);
      }
    }
  }

  if (participantsToBeNotified.length > 0) {
    let subscribersToken = await userModel
      .find({ _id: { $in: participantsToBeNotified } })
      .distinct('device_token');

    let userInfo = await userModel.findById(sender.id);
    let senderName = userInfo?.first_name + ' ' + userInfo?.last_name;

    if (subscribersToken.length > 0) {
      // Send a notification to each token
      for (const token of subscribersToken) {
        await handleExpoNotification({
          token: token,
          title: group.name,
          body: `${senderName}: ${payload.message}`,
          type: 'group',
          screenData: { groupDetails: group },
        });
      }
    }
  }
}

module.exports = handleGroupNotification;

const {
  userModel,
  topicsModel,
  mutedNotificationsModel,
} = require('../../models');
const handleExpoNotification = require('./expoNotification'); // Update the import to use Expo notification handler

async function handleCommentNotification(req, topicSubscribers) {
  let topicId = req.params.topicId;

  let checkReceiverMutedList = await mutedNotificationsModel.find({
    contentType: 'topic',
    contentId: topicId,
    userId: req.user.id,
  });

  if (checkReceiverMutedList && checkReceiverMutedList.length > 0) {
    return;
  }

  let userInfo = await userModel.findById(req.user.id);
  let topicInfo = await topicsModel.findById(topicId);

  if (topicSubscribers.length > 0) {
    let subscribersToken = await userModel
      .find({ _id: { $in: topicSubscribers } })
      .distinct('device_token');

    if (subscribersToken.length < 1) {
      return;
    }

    let filteredTokens = subscribersToken.filter(
      (token) => token !== userInfo.device_token,
    );

    if (filteredTokens.length > 0) {
      // Loop through each token and send notification
      for (const token of filteredTokens) {
        await handleExpoNotification({
          token: token,
          title: 'NEW COMMENT',
          body:
            `${userInfo.first_name} ${userInfo.last_name} commented on: ${topicInfo.content}`,
          type: 'topic',
          screenData: { topicId },
        });
      }
    }
  }
}

module.exports = handleCommentNotification;

const admin = require('firebase-admin');

const serviceAccount = require('../../google-auth.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

// PS: the data payload can only accept strings

async function handleNotification({ token, title, body, type, screenData }) {
  try {
    await admin.messaging().sendToDevice(
      token,
      {
        data: {
          title,
          body,
          sound: 'default',
          type,
          screenData: screenData ? JSON.stringify(screenData) : '{}',
        },
        // notification: { title, body, sound: "default" },
      },
      { collapseKey: title },
    );
  } catch (err) {
    console.log(err);
  }
}

module.exports = handleNotification;

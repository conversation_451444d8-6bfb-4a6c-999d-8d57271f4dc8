const { userModel, mutedNotificationsModel } = require('../../models');
// const handleNotification = require('./notifications');
const handleExpoNotification = require('./expoNotification');

async function handleChatNotification(payload, sender) {
  let checkReceiverMutedList = await mutedNotificationsModel.find({
    contentType: 'user',
    contentId: sender.id,
    userId: payload.accountId,
  });

  //if receiver doesn't want notifications from sender
  if (checkReceiverMutedList && checkReceiverMutedList.length > 0) {
    return;
  }

  let senderInfo = await userModel.findById(sender.id);
  let receiverInfo = await userModel.findById(payload.accountId);

  let senderName = senderInfo?.first_name + ' ' + senderInfo?.last_name;

  if (receiverInfo?.device_token) {
    handleExpoNotification({
      token: receiverInfo.device_token,
      title: senderName,
      body: payload.message,
      type: 'chat',
      screenData: { userDetails: senderInfo },
    });
  }
}

module.exports = handleChatNotification;

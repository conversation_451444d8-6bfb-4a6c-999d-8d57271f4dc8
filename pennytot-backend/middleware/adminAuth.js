const jwt = require('jsonwebtoken');
const { adminModel } = require('../models/admin');

const PERMISSIONS = [
  'CAN_VIEW_DASHBOARD',
  'CAN_MANAGE_HELPDESK',
  'CAN_MANAGE_TOPICS',
  'CAN_MANAGE_TOPIC_COMMENTS',
  'CAN_MANAGE_USERS',
  'CAN_MANAGE_GROUPS',
  'CAN_MANAGE_ADMIN_ACTIVITIES',
  'CAN_MANAGE_APP_ADMIN',
  'CAN_VIEW_APP_LOGS',
  'ONLY_SUPER_ADMIN',
];

module.exports = ({ permissions }) => {
  return async (req, res, next) => {
    try {
      const token = req.headers.authorization.split(' ')[1];
      const decode = jwt.verify(token, process.env.ADMIN_TOKEN_SECRET);
      req.user = decode;

      let user = await adminModel.findById(req.user.id);

      if (!user) {
        res.status(401).json({
          message: 'Account no longer exist or has been deactivated',
        });
        return;
      }

      if (user.suspended) {
        res.status(401).json({
          errorStatus: 'suspended',
          message:
            'Your account has suspended, you cant perform this operation',
        });
        return;
      }

      const isPermitted = permissions.every((item) => {
        return user.permissions.includes(item);
      });

      //check permissions
      if (user.super_admin || isPermitted) {
        req.adminAccount = user;
        next();
      } else {
        res.status(401).json({
          message:
            'You dont have the permission to cant perform this operation',
        });
        return;
      }
    } catch (error) {
      res.status(403).json({
        message: 'Authentication Failed!',
      });
    }
  };
};

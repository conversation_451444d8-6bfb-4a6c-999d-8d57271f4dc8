const { CreditModel } = require('../models');
const credits = require('../constants/credit');

async function checkCredit({ source, userId }) {
  try {
    const credit = await CreditModel.findOne({ userId });

    if (credit) {
      if (credit.amount >= credits[source]) {
        return true;
      } else {
        throw "You don't have enough credit to carry out this action";
      }
    } else {
      throw 'Something went wrong cant find credit account';
    }
  } catch (error) {
    throw error;
  }
}

async function deductCredit({ source, userId }) {
  try {
    await CreditModel.updateOne(
      { userId },
      {
        $inc: {
          amount: -credits[source],
        },
      },
      { new: true },
    );
    return true;
  } catch (error) {
    throw error;
  }
}

module.exports = {
  checkCredit,
  deductCredit,
};

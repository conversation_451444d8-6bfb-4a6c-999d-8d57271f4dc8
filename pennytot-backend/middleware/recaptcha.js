const fetch = require('node-fetch');

function verifyRecaptcha(req, res, next) {
  try {
    const VERIFY_URL = 'https://www.google.com/recaptcha/api/siteverify';

    return fetch(VERIFY_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: `secret=${process.env.MAIN_PAGE_GOOGLE_RECAPTCHA}&response=${req.body['g-recaptcha-response']}`,
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          return next();
        } else {
          throw 'Recaptcha verification failed';
        }
      });
  } catch (error) {
    console.log(error);
    res.status(403).json({
      message: 'Recaptcha verification failed',
    });
  }
}

module.exports = verifyRecaptcha;

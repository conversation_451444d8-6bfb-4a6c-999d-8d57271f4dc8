module.exports = (validator, source) => {
  return (req, res, next) => {
    let requestData = {};

    if (source == 'body') {
      requestData = req.body;
    } else if (source == 'query') {
      requestData = req.query;
    }

    const { error } = validator(requestData);

    console.log('error: ', error);

    if (error) {
      const errorMessage = error.details[0].message
        .replace('"', '')
        .replace('"', '');

      return res.status(400).json({ message: errorMessage });
    }
    next();
  };
};

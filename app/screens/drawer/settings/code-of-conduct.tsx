import React from 'react';
import { ScrollView, StatusBar, View, SafeAreaView } from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import WebView from 'react-native-webview';

const CodeOfConduct = ({ navigation }:any) => {
  const codeOfConductHTML = `
  <html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body style="font-size: 14px; line-height: 1.6; max-width: 100%">
    <p>Pennytots has an obligation to conduct its business in accordance with all applicable rules, regulations and laws. We are committed to helping all Users act in a way that preserves trust and respect. This Code is meant as a guide to using our Site appropriately and must be followed at all times. Breaches of this Code are handled according to our Violations Policy and will result in disciplinary action, up to, and including, account termination. Any questions regarding this Code should be addressed to the Pennytots Support Team where we can provide you with additional information regarding the correct procedure(s) to follow, and address any concerns you may have.</p>

    <p><strong>Personal Behavior</strong></p>
    <ul>
      <li>I will act ethically and with integrity.</li>
      <li>I will comply with all of Pennytots's policies.</li>
      <li>I will respect the rights of all Users.</li>
      <li>I will not abuse confidential information, or participate in any other illegal practice.</li>
      <li>I will have regard for Users' interests, rights and safety.</li>
      <li>I will not harass, bully or discriminate.</li>
      <li>I will not falsify my own or any other identity and I will provide true and correct information.</li>
    </ul>

    <p><strong>User Content</strong></p>
    <ul>
      <li>I am responsible for the content I post on Pennytots and:</li>
      <li>I will not post content that infringes upon any copyright or other intellectual property rights.</li>
      <li>I will not post content that violates any law or regulation.</li>
      <li>I will not post content that is defamatory.</li>
      <li>I will not post content that is obscene or contains child pornography.</li>
      <li>I will not post content that includes incomplete, false or inaccurate information about any person.</li>
      <li>I will not post content that contains any viruses or programming routines intended to damage any system.</li>
      <li>I will not post content that creates liability for Pennytots or harms its business operations or reputation.</li>
    </ul>

    <p><strong>Confidentiality</strong></p>
    <ul>
      <li>I will respect confidentiality and privacy.</li>
      <li>I will not disclose information or documents I have acquired, other than as required by law or where authorization is given by Pennytots.</li>
    </ul>

    <p><strong>Fraud</strong></p>
    <ul>
      <li>I will not engage in fraud.</li>
      <li>I will not use the Site to illegally transfer funds.</li>
      <li>I will not use the Site to generate false feedback.</li>
    </ul>

    <p><strong>Communication</strong></p>
    <ul>
      <li>I will avoid exaggeration, derogatory remarks, and inappropriate references.</li>
      <li>I will not engage in personal attacks, negative or other unfair criticism, and any unprofessional conduct.</li>
    </ul>

    <p><strong>Advertising</strong></p>
    <ul>
      <li>I will not advertise products or services that are illegal.</li>
      <li>I will not advertise anything intended to promote illegal behaviour.</li>
    </ul>

    <p><strong>Affiliates</strong></p>
    <ul>
      <li>I will not obtain names from mailing lists, group emails, etc to send unsolicited emails ("Spam").</li>
    </ul>

    <p><strong>Payments</strong></p>
    <ul>
      <li>I will not use Pennytots to facilitate money exchange.</li>
    </ul>
  </body>
  </html>
  `;

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      <ScrollView style={{ backgroundColor: '#ffffff' }} contentContainerStyle={{ flex: 1 }}>
        <StatusBar barStyle='dark-content' backgroundColor='white' />
        <HeaderTitle title='Code of Conduct' navigation={navigation} />
        <View style={{ flex: 1, paddingHorizontal: 20 }}>
          <WebView source={{ html: codeOfConductHTML }} style={{ flex: 1 }} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default CodeOfConduct;
import { CustomText } from 'app/components/elements';
import React, { useState } from 'react';
import { ScrollView, StatusBar, View, SafeAreaView, ActivityIndicator } from 'react-native';
import HeaderTitle from 'app/components/HeaderTitle';
import WebView from 'react-native-webview';
import { useTranslation } from 'react-i18next';

type PrivacyPolicyProps = {
  navigation: any;
};

const PrivacyPolicy: React.FC<PrivacyPolicyProps> = ({ navigation }) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      <ScrollView style={{ backgroundColor: '#ffffff' }} contentContainerStyle={{ flex: 1 }}>
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        <HeaderTitle title="Pennytots Privacy Policy" navigation={navigation} />
        <View style={{ flex: 1, paddingHorizontal: 20 }}>
          <WebView
            source={{ uri: 'https://app.pennytots.com/privacy-Policy' }}
            style={{ flex: 1, opacity: isLoading ? 0 : 1 }}
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
          />
          {isLoading && (
            <View style={{ 
              position: 'absolute', 
              top: 0, 
              left: 0, 
              right: 0, 
              bottom: 0, 
              justifyContent: 'center', 
              alignItems: 'center' 
            }}>
              <ActivityIndicator size="large" color="#163E23" />
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default PrivacyPolicy;
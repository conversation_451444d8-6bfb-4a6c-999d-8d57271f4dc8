import React, { useState, useEffect } from 'react';
import {
  View,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import {
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { CustomText, CustomButton } from 'app/components/elements';
import Loader from 'app/components/elements/Loader';
import { CommonStyles } from 'app/assets/styles';
import { Axios } from 'app/api/axios';
import { showModal, hideModal } from 'app/providers/modals';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import ProfessionalSVG from '@/app/assets/svg/connectifyHorizontallogo.svg';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Company: React.FC<{ navigation: any }> = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [company, setCompany] = useState('');
  const [company_position, setCompanyPosition] = useState('');
  const { t } = useTranslation();

  const updateProfile = () => {
    // Check if fields are filled
    if (!company || !company_position) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Please fill in all fields before proceeding.',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
      });
      return;
    }

    setLoading(true);

    const formData = {
      company_position,
      company,
    };
  


    Axios({
      method: 'POST',
      url: '/user/update-profile',
      data: formData,

    })
      .then(() => {
        showModal({
          modalVisible: true,
          title: 'Success',
          message: 'Updated successfully',
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
        });
        navigation.navigate('status');
      })
      .finally(() => setLoading(false));
  };

  const saveFormData = async () => {
    try {
      await AsyncStorage.setItem('personalformData', JSON.stringify({ company, company_position }));
    } catch (error) {
      console.error('Error saving form data', error);
    }
  };

  const loadFormData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('personalformData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setCompany(parsedData.company || '');
        setCompanyPosition(parsedData.company_position || '');
       
      }
    } catch (error) {
      console.error('Error loading form data', error);
    }
  };
  
  // Call loadFormData when the component mounts
  useEffect(() => {
    loadFormData();
  }, []);
  
  // Update storage when values change
  useEffect(() => {
    saveFormData();
  }, [company, company_position]);
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >

      <ScrollView style={{ flex: 1, backgroundColor: 'white', paddingHorizontal: wp('5') }}>
        <Loader loading={loading} />
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingTop: rv(10) }}>
          <CustomText style={{ color: '#696969', fontSize: rv(21), fontFamily: 'medium' }}>
            {t('Signup_beProfessional')}
          </CustomText>
          <ProfessionalSVG />
        </View>

        <CustomText style={{ marginTop: rv(12), color: '#696969', fontSize: rv(13), fontFamily: 'medium' }}>
        Showcase your career—tell us where you work and your role.
        </CustomText>

        <View style={{ marginTop: rv(32) }}>
          <CustomText style={{ color: '#110F05', fontSize: rv(12), fontFamily: 'medium' }}>
            {t('Signup_placeOfWork')}
          </CustomText>
          <TextInput
            style={CommonStyles.inputField}
            onChangeText={setCompany}
            placeholder={t('profile_company')}
            value={company}
          />
        </View>

        <View style={{ marginTop: rv(20) }}>
          <CustomText style={{ color: '#110F05', fontSize: rv(12), fontFamily: 'medium' }}>
            {t('Signup_jobTitle')}
          </CustomText>
          <TextInput
            style={CommonStyles.inputField}
            onChangeText={setCompanyPosition}
            placeholder={t('Signup_jobTitle')}
            value={company_position}
          />
        </View>

        <View style={{ marginTop: rv(130), flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <TouchableOpacity onPress={()=> navigation.navigate('personal')}>
            <CustomText
              style={{ color: "#000000", fontSize: rv(13) }}
              textType="medium"
            >
              Back
            </CustomText>
          </TouchableOpacity>
          <CustomButton
            label={t('Signup_nextbtn')}
            onPress={updateProfile}
            style={{ width: '30%', maxWidth: rv(118), borderRadius: rv(12) }}
            loading={loading}
          />
        </View>

      </ScrollView>
    </KeyboardAvoidingView>
  );
};
const styles: any = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: 'white',
    paddingTop: rv(10)
  },
})

export default Company;

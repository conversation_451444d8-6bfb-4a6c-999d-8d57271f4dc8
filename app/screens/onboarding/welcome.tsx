import React, { FunctionComponent, useState, useEffect } from 'react';
import {
  View,
  TextInput,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Platform,
  KeyboardAvoidingView,
  Image,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { SignUpHeaderSVG } from 'app/providers/svg/loader';
import { CustomText, CustomButton } from 'app/components/elements';
import Loader from 'app/components/elements/Loader';
import { CommonStyles } from 'app/assets/styles';
import { Axios } from 'app/api/axios';
import { showModal, hideModal } from 'app/providers/modals';
import SelectDropdown from 'react-native-select-dropdown';
// import Icon from 'react-native-vector-icons/FontAwesome';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import { userConstants } from '../../constants/user';
import { useSelector } from 'react-redux';
import { userAuthInfo } from 'app/redux/user/reducer';
import { useTranslation } from 'react-i18next';
import Select from '../../components/elements/Select';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import TellUsMoreSVG from '@/app/assets/svg/connectifyHorizontallogo.svg';
import countries from 'world-countries';
import ReactFlagsSelect from 'react-flags-select';
import CountryPicker, { Country } from 'react-native-country-picker-modal';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Ionicons from '@expo/vector-icons/Ionicons';

const Welcome: FunctionComponent<{
  navigation: any;
  data: any;
}> = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [gender, setGender] = useState('');
  const [age, setAge] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<{
    name: string;
    flag: string;
    cca2: string;
  }>({
    name: 'Nigeria', // Default country name
    flag: '🇳🇬', // Default flag for Nigeria
    cca2: 'NG', // Country code for Nigeria
  });

  const [isCountryPickerVisible, setCountryPickerVisible] = useState(false);

  const [isReady, setIsReady] = useState(false);

  const user = useSelector(userAuthInfo);
  const { t } = useTranslation();
  const saveFormData = async () => {
    try {
      await AsyncStorage.setItem(
        'personalformData',
        JSON.stringify({ city, state, gender, age, selectedCountry })
      );
    } catch (error) {
      console.error('Error saving form data', error);
    }
  };

  const loadFormData = async () => {
    try {
      const storedData = await AsyncStorage.getItem('personalformData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setCity(parsedData.city || '');
        setState(parsedData.state || '');
        setGender(parsedData.gender || '');
        setAge(parsedData.age || '');
        setSelectedCountry(
          parsedData.selectedCountry || {
            name: 'Nigeria',
            flag: '🇳🇬',
            cca2: 'NG',
          }
        );
      }
    } catch (error) {
      console.error('Error loading form data', error);
    }
  };

  // Call loadFormData when the component mounts
  useEffect(() => {
    loadFormData();
  }, []);

  // Update storage when values change
  useEffect(() => {
    saveFormData();
  }, [city, state, gender, age, selectedCountry]);

  // Build a country list for the <Select> component
  // const countryList = countries.map((country) => ({
  //   label: country.name.common, // Display name
  //   value: country.name.common, // Unique value
  // }));

  const handleCountryChange = (country: Country) => {
    setSelectedCountry({
      name: country.name,
      flag: country.flag,
      cca2: country.cca2, // Ensure cca2 is set properly
    });
  };

  useEffect(() => {
    // If user already has interests, skip this screen
    if (user && user.interests && user.interests.length > 0) {
      navigation.replace('Home');
    } else {
      setIsReady(true);
    }
  }, [user]);

  if (!isReady) {
    return null;
  }

  const updateProfile = () => {
    // Validate required fields
    if (!gender) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Gender is required',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
      });
      return;
    }

    if (!age) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Age is required',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
      });
      return;
    }

    if (!state || !city || !selectedCountry) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Please fill all the fields to proceed.',
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
      });
      return;
    }

    setLoading(true);

    const formData = {
      city,
      state,
      age,
      gender,
      country: selectedCountry?.name,
    };
    console.log(selectedCountry);
    Axios({
      method: 'POST',
      url: '/user/update-profile',
      data: formData,
    })
      .then(() => {
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: 'Updated successfully',
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
        });
        navigation.navigate('personal');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <KeyboardAvoidingView
  behavior={Platform.OS === 'ios' ? 'padding' : 'height'} // Adjust behavior based on platform
  style={{ flex: 1 }} // Full screen
>
  <ScrollView
    contentContainerStyle={styles.scrollview2} // Ensure content expands
    keyboardShouldPersistTaps='handled' // Dismiss keyboard on tap outside
  >
    <SafeAreaView style={styles.scrollview}>
      <Loader loading={loading} />

      {/* Page Header */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
        <View style={{ width: '50%' }}>
          <CustomText
            style={{
              color: '#696969',
              fontSize: rv(21),
              fontFamily: 'medium'
            }}
            textType='medium'
          >
            {t('Signup_tellUsMore')}
          </CustomText>
        </View>
        <View style={{ width: '50%' }}>
          <TellUsMoreSVG style={{ alignSelf: 'flex-end' }} />
        </View>
      </View>

      {/* Page Description */}
      <View style={{ flexDirection: 'column', marginTop: rv(12) }}>
        <CustomText
          style={{
            fontSize: rv(13),
            marginBottom: 20,
            color: '#696969',
            fontFamily: 'medium'
          }}
          textType='regular'
        >
          Help us get to know you better.
        </CustomText>

        {/* Age Dropdown */}
        <View style={{ flexDirection: 'column' }}>
          <CustomText
            style={{
              color: '#110F05',
              fontSize: rv(12),
              fontFamily: 'medium'
            }}
            textType='medium'
          >
            {t('Signup_age')}
          </CustomText>
          <Select
            data={userConstants.ageRange}
            value={age}
            onSelect={(value: string, index: number) => setAge(value)}
          />
        </View>

        {/* Gender Dropdown */}
        <View style={{ flexDirection: 'column' }}>
          <CustomText
            style={{
              color: '#110F05',
              fontSize: rv(12),
              fontFamily: 'medium'
            }}
            textType='medium'
          >
            {t('Signup_gender')}
          </CustomText>
          <Select
            data={userConstants.genders}
            value={gender}
            onSelect={(value: string, index: number) => setGender(value)}
          />
        </View>

        {/* Town Input */}
        <View style={{ flexDirection: 'column' }}>
          <CustomText
            style={{
              color: '#110F05',
              fontSize: rv(12),
              fontFamily: 'medium'
            }}
            textType='medium'
          >
            {t('Signup_town')}
          </CustomText>
          <TextInput
            style={CommonStyles.inputField}
            onChangeText={(text: string) => setCity(text)}
            placeholder={t('Signup_town')}
            value={city}
          />
        </View>

        {/* State Input */}
        <View style={{ flexDirection: 'column' }}>
          <CustomText
            style={{
              color: '#110F05',
              fontSize: rv(12),
              fontFamily: 'medium'
            }}
            textType='medium'
          >
            {t('Signup_State')}
          </CustomText>
          <TextInput
            style={CommonStyles.inputField}
            onChangeText={(text: string) => setState(text)}
            placeholder={t('Signup_State')}
            value={state}
          />
        </View>
      </View>

      {/* Country Picker */}
      <View style={{ flexDirection: 'column', marginTop: 12 }}>
        <CustomText
          style={{
            color: '#110F05',
            fontSize: rv(12),
            fontFamily: 'medium'
          }}
        >
          Country of Residence
        </CustomText>
        <View>
  {/* Trigger for opening the Country Picker */}
          <TouchableOpacity
            style={styles.dropdownTrigger}
            onPress={() => setCountryPickerVisible(true)}
          >
          <CountryPicker
            withFilter
            withFlag
            withCountryNameButton
            onSelect={handleCountryChange}
            countryCode={selectedCountry ? selectedCountry.cca2 : 'NG'}
            containerButtonStyle={styles.countryPickerContainer}
            visible={isCountryPickerVisible}
            countryNameStyle={styles.countryNameText}
            onClose={() => setCountryPickerVisible(false)}
          />

          <Ionicons
            name={isCountryPickerVisible ? 'chevron-up' : 'chevron-down'}
            size={rv(14)}
            style={styles.arrow}
          />
          </TouchableOpacity>
        </View>
      </View>

      {/* Next Button */}
      <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center' }}>
        <View style={{ width: wp('30%') }}>
          <CustomButton
            label={t('Signup_nextbtn')}
            onPress={() => updateProfile()}
            style={{
              width: '100%',
              maxWidth: rv(118),
              borderRadius: rv(12)
            }}
            loading={loading}
          />
        </View>
      </View>
    </SafeAreaView>
  </ScrollView>
</KeyboardAvoidingView>

  );
};

export default Welcome;

const styles: any = StyleSheet.create({
  dropdownTrigger: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: rv(12),
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: rv(8),
    marginRight: 10,
    marginBottom: rv(25)
  },
  countryNameText: {
    fontFamily: 'medium', // matching Personal Info page
    fontSize: rv(12),
    color: '#110F05',
    textAlign: 'left',
    flex: 1,
  },
  arrow: {
    fontSize: rv(14),
    color: '#696969',
    marginLeft: 5,
  },
  countryPickerContainer: {
    flex: 1,
    // Align text to left inside the picker button:
    justifyContent: 'center',
    alignItems: 'flex-start',
  },

  scrollview: {
    display: 'flex',
    paddingTop: rv(20),
    backgroundColor: 'white',
    flexDirection: 'column',
    paddingHorizontal: wp('5'),
    flexGrow: 1, // Allow content to grow and scroll
  },
  scrollview2: {
    flexGrow: 1, // Ensure ScrollView content expands
  },
  
});

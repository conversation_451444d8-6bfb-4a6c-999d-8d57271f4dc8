import React, {
  Component,
  useState,
  useEffect,
  useRef,
  useContext,
} from "react";
// import { useIsFocused } from '@react-navigation/native';
import {
  TextInput,
  Text,
  View,
  Image,
  Button,
  StyleSheet,
  Alert,
  TouchableHighlight,
  ScrollView,
  StatusBar,
  Linking,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { default as themeFont } from "app/assets/themes/fonts.json";
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { CustomButton, CustomText } from "app/components/elements";
import SVG from "app/providers/svg";
import RNPickerSelect from "react-native-picker-select";
import { Flag } from "react-native-svg-flagkit";
import countryAndCode from "app/Data/countryAndCode";
import { PickerSelectStyle } from "app/assets/styles";
import { accountLogin } from "app/redux/user/hooks";
import useNotification from "app/helpers/notification";
import { useDispatch, useSelector } from "react-redux";
import { appLoading, setLoading } from "app/redux/main/reducer";
import CustomCountryPicker from "app/components/CountryPicker";
import { Country as CountryType } from "../../types/types";
import CountryPicker, { Country } from "react-native-country-picker-modal";
import { useTranslation } from "react-i18next";
import * as Notifications from "expo-notifications";
import CustomModal from "app/components/elements/Modals";
// import { GhanaloginlogoSVG } from "app/providers/svg/loader";
import { responsiveValue as rv } from "app/providers/responsive-value";
import Logo_onboard from '@/app/assets/svg/connectifyBigLogo.svg'
// import Logo_onboard_sm from '@/app/assets/svg/AgriFood Logo small.svg'
import Arrow from '@/app/assets/svg/arrowEast.svg'


type SignInScreenProps = {
  navigation: any;
};

interface CustomCountryPickerProps {
  onCountrySelect: (countryCode: string) => void;
}

function Login({ navigation }: SignInScreenProps) {
  const loading = useSelector(appLoading);
  const dispatch = useDispatch();
  const [userPassword, setUserPassword] = useState("");
  const [phone, setPhone] = useState();
  const [country, setCountry] = useState("+234");
  const [selectedCountry, setSelectedCountry] = useState<CountryType | null>(
    null
  );
  const [countryFlag, setCountryFlag] = useState("NG");
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [isCountryPickerVisible, setCountryPickerVisible] = useState(false);

  type CountryProps = {
    label: string;
    value: string;
    name: string;
    flag: string;
  };

  function getCountryFlag(): string {
    let countryData = countryAndCode.find((item: CountryProps) => {
      return item.value == country;
    });
    if (countryData) {
      return countryData.flag;
    } else {
      return "";
    }
  }

  useEffect(() => {
    setCountryFlag(getCountryFlag());
    console.log(country);
  }, [country]);

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);
    setCountry(`+${country.callingCode[0]}`);
  };

  const handleSubmitPress = async () => {
    if (!phone) {
      setAlertMessage("Please fill phone number");
      setModalVisible(true);
      return;
    }
    if (!userPassword) {
      setAlertMessage("Please fill your PIN");
      setModalVisible(true);
      return;
    }

    await accountLogin({
      phone_number: {
        code: country,
        number: phone,
      },
      password: userPassword,
    });
  };
  const handleConfirm = async () => {
    setModalVisible(false);
  };
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {modalVisible && (
          <CustomModal
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            type="alert" // or "stake" based on the scenario
            message={alertMessage}
            handleConfirm={handleConfirm}
            navigation
          />
        )}
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Your existing components */}
        {/* <GhanaloginlogoSVG style={{alignSelf: 'center'}} /> */}
      
        <Logo_onboard  />
       
       {/* <Image
       
       source={require('app/assets/Logo_onboard.png')}
       style={{width: rv(200), alignSelf: 'center'}}
 
     /> */}
   
     <View style={{marginTop: rv(18), width: '85%'}}>

     <CustomText style={{fontFamily: 'semiBold', fontSize: rv(16), color: '#4F4F4F'}}>
    Login 
      </CustomText>
      <CustomText style={{fontFamily: 'medium', fontSize: rv(13),  color: '#696969', marginTop: rv(10), marginBottom: rv(25)}}>
      Your gateway to connecting with your closest network
      </CustomText>
     
     </View>

      <View style={{ width: ('85%') }}>
      <CustomText
            style={{
              color: '#110F05',
              fontSize: rv(12)
            }}
            textType='medium'
          >
           Phone number
          </CustomText>

      <View >
          <View
            style={{
              flex: 1,
              flexDirection: "row",
              marginTop: rv(2)
            }}
          >
            <View
              style={{
                ...styles.signup,
                padding: 5,
                alignItems: "center",
                justifyContent: "center",
                marginRight: 10,
                flexDirection: "row",
              }}
            >
              <TouchableOpacity
                onPress={() => {
                  setCountryPickerVisible(true);
                }}
                hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                style={styles.arrowContainer}
              >
                <Text style={styles.arrow}>▼</Text>
              </TouchableOpacity>
              <View style={styles.container2}>
              <TouchableOpacity
                onPress={() => setCountryPickerVisible(true)}
                hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
                style={styles.container2}
              >
                <Text style={styles.countryCodeText}>
                  +{selectedCountry ? selectedCountry.callingCode[0] : "234"}
                </Text>
                {selectedCountry && (
                  <Image
                    style={styles.flagImage}
                    source={{ uri: selectedCountry.flag }}
                  />
                )}
                </TouchableOpacity>
                <CountryPicker
                  withCallingCode
                  withFilter
                  withFlag
                  onSelect={handleCountryChange}
                  countryCode={selectedCountry ? selectedCountry.cca2 : "NG"}
                  containerButtonStyle={styles.countryPickerContainer}
                  visible={isCountryPickerVisible}
                  onClose={() => setCountryPickerVisible(false)}
                />
              </View>
            </View>
            <View
              style={{
                flex: 1,
                marginLeft: 1,
              }}
            >
              
              <TextInput
                style={styles.signup}
                maxLength={10}
                onChangeText={(text: any) => {
                  setPhone(text);
                }}
                inlineImageLeft="callplain"
                placeholder={t("LoginPage_PhoneNumber")}
                inlineImagePadding={20}
                keyboardType="phone-pad"
                value={phone}
              />
            </View>
          </View>
          <CustomText
            style={{
              color: '#110F05',
              fontSize: rv(12),
              marginBottom: rv(2)

            }}
            textType='medium'
          >
          Pin
          </CustomText>
          <TextInput
            style={styles.signup}
            onChangeText={(userPassword) => setUserPassword(userPassword)}
            inlineImageLeft="key"
            placeholder={t("LoginPage_pin")}
            inlineImagePadding={20}
            textContentType="password"
            secureTextEntry={true}
            keyboardType="numeric"
            maxLength={4}
            value={userPassword}
          />
        </View>
      </View>
        <View style={{ width: wp("85%") }}>
          <CustomButton
            label={t("LoginPage_loginBtn")}
            onPress={() => {
              handleSubmitPress();
            }}
            style={{
           
              textAlign: 'left'
              
            }}
            loading={loading}
           
          />
          
        </View>
     

        <View
          style={{
            width: "85%",
            marginTop: rv(30),
            marginBottom: rv(25),
            
          }}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: 'center',
              justifyContent: 'center'

              
            }}
          >
              <View >
              <TouchableOpacity
              onPress={() => {
                navigation.navigate("register");
              }}
            >
              <CustomText
                style={{ color: "#000", fontSize: rv(13), textAlign: "center", fontFamily: 'medium' }}
               
                
              >
                {t("LoginPage_signUp")}
              </CustomText>
            </TouchableOpacity>
        </View>
            <CustomText
              textType="bold"
              style={{
                paddingHorizontal: 20,
                color: '#163E23'
              }}
            >
              |
            </CustomText>
            <View>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate("forgot-password");
                }}
              >
                <CustomText
                  style={{ color: "black", fontSize: rv(13) }}
                  textType="medium"
                >
                 
                  {t("LoginPage_forgotPass")}
                </CustomText>
              </TouchableOpacity>
            </View>
          </View>
        </View>

      
      

        {/* ... */}

        {/* Your other components */}
        {/* ... */}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles: any = StyleSheet.create({
  container2: {
    flexDirection: "row",
    alignItems: "center",
  },
  countryCodeText: {
    marginLeft: rv(7),
    fontSize: rv(13),
    marginRight: rv(3)
  },
  flagImage: {
    width: rv(25),
    height: rv(15),
    marginLeft: -25,
  },
  arrowContainer: {
    marginLeft: 5,
  },
  arrow: {
    fontSize: 13,
    color: "black",
  },
  countryPickerContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: "center",
    alignItems: "center",
  },

  signup: {
    height: 50,
    paddingLeft: 20,
    backgroundColor: "transparent",
    marginBottom: 15,
    borderStyle: "solid",
    marginTop: 0,
    fontSize: rv(13),
    fontFamily: "regular",
    borderWidth: 1.5,
    borderColor: '#F5F5F5'
  },
  // ... other styles
});

export default Login;
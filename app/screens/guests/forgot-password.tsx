/* eslint-disable prettier/prettier */
import React, { useState } from 'react';
import { useIsFocused } from '@react-navigation/native';
import {
  TextInput,
  View,
  Image,
  StyleSheet,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  SafeAreaView
} from 'react-native';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CustomButton, CustomText } from 'app/components/elements';
import SVG from 'app/providers/svg';
import { useSendResetPasswordToken } from 'app/redux/user/hooks';
import { useTranslation } from 'react-i18next';
import CustomModal from 'app/components/elements/Modals';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Logo_onboard from '@/app/assets/svg/connectifyBigLogo.svg'
import Personal_info from '@/app/assets/svg/AgriFood Logo small.svg'


type SignInScreenProps = {
  navigation: any;
};

function ForgotPasswordScreen({ navigation }: SignInScreenProps) {
  const [email, setEmail] = useState('');

  const { mutate, isLoading } = useSendResetPasswordToken();
  const { t } = useTranslation();
  const [alertMessage, setAlertMessage] = useState('')
  const [modalVisible,setModalVisible] = useState(false)

  const handleSubmitPress = () => {
    if (!email) {
      setAlertMessage('Email is not allowed to empty');
      setModalVisible(true)
      return;
    }

    mutate({
      email,
    });
  };

  const handleConfirm = async () => {
    setModalVisible(false)
  };

  return (
    <ScrollView
      contentContainerStyle={{
        flexGrow: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        paddingHorizontal: wp('5')
      }}
    >
      {modalVisible && (
      <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type="alert" // or "stake" based on the scenario
          message={alertMessage}
          handleConfirm={handleConfirm}
          navigation
        />)}
      <SafeAreaView>
      <StatusBar barStyle='dark-content' backgroundColor='white' />

      <View>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >

          <View
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
                 

            <View>
             <Logo_onboard />
             
            </View>

            <View style={{ width:380}}>
            <View
                style={{
                  // alignItems: 'flex-start',
                  marginTop: 5,
                  marginBottom: 40,
                  paddingTop: rv(20)
                }}
              >
                <CustomText
                  style={{
                     fontSize: rv(13),  color: '#696969', 
                  }}
                  textType='semi-bold'
                >
                  Forgot Password?
                </CustomText>
                <CustomText
                  style={{
                     fontSize: rv(13), color: '#696969', 
                  }}
                  textType='semi-bold'
                >
                  Don`t worry we got you covered
                </CustomText>
              </View>
              <View
                style={{
                  flex: 1,
                  flexDirection: 'row',
                }}
              >
                <View
                  style={{
                    flex: 1,
                
                  }}
                >
                     <CustomText
                  style={{
                     fontSize: rv(13), color: '#797C7B', 
                  }}
                  textType='semi-bold'
                >
                  Email:
                </CustomText>
                  <TextInput
                    style={styles.signup}
                    onChangeText={(text: any) => {
                      setEmail(text);
                    }}
                    placeholder=''
                    value={email}
                  />
                </View>
              </View>
            </View>

            <View style={{ width: wp('90%'), alignItems: 'center', display: 'flex' }}>
              <CustomButton
                label={t('ForgotPin_btn')}
                onPress={() => {
                  handleSubmitPress();
                }}
                buttonTheme='primary'
                style={{
                
                }}
                loading={isLoading}
              />
            </View>
            <View style={{ width: wp('90%'), flexDirection: 'row', marginTop: rv(20),  alignItems: 'center', justifyContent: 'center' }}>
            <View >
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('register');
        }}
      >
        <CustomText
          style={{ color: '#000', fontSize: rv(13), textAlign: 'left', fontFamily: 'medium' }}
        >
          {t('LoginPage_signUp')}
        </CustomText>
      </TouchableOpacity>
    </View>
    <CustomText
      textType="bold"
      style={{
        paddingHorizontal: 20,
        color: '#163E23',
      }}
    >
      |
    </CustomText>
    <View>
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('login');
        }}
      >
        <CustomText
          style={{ color: 'black', fontSize: rv(13), textAlign: 'left' }}
          textType="medium"
        >
          Login
        </CustomText>
      </TouchableOpacity>
    </View>
            </View>


</View>

        
        </View>
      </View>
      </SafeAreaView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  signup: {
    height: 60,
    width: '100%',
   
    paddingLeft: 20,
    backgroundColor: 'transparent',
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
    fontSize: rv(12),
    fontFamily: 'regular',
       borderWidth: 1.5,
    borderColor: '#F5F5F5'

    // fontFamily: 'regular',
  },
});

export default ForgotPasswordScreen;

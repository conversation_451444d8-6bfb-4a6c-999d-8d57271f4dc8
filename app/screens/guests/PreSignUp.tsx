import React, { useEffect } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text, View, Image, TouchableOpacity, StyleSheet, ImageBackground } from "react-native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import { navigate } from "app/navigation/root";
import { useNavigation } from "@react-navigation/native";
import Logo_onboard from '@/app/assets/svg/connectifyBigLogo.svg'
import Pre_login from '@/app/assets/svg/PreLogin.svg'
import { t } from "i18next";
import { CustomText } from "app/components/elements";
import AsyncStorage from "@react-native-async-storage/async-storage";

const PreSignUp = () => {
  const navigation = useNavigation();

  const handleNavigateToRegister = async () => {
    try {
      await AsyncStorage.removeItem('formData'); // Clear storage
      navigation.navigate("register" as never); // Navigate to the register screen
    } catch (error) {
      console.error('Error clearing form data', error);
    }
  };
  
  
 
    const handleNavigateToLogin = () => {
    navigation.navigate("login" as never);
  };
  return (
<View style={styles.container}>
    <SafeAreaView style={styles.scrollview}>
    <View style={{ alignSelf: 'center', marginTop: rv(46) }}>
          <Logo_onboard />
        </View>
     <View style={{marginTop: rv(40),}}>
     {/* <CustomText style={{fontFamily: 'hammersmithRegular', fontSize: rv(20), textAlign: 'center', color: '#2E2E2E'}}>
      AKWAABA
      </CustomText> */}
      <CustomText style={{fontFamily:  'semiBold', fontSize: rv(13), textAlign: 'center', color: '#696969', marginTop: rv(16)}}>
      Join the Connectify Network. Connect, collaborate, and grow together
            </CustomText>
     </View>
     {/* <View style={{alignSelf: 'center'}}>
   <Pre_login />
     </View> */}
      <View style={{ justifyContent: 'center', marginTop: rv(30),}}>
  
        <View style={{  alignItems: 'center' }}>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleNavigateToLogin}
          >
            <Text style={styles.loginInnerText}>{t("login")}</Text>
          </TouchableOpacity>
         
        </View>
        <View style={{ marginTop: rv(16), alignItems: 'center' }}>
        <TouchableOpacity
            style={styles.signupButton}
            onPress={handleNavigateToRegister}
          >
            <Text style={styles.signupText}>{t("signUp")}</Text>
          </TouchableOpacity>
        </View>
        <View style={{height: rv(90)}}></View>
      </View>
    </SafeAreaView>

</View>
  );
};
const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
       alignContent: "center",
    justifyContent: "center",
  backgroundColor: '#FFFFFF'
    
   
  },

  scrollview: {
    flex: 1,
    paddingHorizontal: rv(16),
    backgroundColor: 'transparent',
    alignContent: "center",
    justifyContent: "center",
    zIndex: 9999,
    position: 'absolute',
    width: '100%'

  },
  signupText: {
    fontSize: rv(14),
    color: "#48463E",
   fontFamily: 'medium',
  },
  // buttonInnerText: {
  //   fontSize: rv(12),
  //   fontFamily: "regular",
  //   color: "#48463E",
   
  // },
  loginInnerText: {
 color: '#FFFFFF',
fontFamily: 'medium',
 fontSize: rv(14)
  },
  signupButton: {
    maxWidth: rv(364),
    width: '100%',
    backgroundColor: 'transparent',

    height: rv(50),
   
    
    justifyContent: "center",
    alignItems: "center",
  
    borderWidth: 1,
    borderColor: '#163E23'
    
  },
  loginButton: {
    maxWidth: rv(364),
    width: '100%',
    
   
    // borderRadius: rv(12),
    height: rv(50),
    justifyContent: "center",
    alignItems: "center",
   
    backgroundColor: '#163E23',
   
  },
});

export default PreSignUp;
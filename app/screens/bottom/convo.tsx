import { useFocusEffect } from '@react-navigation/native';
import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  StatusBar,
  FlatList,
  TouchableOpacity,
  
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TopicCard } from '../../components/cards/topics';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import ConvoNotificationIcon from 'app/components/convo-icon';
import { ITopic } from 'app/redux/topic/types';
import { useConvos } from 'app/redux/topic/hooks';
import { useGetUnreadNotifications } from 'app/redux/main/hooks';
import { main } from 'app/api/main';
import Ionicons from '@expo/vector-icons/Ionicons';
import { CustomText } from 'app/components/elements';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';


const Loading = require('../../assets/loading.gif');

interface IConvoScreen {
  navigation: any;
  route: any;
}

interface IConvos {
  _id: string;
  topicId: ITopic;
}

const ConvoScreen: React.FC<IConvoScreen> = ({ navigation, route }) => {

  const {t} = useTranslation()
  const { data, isFetching, refetch, isLoading } = useConvos();

  const { data: unread } = useGetUnreadNotifications(false);

  const { count = 0 } = route.params || {}; 

  useEffect(() => {
    if (unread && unread.convos.length > 0) {
      main.clearUnReadNotifications({ source: 'convos' }).then((response) => {
        console.log(response);
      });
    }
  }, [unread, data]);

  return (
    <SafeAreaView style={{ flexDirection: 'column', backgroundColor: 'white', flex: 1 }}>
      <StatusBar barStyle='dark-content' backgroundColor='white' />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginVertical: 20,
        }}
      >
        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}
          style={{
            marginLeft: 15,
            padding: 10
          }}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }} // Expands the touchable area
        >
          <Ionicons
            name='chevron-back'
            size={22}
            color='black'
            style={{ marginLeft: 5 }}
          />
        </TouchableOpacity>
            <CustomText
        style={{
          fontSize: rv(13),
          marginRight: 3,
          paddingTop: 3,
        }}
      >
        {t("Convos")}
      </CustomText>
        {/* <ConvoNotificationIcon count={count} /> */}
      </View>
      {isLoading && (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image
            source={Loading}
            style={{
              width: rv(50),
              height: rv(50),
            }}
          />
        </View>
      )}
      {!isLoading && (
        <FlatList
          data={data}
          keyExtractor={(item: IConvos) => item._id}
          refreshing={isFetching}
          onRefresh={() => refetch()}
          renderItem={({ item }) => (
            <TopicCard
              item={item.topicId}
              navigation={navigation}
              convo={true}
              refreshFunction={() => refetch()}
            />
          )}
          contentContainerStyle={{ paddingBottom: 20 }} // Adding padding to the bottom
        />
      )}
    </SafeAreaView>
  );
};

export default ConvoScreen;

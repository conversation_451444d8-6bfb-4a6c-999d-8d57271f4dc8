import { useTopics } from 'app/redux/topic/hooks';
import React from 'react';

import {
  View,
  StatusBar,
  FlatList, 
  Text, TouchableOpacity, StyleSheet, Image
} from 'react-native';

import { TopicCard } from 'app/components/cards';
import FloatingAddSVG from 'app/assets/svg/floating-add.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
const Loading = require('../../assets/loading.gif');

function Topic({ navigation, topicId }: any) {
  const { status, data, error, isFetching, refetch, isLoading, } = useTopics({topicId});
  console.log('topicid', topicId)
  return (
    <View style={{ flexDirection: 'column', flex: 1 }}>
      <StatusBar barStyle='dark-content' backgroundColor='white' />
      {/* <View>
        <Text>
          status: {status} { '\n'}
          isFetching: {isFetching.toString()} { '\n'}  
          error: {error}
        </Text>
       </View> */}
       {isLoading ? (
      <View
      style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Image
        source={Loading}
        style={{
          width: rv(50),
          height: rv(50),
        }}
      />
    </View>
       ) : (
 
        <FlatList
          data={data}
          keyExtractor={(item: any, index) => item._id}
          refreshing={isFetching}
          onRefresh={() => refetch()}
          renderItem={({ item }) => {
            return <TopicCard item={item} navigation={navigation} convo={false} refreshFunction={() => refetch()} onToggleMuted={function (): void {
              throw new Error('Function not implemented.');
            } } muted={false} />;
          }}
        />
       )}
    
   

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => navigation.navigate('PostTopic')}
        style={styles.touchableOpacityStyle}
      >
        <FloatingAddSVG
          width={rv(45)}
          height={rv(45)}
        />


      </TouchableOpacity>
    </View>

  );
}


const styles = StyleSheet.create({
  touchableOpacityStyle: {
    position: 'absolute',
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    right: 30,
    bottom: 30,
  },
})
export default Topic;

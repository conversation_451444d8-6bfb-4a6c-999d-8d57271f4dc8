import React, { useCallback, useState } from 'react';
import { View, FlatList, Text, ListRenderItemInfo, Image } from 'react-native';
import Group from 'app/components/cards/group';
import { useSuggestedGroups } from 'app/redux/group/hooks';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import  BlankJoinGroupSVG  from 'app/assets/svg/empty-pages.svg';
import { useFocusEffect } from '@react-navigation/native';
import RecommendedTextSVG from '@/app/assets/svg/Recommendedgroups_text.svg'

export type RecommendedGroupsScreenProps = {
  navigation?: any;
};

const Loading = require('../../../assets/loading.gif');

const RecommendedGroupsScreen: React.FC<RecommendedGroupsScreenProps> = ({
  navigation,
}) => {
  const { status, data, error, isFetching, refetch, isLoading } =
    useSuggestedGroups();
  const [initialRefetch, setInitialRefetch] = useState(true)
  useFocusEffect(
    useCallback(() => {
      let isActive = true;
      const fetchData = async () => {
        try {
          if (isActive && initialRefetch) {
            refetch();
            setInitialRefetch(false);
          }
        } catch (e) {
          // Handle error
        }
      };

      fetchData();
      return () => {
        isActive = false;
      };
    }, [initialRefetch, refetch])
  );

  const handleRefresh = () => {
    setInitialRefetch(true);
    refetch();
  };

  return (
    <View
      style={{
        flex: 1,
      }}
    >
      {(isLoading) ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image
            source={Loading}
            style={{
              width: rv(50),
              height: rv(50),
            }}
          />
        </View>
      ) : (data && data.length > 0 ? (
        <FlatList

          refreshing={isFetching}
          onRefresh={handleRefresh}
          style={{
            // marginTop: 6,
            paddingHorizontal: 20,
          }}
          data={data}
          keyExtractor={(item: GroupProps) => item._id}
          renderItem={({ item }: ListRenderItemInfo<GroupProps>) => (
            <Group item={item} navigation={navigation} recommended={true} />
          )}
        />
      ) : (
        <View
          style={{
            alignContent: 'center',
            marginTop: '30%'
          }}
        >
          <BlankJoinGroupSVG style={{alignSelf: 'center'}}/>
       
          <RecommendedTextSVG style={{ alignSelf: 'center', marginTop: rv(20) }} />
        </View>
      ))}

    </View>
  );
};

export default RecommendedGroupsScreen;

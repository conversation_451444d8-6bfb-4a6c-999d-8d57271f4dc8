import { useIsFocused, useFocusEffect } from '@react-navigation/native';

import React, {
  Component,
  useState,
  useCallback,
  useEffect,
  useContext,
  useRef,
} from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ListRenderItemInfo,
} from 'react-native';
import { useMyGroups } from 'app/redux/group/hooks';
import { Axios } from 'app/api/axios';
// import { showMessage } from 'app/reuseables/toast-message';
import Group from 'app/components/cards/group';
import { useRefetchOnFocus } from 'app/custom-hooks/usefetchonfocus';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import  BlankMyGroupSVG  from 'app/assets/svg/empty-pages.svg';
import ChatTextSVG from '@/app/assets/svg/Mygroups_text.svg'
const Loading = require('../../../assets/loading.gif');

function MyGroups({ navigation }: any) {
  const { data, error, isFetching, refetch, isLoading } = useMyGroups();
  // useRefetchOnFocus(refetch); //refresh on focus

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: 'white',
      }}
    >
      <View
        style={{
          alignItems: 'center',
          marginVertical: 4,
        }}
      ></View>
      {isLoading ? (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image
            source={Loading}
            style={{
              width: rv(50),
              height: rv(50),
            }}
          />
        </View>
      ) : (
        <>
          {data && data.length > 0 ? (
            <FlatList
              data={data}
              refreshing={isFetching}

              style={{
                paddingHorizontal: 20,
              }}
              keyExtractor={(item: GroupProps) => item._id}
              renderItem={({ item }: ListRenderItemInfo<GroupProps>) => (
                <Group
                  item={item}
                  navigation={navigation}
                  refreshFunction={() => refetch()}
                />
              )}
            />
          ) : (
            <View
              style={{
                alignContent: 'center',
                marginTop: rv(20)
              }}
            >
              <BlankMyGroupSVG                 style={{ alignSelf: 'center', }}
 />

    
              <ChatTextSVG style={{ alignSelf: 'center', marginTop: rv(20),  }} />
            </View>
          )}
        </>
      )}
    </View>
  );
}

export default MyGroups;

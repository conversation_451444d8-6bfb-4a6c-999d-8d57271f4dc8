import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ScrollView,
  Modal,
  Button,
} from "react-native";
import { useTranslation } from "react-i18next";
import { CustomText } from "app/components/elements";
import { SafeAreaView } from "react-native-safe-area-context";
import { useGetCredit } from "app/redux/credit/hook";
import { processFontFamily } from "expo-font";
import useFonts from "app/hooks/useFont.js"; // Corrected import name
import { useNavigation } from "@react-navigation/native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import CustomModal from "app/components/elements/Modals";
import Ionicons from "@expo/vector-icons/Ionicons";
import UltimateChallenge from "./ultimateChallenge";
import {
  BulbSVG,
  HelpdeskSVG,
  NextArrowSVG,
  GameModeSVG,
  BuyCreditSVG,
} from "app/providers/svg/loader";
import { SafeAreaProvider } from "react-native-safe-area-context";

interface ChallengeModeProps {
  onChangeText: (text: string) => void;
  navigation: any; // Adjust the type according to your navigation prop type
  // Define the type of the 'onChangeText' prop
}
const ChallengeMode: React.FC<ChallengeModeProps> = ({ route }: any) => {
  const { data: credits } = useGetCredit();
  const navigation = useNavigation(); // Get navigation object
  const [modalVisible, setModalVisible] = useState(false);

  const [stake, setStake] = React.useState(500);
  const handleIncrement = () => {
    if (stake < 5000) {
      // Check if stake is less than the maximum value
      setStake(stake + 500);
    }
  };

  const handleDecrement = () => {
    if (stake > 500) {
      // Check if stake is greater than the minimum value
      setStake(stake - 500);
    }
  };

  const handleNavigateToUltimateChallenge = () => {
    navigation.navigate("UltimateChallenge" as never);
  };
  const { t } = useTranslation();

  const handleNavigateToGameMode = () => {
    // You can do some validation here before navigating
    if (credits?.amount < stake) {
      // If not enough credits, show the modal and do not navigate
      setModalVisible(true);
    } else {
      navigation.navigate("GameMode", { text: stake }); // Passing the 'text' parameter
    }
  };

  const handleNavigateToChallengeMode = () => {
    // You can do some validation here before navigating
    navigation.navigate("QuizScreen" as never); // Passing the 'text' parameter
  };

  const handleNavigateToCreditScreen = () => {
    // You can do some validation here before navigating
    setModalVisible(false);
    navigation.navigate("Credit" as never); // Passing the 'text' parameter
  };

  const handleEndChallenge = () => {
    navigation.navigate("QuizScreen" as never);
  };
  return (
    <SafeAreaView
      style={{
        flex: 1,
        paddingHorizontal: rv(6),
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "white",
      }}
    >
      {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          type="credit"
          handleConfirm={handleEndChallenge}
          NavigateToCreditPage={handleNavigateToCreditScreen}
          navigation={navigation}
        />
      )}
      <ScrollView contentContainerStyle={{ flexGrow: 1, alignItems: "center" }}>
        <View
          style={{
            flexDirection: "column",
            justifyContent: "space-between",
            width: "90%",
            marginTop: rv(10),
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <TouchableOpacity
              style={{ flexDirection: "row", alignItems: "center" }}
              onPress={handleNavigateToChallengeMode}
            >
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Ionicons name="chevron-back" size={22} color="black" />
                <CustomText
                  style={{
                    fontSize: rv(13),
                    fontFamily: "semiBold",
                    fontWeight: "100",
                    paddingLeft: rv(5.5),
                    color: "#4F4F4F",
                  }}
                >
                  {t("back_to_game_mode")}
                </CustomText>
              </View>
            </TouchableOpacity>

            <View style={{ flexDirection: "row" }}>
              <Text
                style={{
                  color: "#4F4F4F",
                  fontSize: rv(13),
                  fontFamily: "semiBold",
                  fontWeight: "100",
                  paddingLeft: rv(5.5),
                }}
              >
                {credits?.amount}
              </Text>
              <Text
                style={{
                  color: "#4F4F4F",
                  fontSize: rv(13),
                  fontFamily: "semiBold",
                  fontWeight: "100",
                  paddingLeft: rv(5.5),
                }}
              >
                {t("pennytots")}
              </Text>
            </View>
          </View>

          <View style={{ alignItems: "center" }}>
            <CustomText
              style={{
                marginTop: rv(22),
                fontFamily: "semiBold",
                fontSize: rv(20),
                color: "#797978",
              }}
            >
              {t("double_up")}
            </CustomText>
            <Image
              style={{ marginTop: rv(5), width: rv(160), height: rv(160) }}
              source={require(`app/assets/Pennytots_gold.png`)}
            />
          </View>
        </View>
        <CustomText
          style={{
            fontSize: rv(12),
            // fontWeight: 'bold',
            fontFamily: "medium",
            width: "85%",
            marginTop: rv(5),
            alignItems: "center",
            textAlign: "center",
            color: "#696969",
          }}
        >
          {t("build_up_your_credit_here_for_")}
        </CustomText>
        <CustomText
          style={{
            fontFamily: "medium",
            fontSize: rv(12),
            alignItems: "center",
            textAlign: "center",
            marginTop: rv(12),
            color: "#FFB61D",
          }}
        >
          {t("24karat_1_ounce_custommade_pur")}
        </CustomText>
        <CustomText
          style={{
            fontSize: rv(12),
            marginTop: rv(12),
            // fontWeight: 'bold',
            fontFamily: "medium",
            width: "85%",
            alignItems: "center",
            textAlign: "center",
            color: "#696969",
          }}
        >
         {t("answer_5_questions_correctly_b")}
        </CustomText>
        <CustomText
          style={{
            fontSize: rv(12),
            fontFamily: "medium",
            marginTop: rv(27),
            textAlign: "center",
            color: "#696969",
          }}
        >
          {t("you_need_to_have_at_least_500_")}
        </CustomText>
        <View style={styles.container}>
          <Text style={styles.label}>Stake</Text>
          <View style={styles.inputContainer}>
            <TouchableOpacity
              onPress={handleDecrement}
              disabled={stake <= 500}
              style={[styles.button2, stake <= 500 && styles.disabledButton]}
            >
              <Text>-</Text>
            </TouchableOpacity>
            <TextInput
              style={styles.input}
              onChangeText={(text) => setStake(Number(text))}
              value={String(stake)}
              editable={false}
              placeholder=""
              keyboardType="numeric"
            />
            <TouchableOpacity
              onPress={handleIncrement}
              disabled={stake >= 5000}
              style={[styles.button3, stake >= 5000 && styles.disabledButton]}
            >
              <Text>+</Text>
            </TouchableOpacity>
          </View>
        </View>
        <TouchableOpacity
          style={styles.button}
          onPress={handleNavigateToGameMode}
        >
          <CustomText style={styles.buttonText}>{t("play")}</CustomText>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: rv(26),
  },
  label: {
    textAlign: "center",
    fontSize: rv(14),
    color: "#797978",
    fontFamily: "semiBold",
  },
  inputContainer: {
    marginTop: rv(8),
    height: rv(30),
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  disabledButton: {
    opacity: 0.5,
    // Add other styling for your disabled buttons
  },
  button: {
    backgroundColor: "#FED830",
    width: "85%",
    height: rv(48),
    paddingVertical: 8,
    paddingHorizontal: rv(12),
    justifyContent: "center", // Center the content vertically
    alignItems: "center",
    borderRadius: rv(32),
    marginTop: rv(41),
  },
  button2: {
    backgroundColor: "#FFC085",
    borderWidth: 1,
    borderColor: "transparent",
    borderRadius: rv(8),
    display: "flex",
    height: "100%",
    width: rv(30),
    fontSize: rv(16),
    marginRight: rv(8),
    alignItems: "center",
    justifyContent: "center",
  },
  button3: {
    backgroundColor: "#FFC085",
    borderWidth: 1,
    borderColor: "transparent",
    borderRadius: rv(8),
    display: "flex",
    height: "100%",
    width: rv(30),
    fontSize: rv(16),
    alignItems: "center",
    justifyContent: "center",
    marginLeft: rv(8),
  },

  input: {
    textAlign: "center",
    paddingVertical: rv(5),
    width: rv(115),
    borderRadius: 1.5,
    backgroundColor: "rgba(254, 216, 48, 0.13)",
    fontFamily: "medium",
    fontSize: rv(14),
    color: "#797978",
    fontWeight: "500", // Use Poppins font family for TextInput
  },
  buttonText: {
    color: "#48463E",
    fontSize: rv(16),
    fontWeight: "bold",
    lineHeight: rv(24),
    fontFamily: "semiBold",
    marginBottom: 0,
  },
  containers: {
    justifyContent: "center",
    alignItems: "center",
  },

  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: rv(22),
  },
  modalView: {
    margin: rv(20),
    backgroundColor: "white",
    borderRadius: 20,
    padding: rv(35),
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: rv(2),
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalText: {
    marginBottom: rv(15),
    textAlign: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
  },
});

export default ChallengeMode;

import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  View,
  Text,
  Button,
  Animated,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { CustomText } from 'app/components/elements';
import {
  useGetCredit,
  useGetCreditTransactionHistory,
} from 'app/redux/credit/hook';
import { SafeAreaView } from 'react-native-safe-area-context';
import FloatingAddSVG from 'app/assets/svg/next.svg';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Animation from 'app/components/animation';
import { useGetQuestions, useChangeUserPennytots } from 'app/redux/quiz/hook';
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect } from '@react-navigation/native';
import CustomModal from 'app/components/elements/Modals';
import { logout } from 'app/redux/user/hooks';
import {
  BulbSVG,
  HelpdeskSVG,
  NextArrowSVG,
  GameModeSVG,
  BuyCreditSVG,
} from 'app/providers/svg/loader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';

const Loading = require('../../assets/loading.gif');
// const Wrong1 = require('app/assets/times-up.png');
const Wrong2 = require('app/assets/gifs/wrong2.gif');
const Wrong3 = require('../../assets/gifs/angry-furious.gif');
const Wrong4 = require('../../assets/gifs/bad-hair.gif');
const Wrong5 = require('../../assets/gifs/bernie-mac-nervous.gif');
const Wrong6 = require('../../assets/gifs/well-damn.gif');
const Wrong7 = require('../../assets/gifs/martin.gif');
const Wrong8 = require('../../assets/gifs/no-chris-rock.gif');
const Wrong9 = require('../../assets/gifs/no-machaizelli-kahey.gif');
const Wrong10 = require('../../assets/gifs/wow-amazing.gif');
const Wrong11 = require('../../assets/gifs/no-nope-nope-nope.gif');
const Wrong12 = require('../../assets/gifs/no-richard-lane.gif');
const Wrong13 = require('../../assets/gifs/nope-kenan-thompson.gif');
const Wrong14 = require('../../assets/gifs/nope-terry-jeffords.gif');
const Wrong15 = require('../../assets/gifs/oh-come-on-captain-ray-holt.gif');
const Wrong16 = require('../../assets/gifs/oh-no-jeremy.gif');
const Wrong17 = require('../../assets/gifs/orange-is-the-new-black-oitnb.gif');
const Right1 = require('../../assets/gifs/correct1.gif');
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';

const OptionButton = ({
  option,
  onPress,
  isCorrect,
  isSelected,
  correctAnswer,
  canClick, // New prop to control when to show the correct answer
  gameOver,
  style,
}: any) => {
  const colors = [
    {
      background: '#55804C',
      backgroundColor: '#FED830',
    },
    {
      background: '#FFFFFF',
      backgroundColor: '#000000',
    },
    // Add more color combinations here
  ];

  const [currentColor, setCurrentColor] = useState(colors[0]);
  let randomIndex = 0;
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (randomIndex === 0) {
        randomIndex = 1;
      } else {
        randomIndex = 0;
      }
      setCurrentColor(colors[randomIndex]);
    }, 500);

    return () => clearInterval(intervalId);
  }, [randomIndex]);

  return (
    <TouchableOpacity
      onPress={() => {
        // Call the onPress function provided in the props
        onPress();
      }}
      style={{
        backgroundColor:
          isCorrect && isSelected // Show correct answer color if the option is correct and either selected or showAnswer is true
            ? currentColor.background
            : isSelected
            ? '#FE3233'
            : isCorrect !== null && option === correctAnswer
            ? currentColor.background
            : '#FFFFFF',
        height: rv(50),
        borderRadius: rv(12),
        borderColor: isCorrect && isSelected ? currentColor.background : '#163E23',
        borderWidth: isCorrect && isSelected  ? 0
        : isSelected ? 0 : 1,

        paddingHorizontal: rv(30),
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'regular',
        margin: rv(3),
        marginRight: rv(4),
        opacity: gameOver
          ? 0.5
          : isCorrect === null
          ? 1
          : isSelected
          ? 1
          : isCorrect !== null && option === correctAnswer
          ? 1
          : 0.5,
        ...style,
      }}
      disabled={isCorrect !== null || gameOver}
    >
      <Text
        style={{
          color: isCorrect && isSelected 
          ? 'white' 
          : isSelected 
          ? 'white' 
          : isCorrect !== null && option === correctAnswer 
          ? currentColor.background 
            ? 'white' 
            : 'black' 
          : 'black',
          textAlign: 'center',
          fontSize: rv(11),
          fontFamily: 'semiBold',
        }}
      >
        {option}
      </Text>
    </TouchableOpacity>
  );
};

const Quiz = ({ route }: any) => {
  const [optionSelected, setOptionSelected] = useState(false);
  const { t } = useTranslation();
  const {
    data: quizData,
    isLoading,
    refetch: getNextQuestion,
    isFetching,
  } = useGetQuestions();
  const [isNavigatedQuestion, setIsNavigatedQuestion] = useState(false);
  const [navigatedQuestionData, setNavigatedQuestionData] = useState(null);
  const [data1, setData1] = useState<any>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const { mutate: changeUserPennytots } = useChangeUserPennytots();
  const [selectedOption, setSelectedOption] = useState(null);
  const [goToNext, setGoToNext] = useState(false);
  const [score, setScore] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [isCorrect, setIsCorrect] = useState<any>(null);
  const { data: credits, refetch } = useGetCredit();
  const [isCurrentSelectionCorrect, setIsCurrentSelectionCorrect] =
    useState<any>(null);
  const [showScoreMessage, setShowScoreMessage] = useState(false);
  const [image, setImage] = useState(null);
  const displayQuestionData = isNavigatedQuestion
    ? navigatedQuestionData
    : quizData;
  const {
    question,
    options,
    answer: correctAnswer,
  } = displayQuestionData || {};
  const [end, setEnd] = useState(false);
  const [canClick, setCanClick] = useState(true);
  const navigation = useNavigation();
  const [canPlay, setCanPlay] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [type, setType] = useState<any>('');
  const [clickCount, setClickCount] = useState(0);
  
  const clickThreshold = 15;

  useEffect(() => {
    // Retrieve click count from AsyncStorage when the component mounts
    const getClickCount = async () => {
      try {
        const storedClickCount = await AsyncStorage.getItem('clickCount');
        if (storedClickCount !== null) {
          setClickCount(parseInt(storedClickCount));
        }
      } catch (error) {
        console.error('Failed to load click count:', error);
      }
    };

    getClickCount();
  }, []);

  const handleButtonClick = async () => {
    try {
      const newClickCount = clickCount + 1;
      setClickCount(newClickCount);
      await AsyncStorage.setItem('clickCount', newClickCount.toString());
    } catch (error) {
      console.error('Failed to save click count:', error);
    }
  };

  const resetClickCount = async () => {
    try {
      setClickCount(0);
      await AsyncStorage.setItem('clickCount', '0');
    } catch (error) {
      console.error('Failed to reset click count:', error);
    }
  };

  useEffect(() => {
    // Show modal if click count reaches the threshold
    if (clickCount >= clickThreshold) {
      setType('support-us');
      setModalVisible(true);
      resetClickCount();
    }
  }, [clickCount]);
  // Handle incoming question data from navigation
  // useFocusEffect(
  //   React.useCallback(() => {

  //   if (route.params?.questionData) {
  //     setNavigatedQuestionData(route.params.questionData);
  //     setIsNavigatedQuestion(true);
  //   }return () => {
  //     // Optional: Any cleanup code goes here
  //   };
  // }, [route.params?.questionData]));

  // function to get random image
  const getRandomWrongImage = () => {
    const images = [
      Wrong2,
      Wrong3,
      Wrong4,
      Wrong5,
      Wrong6,
      Wrong7,
      Wrong8,
      Wrong9,
      Wrong10,
      Wrong11,
      Wrong12,
      Wrong13,
      Wrong14,
      Wrong15,
      Wrong16,
      Wrong17,
    ];
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchLastNotification = async () => {
        try {
          console.log('Fetching last notification response');
          const response = await Notifications.getLastNotificationResponseAsync();
          console.log('Last notification response:', response);

          if (response) {
            const notificationId = response.notification.request.identifier;
            const hasBeenProcessed = await AsyncStorage.getItem('lastNotificationId');

            if (notificationId !== hasBeenProcessed) {
              const data = response.notification.request.content.data;
              console.log('Notification data:', data);

              if (data.type === 'quiz' && data.questionData) {
                console.log('Setting question data from notification:', data.questionData);
                setNavigatedQuestionData(data.questionData);
                setIsNavigatedQuestion(true);
                await AsyncStorage.setItem('lastNotificationId', notificationId); // Mark notification as processed
                return;
              }
            } else {
              console.log('Notification has already been processed');
            }
          } else {
            console.log('No last notification response found');
          }
        } catch (error) {
          console.error('Error handling last notification:', error);
        }

        // If no notification data, proceed with normal flow
        console.log('No question data, fetching new question');
        setIsNavigatedQuestion(false)
        getNextQuestion();
        setOptionSelected(false);
        setIsCurrentSelectionCorrect(null);
        setSelectedOption(null);
        setGoToNext(false);
        setScore(0);
        setIsCorrect(null);
        setShowScoreMessage(false);
        setImage(null);
        setEnd(false);
        setGameOver(false);
        setModalVisible(false);
        refetch();
        
      };

      fetchLastNotification();
     

      

      return () => {
        console.log('useFocusEffect cleanup');
      };
    }, [route.params?.questionData])
  );

  const getRandomCorrectImage = () => {
    const images = [Right1];
    const randomIndex = Math.floor(Math.random() * images.length);
    return images[randomIndex];
  };

  // useEffect(() => {
  //   refetch() // Fetch the first question when the component mounts
  // }, [selectedOption,credits]);

  const handleOptionSelect = async (option: any) => {
    setOptionSelected(true); // Indicate that an option has been selected
    handleButtonClick();

    const isCorrect = option === correctAnswer;
    if (!canPlay) {
      setModalVisible(true);
      return;
    }
    setSelectedOption(option);
    try {
      const isAnswerCorrect = option === correctAnswer;
      setIsCorrect(isAnswerCorrect);

      setEnd(true);
      setShowScoreMessage(true);
      setTimeout(() => {
        setShowScoreMessage(false);
      }, 2500);
      setGoToNext(true);
      if (isAnswerCorrect) {
        setIsCurrentSelectionCorrect(true);
        setImage(getRandomCorrectImage());
        await changeUserPennytots('increase');
        refetch();
      } else {
        setIsCurrentSelectionCorrect(false);
        setImage(getRandomWrongImage());
        setCanClick(false);
        setTimeout(() => {
          setIsCorrect(null); // Hide the score message after 5 seconds
        }, 2500);
        await changeUserPennytots('reduce');
        refetch();
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      setIsCurrentSelectionCorrect(true);
    }
  };
  const handleImageError = (error: any) => {
    console.error('Error loading image:', error);
    // Handle the error, such as displaying a placeholder image or showing an error message
  };
  const handleNextQuestion = () => {
    if (isNavigatedQuestion) {
      // Reset to fetch from backend after answering a navigated question
      setIsNavigatedQuestion(false);
      setNavigatedQuestionData(null);
    }
    setGoToNext(false);
    getNextQuestion(); // Refetch the mock data, replace it with your API call
    setEnd(false);
    setImage(null);
    setSelectedOption(null);
    setIsCorrect(null);
    setIsCurrentSelectionCorrect(null);
    setCanClick(true);
  };

  // const handleNavigateToChallengeMode = () => {
  //   navigation.navigate('ChallengeMode' as never);
  // };

  const NavigateToCreditPage = () => {
    setModalVisible(!modalVisible);
    navigation.navigate('Credit' as never);
  };

  const NavigateToCreditPagePurchase = () => {
    setModalVisible(!modalVisible);
    navigation.navigate('Credit', { message: 'paramValue' });
  };
  //  useEffect(()=>{
  //      if(credits?.amount >= "10000"){

  //       navigation.navigate('Credit', { replace: true } as never);
  //     }

  //  }
  //       navigation.navigate('Credit' as never);
  //     }

  //  })

  useEffect(() => {
    if (credits?.amount <= 1) {
      setCanPlay(false);
    } else {
      setCanPlay(true);
    }
  }, [credits]);

  return (
    <View
      style={{ flex: 1, backgroundColor: 'white', paddingHorizontal: rv(20) }}
    >
      {modalVisible && (
        <CustomModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          NavigateToCreditPage={NavigateToCreditPage}
          type={
            credits.amount <= 99
              ? 'credit'
              : type === 'support-us'
              ? type
              : 'new-credit'
          } // or "stake" based on the scenario
          handleConfirm={
            type === 'support-us'
              ? NavigateToCreditPagePurchase
              : NavigateToCreditPage
          }
          navigation={navigation}
        />
      )}
      {/* <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: rv(30),
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <BulbSVG width={rv(20)} height={rv(20)} />
            <Text
              style={{
                fontSize: rv(13),
                fontWeight: '600',
                color: '#4F4F4F',
                paddingLeft: rv(4),
              }}
            >
             {t("game_mode")}
            </Text>
        </View>

        <View style={{ flexDirection: 'column' }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Text
              style={{
                paddingRight: rv(3),
                fontSize: rv(13),
                fontWeight: '600',
                color: '#696969',
              }}
            >
              {credits?.amount}/10,000
            </Text>
            <Text
              style={{ fontSize: rv(13), fontWeight: '600', color: '#696969' }}
            >
              {t("pennytots")}
            </Text>
          </View>
          <View
            style={{ flexDirection: 'row', position: 'absolute', top: rv(22) }}
          >
            <Animatable.View animation='fadeIn' style={{ width: rv(30) }}>
              {showScoreMessage && (
                <Text
                  style={{
                    color: isCorrect ? 'green' : 'red',
                    fontSize: rv(12),
                  }}
                >
                  {isCorrect ? '+100' : isCorrect === false ? '-100' : ''}
                </Text>
              )}
            </Animatable.View>
          </View>
        </View>
      </View> */}
      {isFetching && (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignContent: 'center',
          }}
        >
          {isFetching && (
            <Image source={Loading} style={{ width: rv(50), height: rv(50) }} />
          )}
          {/* Your content here */}
        </View>
      )}
      {!isFetching && (
        <View style={{ flex: 1,  }}>
          <Text
            style={{
              marginTop: rv(15),
              fontSize: rv(11.5),
              fontFamily: 'semiBold',
              color: '#696969',
              
            }}
          >
            {question}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'space-between',
              marginTop: rv(28),
             
            }}
          >
            {options?.map((option, index) => (
              <OptionButton
                key={index}
                option={option}
                onPress={() => handleOptionSelect(option)}
                isSelected={selectedOption === option}
                isCorrect={isCurrentSelectionCorrect}
                correctAnswer={correctAnswer}
                gameOver={gameOver}
                canClick={canClick}
                style={{ width: '100%', marginBottom: wp('3'),  fontFamily: 'regular', }} // Adjust the width as needed
              />
            ))}
          </View>
          <Animation image={image} end={end} />
        </View>
      )}
      {goToNext && (
        <TouchableOpacity
          style={{ position: 'absolute', bottom: rv(5), right: rv(20) }}
          onPress={handleNextQuestion}
        >
          <View
            style={{
              // padding: rv(10),
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#163E23',
              width: rv(100),
              height: rv(44),
              borderRadius: 12,
            }}
          >
            <NextArrowSVG
              width={rv(35)}
              height={rv(30)}
               style={{ marginTop: rv(5) }}
              color="white"
            />
            <Text style={{ fontFamily: 'semiBold', fontSize: rv(13), color: 'white' }}>
             {t("next")}
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

// const styles = StyleSheet.create({
//   container: {
//     flex: 1, // Use flex in the parent container to cover the entire screen
//   },
//   centeredView: {
//     position: 'absolute', // Position the modal view absolutely
//     top: 0, // Align the top edge with the container
//     left: 0, // Align the left edge with the container
//     right: 0, // Align the right edge with the container
//     bottom: 0, // Align the bottom edge with the container
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
//   },
//   modalView: {
//     margin: 20,
//     backgroundColor: 'white',
//     borderRadius: 20,
//     padding: 35,
//     alignItems: 'center',
//     elevation: 5,
//   },
//   modalText: {
//     marginBottom: 15,
//     textAlign: 'center',
//   },
//   buttonContainer: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//   },
// });

export default Quiz;

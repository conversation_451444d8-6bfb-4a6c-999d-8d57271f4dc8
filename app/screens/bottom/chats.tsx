import React, { useEffect } from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import ChatTextSVG from '@/app/assets/svg/Chats_text.svg';
import BlankChatSVG from '@/app/assets/svg/empty-pages.svg';
import useMyChat from 'app/redux/chat/hooks';
import { ChatCard } from 'app/components/cards/chats';

const Loading = require('../../assets/loading.gif');

function Chat({ navigation }:any) {
  const { data, isFetching, refetch, isLoading } = useMyChat();

  // Log data when updated (optional)
  useEffect(() => {
    console.log('Chat data:', data);
  }, [data]);

  // Refetch data every time the screen is focused
  useFocusEffect(
    React.useCallback(() => {
      refetch();
    }, [refetch])
  );

  const onRefresh = async () => {
    await refetch();
  };

  return (
    <View style={{ flex: 1, backgroundColor: 'white' }}>
      {/* Render the FlatList with the chat data */}
      <FlatList
        data={data}
        keyExtractor={(item) => item._id}
        refreshing={isFetching}
        onRefresh={onRefresh}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <BlankChatSVG style={{ alignSelf: 'center' }} />
            <ChatTextSVG style={{ marginTop: rv(20) }} />
          </View>
        )}
        renderItem={({ item }) => (
          <ChatCard item={item} navigation={navigation} refreshFunction={refetch} />
        )}
      />

      
    </View>
  );
}

const styles = StyleSheet.create({
  emptyContainer: {
    alignSelf: 'center',
    marginTop: '40%',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    // Optional: add a semi-transparent background to emphasize the loading state
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
});

export default Chat;

import React, { useState, useEffect } from 'react';
import {
  TextInput,
  Text,
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  BackHandler,
  Share,
  SafeAreaView,
  KeyboardAvoidingView,
  Modal,
  TouchableWithoutFeedback,
  Platform,
  Keyboard,
} from 'react-native';
import Loader from 'app/components/elements/Loader';
// import Icon from 'react-native-vector-icons/FontAwesome';
import SelectAttachment from 'app/components/select-attachment';
import ReportDialog from 'app/components/dialogs/report';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import { CustomText } from 'app/components/elements';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CameraHelper, FileHelper, FileSelectHelper } from 'app/helpers';
import Document from 'app/components/attachments/Document';
import Audio from 'app/components/attachments/Audio';
import Video from 'app/components/attachments/Video';
import { TopicCard } from 'app/components/cards';
import Avatar from 'app/components/elements/Avater';
import { useSelector } from 'react-redux';
import { userAuthInfo } from 'app/redux/user/reducer';
import {
  useCreateComment,
  useCreateSubComment,
  useSingleTopic,
  useTopicComments,
} from 'app/redux/topic/hooks';
import { Axios } from 'app/api/axios';
import ThumbSVG from 'app/assets/svg/thumb.svg';
import CommentSVG from 'app/assets/svg/comment.svg';
import { timeAgo } from 'app/helpers/time-ago';
import CommentCard from 'app/components/cards/comment';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import CloseSVG from 'app/assets/svg/close.svg';
import AttachmentSVG from 'app/assets/svg/attachment-icon.svg';
import ChatSendSVG from 'app/assets/svg/chat-send.svg';
import HeaderTitle from 'app/components/HeaderTitle';
import { useTranslation } from 'react-i18next';
import EmojiPicker from 'rn-emoji-keyboard';
import { InputToolbar } from 'react-native-gifted-chat';
import Topic from '../bottom/topic';
import SmileySVG from 'app/assets/svg/smiley.svg';
import useTrackActivity from 'app/hooks/useTrackActivity';
import { showModal, hideModal } from 'app/providers/modals';

function ViewTopic({ navigation, route }: any) {
  const profile = useSelector(userAuthInfo);
  const Loading = require('app/assets/loading.gif');

  const { postid } = route.params;
  const { t } = useTranslation();

  const { data, refetch, isFetching } = useSingleTopic(postid);
  const {
    data: comments,
    refetch: refetchComment,
    isFetching: isFetchingComments,
  } = useTopicComments(postid);
  const { mutateAsync: comment, isLoading: loadingCreateSubComment } =
    useCreateComment();
  const { mutateAsync: subComment, isLoading: loadingCreateComment } =
    useCreateSubComment();

  const isSending = loadingCreateComment || loadingCreateSubComment;
  const loading = isFetching;
  const [userComment, setUserComment] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [inputHeight, setInputHeight] = useState(0);
  const [showAttachmentDialog, setShowAttachmentDialog] = useState(false);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [processingFile, setProcessingFile] = useState(false);
  const [chatDetails, setChatDetails] = useState<any>(null);
  const { userDetails, bot } = route.params;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [text, setTextInputValue] = useState(''); // State for text input value
  const [subCommentData, setSubCommentData] = useState<any>(null);
  const { trackActivity, activityCounts, modalVisible } =
    useTrackActivity('sponsor');
  const { trackActivity: trackShare } = useTrackActivity('share');

  const handleSponsorAndShare = () => {
    trackActivity();
    trackShare();
    console.log(activityCounts, 'activityCounts');
  };

  let numOfLines = 0;

  const _handleTextChange = (inputValue: string) => {
    setInputValue(inputValue);
    setUserComment(inputValue);
  };

  const handleEmojiSelected = (emoji: any) => {
    const updatedText = `${inputValue} ${emoji.emoji}`;
    setInputValue(updatedText);
    setUserComment(updatedText);
  };
  const handleComposerTextChanged = (text: string) => {
    setTextInputValue(text); // Update the text input value in the state
  };

  const _handleSizeChange = (event: any) => {
    console.log(
      '_handleSizeChange ---->',
      event.nativeEvent.contentSize.height
    );

    setInputHeight(event.nativeEvent.contentSize.height);
  };

  const backAction = () => {
    if (showAttachmentDialog == true) {
      setShowAttachmentDialog(!showAttachmentDialog);
      return true;
    } else {
      return false;
    }
  };

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );

    return () => backHandler.remove();
  }, [showAttachmentDialog]);

  useEffect(() => {
    if (comments) {
      console.log('🚀 - comments:', comments);
    }
  }, [comments]);
  const toggleEmojiPicker = () => {
    setShowEmojiPicker(!showEmojiPicker);
  };

  const openShareAttachment = async (attachmentType: string) => {
    try {
      let file = null;
      if (attachmentType == 'camera') {
        file = await CameraHelper.openCamera();
        attachmentType = 'image';
      } else if (attachmentType == 'image') {
        file = await CameraHelper.selectImageFromGallery();
      } else if (attachmentType == 'video') {
        file = await CameraHelper.selectVideoFromGallery(setProcessingFile);
      } else {
        file = await FileSelectHelper(attachmentType);
      }

      if (!file) {
        return;
      }

      navigation.push('Common', {
        screen: 'share-attachment',
        params: {
          attachmentType,
          attachment: file,
          name: 'Share Comment Attachment',
          post: data,
          chatType: 'topic-comment',
        },
      });
    } catch (err) {
      alert(err);
    }
  };

  async function createComment() {
    try {
      const commentData = new FormData();
      commentData.append('comment', userComment);

      let payload = {
        postId: postid,
        data: commentData,
      };

      await comment(payload); // Await to properly handle promise resolution
      setInputValue('');
      setUserComment('');
      refetchComment();
    } catch (error: any) {
      // Re-throw the error for the parent function to catch
      console.log('errorIbk', error);
      throw error;
    }
  }

  async function createSubComment() {
    try {
      if (subCommentData && subCommentData.id) {
        let payload = {
          commentId: subCommentData.id,
          data: {
            comment: userComment,
          },
        };

        await subComment(payload); // Await to properly handle promise resolution
        setInputValue('');
        setUserComment('');
        setSubCommentData(null);
        refetchComment();
      }
    } catch (error: any) {
      // Re-throw the error for the parent function to catch
      throw error;
    }
  }

  const submitComment = async () => {
    // Prevent duplicate submissions: if already submitting, return early
    if (isSubmitting || !userComment.trim()) return;
  
    setIsSubmitting(true);
    try {
      if (subCommentData && subCommentData.id) {
        await createSubComment();
      } else {
        await createComment();
      }
  
      // Clear states after a successful submission
      setUserComment('');
      setSubCommentData(null);
      setInputValue('');
      handleSponsorAndShare();
      refetchComment();
    } catch (error:any) {
      console.log(error?.message || 'Error occurred', 'errorIbk');
      // You can either read from error?.message
      // or from error?.response?.data?.message if it's an Axios error
      let errorMessage =
        error?.response?.data?.message || // If it's a server error with a message
        error?.message || // If it's a direct error object
        'An error occurred';

      let modalType = 'alert';

      // Check if the error is subscription-related
      if (
        errorMessage === 'Your subscription has expired' ||
        errorMessage === 'Your subscription was not found'
      ) {
        modalType = 'credit';
      }

      showModal({
        modalVisible: true,
        title: 'Alert',
        message: errorMessage,
        setModalVisible: hideModal,
        type: modalType,
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // Error handling code, e.g., showing a modal, can be placed here
    } finally {
      // Re-enable submission once the process is complete
      setIsSubmitting(false);
    }
  };
  const [isInputFocused, setIsInputFocused] = useState(false);

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: 'white' }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? hp('6%') : 13}
    >
      {isInputFocused && (
        <TouchableWithoutFeedback
          onPress={() => {
            Keyboard.dismiss();
            setIsInputFocused(false);
          }}
        >
          <View style={styles.overlay} />
        </TouchableWithoutFeedback>
      )}
      <SafeAreaView style={{}}>
        {/* Your top section content */}
        <HeaderTitle title={t('comments_title')} navigation={navigation} />
      </SafeAreaView>

      {/* <Loader loading={isFetching || isFetchingComments || loadingCreateComment} /> */}

      <ReportDialog
        show={showReportDialog}
        setShow={setShowReportDialog}
        reportDetails={reportDetails}
      />
      {isFetching ? (
        // Show loader while data is being fetched or a comment is being sent
        <View
          style={{
            flex: 1, // Fill the available space
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image source={Loading} style={{ width: rv(50), height: rv(50) }} />
        </View>
      ) : showAttachmentDialog ? (
        <TouchableWithoutFeedback
          onPress={() => setShowAttachmentDialog(false)}
        >
          <View style={{ flex: 1 }} pointerEvents='box-none'>
            {/* Main Content */}
            <ScrollView
              style={{ backgroundColor: '#ffffff', paddingBottom: 70 }}
            >
              <StatusBar barStyle='dark-content' backgroundColor='white' />

              {data ? (
                <TopicCard
                  item={data}
                  navigation={navigation}
                  refreshFunction={() => refetch()}
                />
              ) : null}

              {isSending ? (
                // Show loader while data is being fetched or a comment is being sent
                <View
                  style={{
                    flex: 1, // Fill the available space
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Image
                    source={Loading}
                    style={{ width: rv(50), height: rv(50) }}
                  />
                </View>
              ) : (
                <View style={{}}>
                  {comments && comments.length > 0 ? (
                    comments.map((item: any, index: number) => (
                      <CommentCard
                        key={index}
                        item={item}
                        navigation={navigation}
                        setSubCommentData={setSubCommentData}
                        refreshFunction={() => refetchComment()}
                      />
                    ))
                  ) : (
                    <Text></Text>
                  )}
                </View>
              )}
            </ScrollView>
            {/* Reply Comment and Input Area */}
            <View
              style={{
                flexDirection: 'column',
                backgroundColor: 'black',
                borderTopLeftRadius: rv(10),
                borderTopRightRadius: rv(10),
              }}
            >
              {subCommentData ? (
                <View
                  style={{
                    margin: rv(10),
                    flexDirection: 'column',
                    padding: rv(10),
                    borderLeftWidth: rv(7),
                    borderLeftColor: '#F19C4A',
                    backgroundColor: '#3D3C39',
                    borderRadius: rv(10),
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}
                  >
                    <CustomText
                      style={{
                        color: '#F19C4A',
                        fontFamily: 'semiBold'
                      }}
                      // textType='semi-bold'
                    >
                      Replying to
                    </CustomText>

                    <TouchableOpacity
                      onPress={() => {
                        setSubCommentData(null);
                      }}
                      hitSlop={{ top: 40, bottom: 40, left: 40, right: 40 }}
                    >
                      <CloseSVG
                        width={rv(13)}
                        height={rv(13)}
                        color={'#F19C4A'}
                      />
                    </TouchableOpacity>
                  </View>

                  <View>
                    <CustomText
                      style={{
                        color: 'white',
                        fontFamily: 'medium'
                      }}
                    >
                      {subCommentData.comment}
                    </CustomText>
                  </View>
                </View>
              ) : null}

              <View
                style={{
                  width: '100%',
                  alignItems: 'center',
                  flexDirection: 'row',
                  paddingHorizontal: 10,
                  paddingVertical: 7,

                  paddingBottom: rv(10),
                }}
              >
                <View
                  style={
                    {
                      // justifyContent: 'center',
                      // alignItems: 'center',
                    }
                  }
                >
                  <View style={{}}>
                    {showEmojiPicker && (
                      <EmojiPicker
                        onEmojiSelected={handleEmojiSelected}
                        open={showEmojiPicker}
                        onClose={toggleEmojiPicker}
                      />
                    )}
                    <TouchableOpacity
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: 5,
                        paddingHorizontal: 8,
                        paddingBottom: 10,
                      }}
                      onPress={toggleEmojiPicker}
                    >
                      <SmileySVG width={rv(25)} height={rv(25)} />
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    flex: 1,
                    width: 'auto',
                    // marginTop: 'auto',
                    flexDirection: 'row',
                    // alignItems: 'center',
                    // justifyContent: 'center',
                  }}
                >
                  <View
                    style={[
                      {
                        flex: 1,
                        // justifyContent: 'center',
                        // paddingBottom: Math.max(hp('6%'), inputHeight - 5),
                      },
                    ]}
                  >
                    <View
                      style={{
                        maxHeight: hp('20'),
                      }}
                    >
                      <TextInput
                        value={inputValue}
                        placeholder={t('comments_typeMessage')}
                        placeholderTextColor='#F4F5FA'
                        autoCapitalize='sentences'
                        maxLength={150}
                        multiline={true}
                        // enablesReturnKeyAutomatically={true}
                        blurOnSubmit={true}
                        onChangeText={_handleTextChange}
                        numberOfLines={numOfLines}
                        onContentSizeChange={(event) =>
                          _handleSizeChange(event)
                        }
                        style={[
                          {
                            minHeight: 45,
                            height: inputHeight,
                            fontFamily: 'regular',
                            fontSize: rv(13),
                            color: 'white',
                            lineHeight: 20,
                            borderRadius: 30,
                            padding: 10,
                            paddingTop: 12,
                            backgroundColor: '#3D3C39',
                          },
                        ]}
                      />
                    </View>
                  </View>

                  <View
                    style={{
                      marginHorizontal: 5,
                      justifyContent: 'center',
                    }}
                  >
                    <TouchableOpacity
                      onPress={() => {
                        setShowAttachmentDialog(!showAttachmentDialog);
                      }}
                      style={{
                        display: subCommentData ? 'none' : 'flex',
                      }}
                    >
                      <AttachmentSVG width={rv(20)} height={rv(20)} />
                    </TouchableOpacity>
                  </View>

                  <TouchableOpacity activeOpacity={0.5} onPress={submitComment}>
                    <ChatSendSVG width={rv(43)} />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      ) : (
        // When dialog is not open, render content normally
        <View style={{ flex: 1 }}>
          {/* Main Content */}
          <ScrollView style={{ backgroundColor: '#ffffff', paddingBottom: 70 }}>
            <StatusBar barStyle='dark-content' backgroundColor='white' />

            {data ? (
              <TopicCard
                item={data}
                navigation={navigation}
                refreshFunction={() => refetch()}
              />
            ) : null}

            {isSending ? (
              // Show loader while data is being fetched or a comment is being sent
              <View
                style={{
                  flex: 1, // Fill the available space
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Image
                  source={Loading}
                  style={{ width: rv(50), height: rv(50) }}
                />
              </View>
            ) : (
              <View style={{}}>
                {comments && comments.length > 0 ? (
                  comments.map((item: any, index: number) => (
                    <CommentCard
                      key={index}
                      item={item}
                      navigation={navigation}
                      setSubCommentData={setSubCommentData}
                      refreshFunction={() => refetchComment()}
                    />
                  ))
                ) : (
                  <Text></Text>
                )}
              </View>
            )}
          </ScrollView>

          {/* Reply Comment and Input Area */}
          <View
            style={{
              flexDirection: 'column',
              backgroundColor: 'black',
              borderTopLeftRadius: rv(10),
              borderTopRightRadius: rv(10),
            }}
          >
            {subCommentData ? (
              <View
                style={{
                  margin: rv(10),
                  flexDirection: 'column',
                  padding: rv(10),
                  borderLeftWidth: rv(7),
                  borderLeftColor: '#F19C4A',
                  backgroundColor: '#3D3C39',
                  borderRadius: rv(10),
                }}
              >
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}
                >
                  <CustomText
                    style={{
                      color: '#F19C4A',
                      fontFamily: 'semiBold'
                    }}
                    // textType='semi-bold'
                  >
                    Replying to
                  </CustomText>

                  <TouchableOpacity
                    onPress={() => {
                      setSubCommentData(null);
                    }}
                  >
                    <CloseSVG
                      width={rv(13)}
                      height={rv(13)}
                      color={'#F19C4A'}
                    />
                  </TouchableOpacity>
                </View>

                <View>
                  <CustomText
                    style={{
                      color: 'white',
                      fontFamily: 'medium'
                    }}
                  >
                    {subCommentData.comment}
                  </CustomText>
                </View>
              </View>
            ) : null}

            <View
              style={{
                width: '100%',
                alignItems: 'center',
                flexDirection: 'row',
                paddingHorizontal: 10,
                paddingVertical: 7,

                paddingBottom: rv(10),
              }}
            >
              <View
                style={
                  {
                    // justifyContent: 'center',
                    // alignItems: 'center',
                  }
                }
              >
                <View style={{}}>
                  {showEmojiPicker && (
                    <EmojiPicker
                      onEmojiSelected={handleEmojiSelected}
                      open={showEmojiPicker}
                      onClose={toggleEmojiPicker}
                    />
                  )}
                  <TouchableOpacity
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 5,
                      paddingHorizontal: 8,
                      paddingBottom: 10,
                    }}
                    onPress={toggleEmojiPicker}
                  >
                    <SmileySVG width={rv(25)} height={rv(25)} />
                  </TouchableOpacity>
                </View>
              </View>
              <View
                style={{
                  flex: 1,
                  width: 'auto',
                  // marginTop: 'auto',
                  flexDirection: 'row',
                  // alignItems: 'center',
                  // justifyContent: 'center',
                }}
              >
                <View
                  style={[
                    {
                      flex: 1,
                      // justifyContent: 'center',
                      // paddingBottom: Math.max(hp('6%'), inputHeight - 5),
                    },
                  ]}
                >
                  <View
                    style={{
                      maxHeight: hp('20'),
                    }}
                  >
                    <TextInput
                      value={inputValue}
                      placeholder={t('comments_typeMessage')}
                      placeholderTextColor='#F4F5FA'
                      autoCapitalize='sentences'
                      maxLength={150}
                      multiline={true}
                      // enablesReturnKeyAutomatically={true}
                      blurOnSubmit={true}
                      onChangeText={_handleTextChange}
                      numberOfLines={numOfLines}
                      onContentSizeChange={(event) => _handleSizeChange(event)}
                      style={[
                        {
                          minHeight: 45,
                          height: inputHeight,
                          fontFamily: 'regular',
                          fontSize: rv(13),
                          color: 'white',
                          lineHeight: 20,
                          borderRadius: 30,
                          padding: 10,
                          paddingTop: 12,
                          backgroundColor: '#3D3C39',
                        },
                      ]}
                    />
                  </View>
                </View>

                <View
                  style={{
                    marginHorizontal: 5,
                    justifyContent: 'center',
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      setShowAttachmentDialog(!showAttachmentDialog);
                    }}
                    style={{
                      display: subCommentData ? 'none' : 'flex',
                    }}
                  >
                    <AttachmentSVG width={rv(20)} height={rv(20)} />
                  </TouchableOpacity>
                </View>

                <TouchableOpacity activeOpacity={0.5} onPress={submitComment}>
                  <ChatSendSVG width={rv(43)} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      )}

      <View style={{ width: wp('100%') }}>
        <SelectAttachment
          show={showAttachmentDialog}
          openShareAttachment={openShareAttachment}
          source={'topic-comment'}
        />
      </View>

      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelected={handleEmojiSelected}
          open={showEmojiPicker}
          onClose={toggleEmojiPicker}
        />
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    width: wp('80%'), // Adjust as needed
    backgroundColor: 'white',
    borderRadius: 10,
    padding: rv(20),
    // Add any other styles you need
  },
});

export default ViewTopic;

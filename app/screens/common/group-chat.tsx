import * as Clipboard from 'expo-clipboard';
import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  FlatList,
  Modal,
  Platform,
} from 'react-native';
import { v4 as uuidv4 } from 'uuid';
import { useSelector } from 'react-redux';
import { userId, userToken } from 'app/redux/user/reducer';
import { Axios, MAIN_URL } from 'app/api/axios';
import socketIOClient from 'socket.io-client';
import { useTranslation } from 'react-i18next';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Ionicons from '@expo/vector-icons/Ionicons';
import EmojiPicker from 'rn-emoji-keyboard';
import { Provider, Menu } from 'react-native-paper';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';

import { hideModal, showModal } from 'app/providers/modals';
import SelectContact from 'app/components/SelectContact';
import SelectAttachment from 'app/components/select-attachment';
import { CameraHelper, FileSelectHelper, FileHelper } from 'app/helpers';

import ChatSendSVG from 'app/assets/svg/chat-send.svg';
import SmileySVG from 'app/assets/svg/smiley.svg';
import AttachmentSVG from 'app/assets/svg/attachment-icon.svg';
import MenuSVG from 'app/assets/svg/menu.svg';

import { responsiveValue as rv } from 'app/providers/responsive-value';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

import useTrackActivity from 'app/hooks/useTrackActivity';
const Loading = require('../../assets/loading.gif');
import { customParsePatterns } from 'app/helpers/linkpattern';

// ------- STYLES ----------
import { StyleSheet } from 'react-native';

// -------------- MAIN COMPONENT ---------------
function GroupChatContent({ navigation, route }: any) {
  const { groupDetails } = route.params;
  const insets = useSafeAreaInsets();

  const appToken = useSelector(userToken);
  const myId = useSelector(userId);
  const { t } = useTranslation();

  // LOCAL STATE
  const [messages, setMessages] = useState<any[]>([]);
  const [pageNumber, setPageNumber] = useState(1);
  const [totalPageNumber, setTotalPageNumber] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingOlderMessages, setIsLoadingOlderMessages] = useState(false);
  const [processingFile, setProcessingFile] = useState(false);

  // For searching:
  const [openSearch, setOpenSearch] = useState(false);
  const [searchInput, setSearchInput] = useState('');

  // For sending text & attachments:
  const [text, setTextInputValue] = useState('');
  const [showAttachmentDialog, setShowAttachmentDialog] = useState(false);
  const [contactDetails, setContactDetails] = useState(null);
  const [showSelectContact, setShowSelectContact] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // For quoting replies
  const [quotedData, setQuotedData] = useState<any>(null);

  // For membership handling
  const [showJoinGroup, setShowJoinGroup] = useState(true);

  // For real-time
  const socket = useRef<any>(null);
  const [isSending, setIsSending] = useState(false);

  // For the action sheet (long press) replacement
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [actionSheetOptions, setActionSheetOptions] = useState<string[]>([]);
  const [cancelButtonIndex, setCancelButtonIndex] = useState<number>(0);
  const [onActionSheetSelect, setOnActionSheetSelect] = useState<
    (index: number) => void
  >(() => {});

  // For tracking sponsor/share
  const { trackActivity, activityCounts } = useTrackActivity('sponsor');
  const { trackActivity: trackShare } = useTrackActivity('share');

  const handleSponsorAndShare = () => {
    trackActivity();
    trackShare();
    // console.log(activityCounts, "activityCounts");
  };

  // ---------- SOCKET SETUP ----------
  useEffect(() => {
    if (appToken && groupDetails?._id && !socket.current) {
      const connectionOptions: any = {
        'force new connection': true,
        reconnectionAttempts: 'Infinity',
        timeout: 100000,
        transports: ['websocket'],
        auth: { token: appToken },
      };

      socket.current = socketIOClient(MAIN_URL, connectionOptions);

      // Join the group:
      socket.current.emit('join-group', groupDetails._id, (response: any) => {
        // If there's an error or success message, you can handle it here
        // console.log('join-group response => ', response);
      });

      // Listen to incoming group messages
      socket.current.on('group-messages', (data: any) => {
        if (data.deletedMessage) {
          // It's a "deleted message" event
          setMessages((prev) =>
            prev.filter((m: any) => m._id !== data.messageId)
          );
        } else {
          // It's a new or updated message
          const serverMsg = formatMessage(data);

          setMessages((prev: any) => {
            // If there's a local pending version, replace it
            let foundLocalPending = false;
            const updatedList = prev.map((m: any) => {
              // Compare by localId or by text
              if (
                m.pending &&
                (m.text === serverMsg.text || data.localId === m._id)
              ) {
                foundLocalPending = true;
                return { ...serverMsg, pending: false };
              }
              return m;
            });
            if (!foundLocalPending) {
              // brand-new from server
              return [serverMsg, ...updatedList];
            }
            return updatedList;
          });
          setIsSending(false);
        }
      });
    }
  }, [appToken, groupDetails]);

  // ---------- CHECK IF USER IS PARTICIPANT ----------
  useEffect(() => {
    if (groupDetails && groupDetails._id && myId) {
      const isParticipant = groupDetails.participants?.some(
        (id: string) => id === myId
      );
      setShowJoinGroup(!isParticipant);
    }
  }, [groupDetails, myId]);

  // ---------- SEARCH SIDE EFFECT ----------
  useEffect(() => {
    if (searchInput.trim() !== '') {
      searchChat();
    }
  }, [searchInput]);

  // ---------- LOAD DATA (MESSAGES) ----------
  const loadData = async () => {
    if (!groupDetails?._id) return;
    try {
      setIsLoadingOlderMessages(true);
      setIsLoading(true);
      const res = await Axios.get(
        `/group/chats/${groupDetails._id}?page=${pageNumber}&limit=10`
      );
      const { docs, totalPages } = res.data?.groupChats || {};
      setTotalPageNumber(totalPages || 0);

      // Format each doc
      const newMessages = (docs || []).map(formatMessage);

      setMessages((prev) => [...prev, ...newMessages]);
    } catch (error) {
      // console.log('Error fetching messages: ', error);
    } finally {
      setIsLoading(false);
      setIsLoadingOlderMessages(false);
    }
  };

  // LOAD on mount (page=1) or next pages
  useEffect(() => {
    if (groupDetails?._id) {
      loadData();
    }
  }, [pageNumber, groupDetails]);

  // ---------- HELPER: FORMAT MESSAGE ----------
  const formatMessage = (chatMessage: any) => {
    const msg = { ...chatMessage };
    msg.text = msg.message || 'NO_MESSAGE';
    msg.emptyText = !msg.message;
    // If contact
    if (msg.type === 'contact' && msg.attachment) {
      try {
        msg.contact = JSON.parse(msg.attachment);
      } catch {}
    }
    // If not a system message, define msg.user
    if (!msg.system && msg.senderId) {
      msg.user = {
        _id: msg.senderId._id,
        name: `${msg.senderId.first_name} ${msg.senderId.last_name}`,
        avatar: msg.senderId.profile_picture || require('app/assets/user.jpg'),
      };
    }
    // Ensure _id is a string
    msg._id = msg._id ? msg._id.toString() : uuidv4();
    return msg;
  };

  // ---------- SEARCH MESSAGES ----------
  const searchChat = async () => {
    if (!groupDetails?._id || !searchInput.trim()) return;
    try {
      const res = await Axios.post(`/group/search/${groupDetails._id}`, {
        search: searchInput.trim(),
      });
      const { docs, totalPages } = res.data || {};
      setTotalPageNumber(totalPages || 0);
      const newMessages = (docs || []).map(formatMessage);
      setMessages(newMessages);
    } catch (error) {
      // console.log('Search error: ', error);
    }
  };

  // ---------- PAGINATION LOADER ----------
  const loadNewerMessages = () => {
    if (pageNumber < totalPageNumber) {
      setPageNumber((prev) => prev + 1);
    }
  };

  // ---------- JOIN / LEAVE GROUP ----------
  const joinGroup = async () => {
    if (!groupDetails?._id) return;
    setIsLoading(true);
    try {
      await Axios.patch(`/group/join/${groupDetails._id}`);
      // Successfully joined => not show the join button anymore
      setShowJoinGroup(false);
    } catch (error) {
      // handle error
    } finally {
      setIsLoading(false);
    }
  };

  const leaveGroup = async () => {
    if (!groupDetails?._id) return;
    setIsLoading(true);
    try {
      await Axios.delete(`/group/leave/${groupDetails._id}`);
      // If left the group => goBack
      setShowJoinGroup(true);
      navigation.goBack();
    } catch (error) {
      // handle error
    } finally {
      setIsLoading(false);
    }
  };

  // ---------- SENDING MESSAGES ----------
  const onSend = (newMessageText: string) => {
    if (!newMessageText.trim()) return;
    setIsSending(true);

    // Local “pending” message
    const localId = uuidv4();
    const now = new Date();
    const localMessage = {
      _id: localId,
      text: newMessageText,
      createdAt: now,
      user: { _id: myId, name: 'You' },
      pending: true,
      ...(quotedData && { quotedReply: quotedData }),
    };
    // Add to local
    setMessages((prev) => [localMessage, ...prev]);
    setTextInputValue(''); // Clear input
    // Build payload for the server
    const payload: any = {
      groupId: groupDetails._id,
      message: newMessageText,
      type: 'message',
      token: appToken,
      localId, // so we can match pending messages if needed
      clientCreatedAt: now,
    };
    // If quoting
    if (quotedData) {
      payload.quotedData = quotedData;
      setQuotedData(null);
    }
    // Emit
    socket.current?.emit('Group Chat', payload, (response: any) => {
      if (!response.success) {
        // Remove local pending message
        setMessages((prev) => prev.filter((m) => m._id !== localId));

        // Show error modal
        const errorMessage = response.message;
        let modalType = 'alert';
        if (
          errorMessage === 'Your subscription has expired' ||
          errorMessage === 'Your subscription was not found'
        ) {
          modalType = 'credit';
        }
        showModal({
          modalVisible: true,
          title: 'Alert',
          message: response.message,
          setModalVisible: hideModal,
          type: modalType,
          handleConfirm: () => hideModal(),
          handleAlert: () => hideModal(),
          navigation,
        });
      }
      setIsSending(false);
    });
    handleSponsorAndShare();
  };

  // ---------- DELETE MESSAGES ----------
  const deleteMessage = async (messageId: string) => {
    if (!messageId) return;
    try {
      await Axios.delete(`/group/delete-message/${messageId}`);
      // Removal from local state is handled by the 'group-messages' event or local logic
    } catch (error) {
      // console.log('Error deleting message: ', error);
    }
  };

  // ---------- OPEN ATTACHMENT -----------
  const openShareAttachment = async (attachmentType: string) => {
    try {
      let file = null;
      let fileType = attachmentType;

      switch (attachmentType) {
        case 'camera':
          file = await CameraHelper.openCamera();
          fileType = 'image';
          break;
        case 'image':
          file = await CameraHelper.selectImageFromGallery();
          fileType = 'image';
          break;
        case 'video':
          file = await CameraHelper.selectVideoFromGallery(setProcessingFile);
          fileType = 'video';
          break;
        case 'contact':
          // If contactDetails is set
          if (contactDetails) {
            file = contactDetails;
            fileType = 'contact';
          }
          break;
        default:
          // 'audio', 'document', etc.
          file = await FileSelectHelper(attachmentType);
          break;
      }

      if (!file) return;

      navigation.push('Common', {
        screen: 'share-attachment',
        params: {
          attachmentType: fileType,
          attachment: file,
          name: `Share to ${groupDetails.name}`,
          chatType: 'group',
          chatId: groupDetails._id,
        },
      });
    } catch (err: any) {
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: err?.message || String(err),
        setModalVisible: hideModal,
        type: 'alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
        navigation,
      });
    }
  };

  // ---------- WHEN CONTACT SELECTED ----------
  useEffect(() => {
    if (contactDetails) {
      openShareAttachment('contact');
    }
  }, [contactDetails]);

  // ---------- TOGGLE SEARCH ----------
  const toggleOpenSearch = () => {
    if (openSearch) {
      // close search => reload original messages
      setMessages([]);
      setPageNumber(1);
      loadData();
    }
    setOpenSearch(!openSearch);
  };

  // ---------- RENDER HEADER ----------
  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.headerLeft}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name='chevron-back' size={22} color='black' />
        </TouchableOpacity>
        {openSearch ? (
          <TextInput
            placeholderTextColor={'grey'}
            style={styles.headerSearchInput}
            onChangeText={setSearchInput}
            placeholder={t('Groups_SearchGroup') + '...'}
            value={searchInput}
          />
        ) : (
          <TouchableOpacity
            onPress={() => {
              // navigate to group-info if not searching
              if (groupDetails) {
                navigation.navigate('group-info', { groupDetails });
              }
            }}
            style={styles.headerTitleContainer}
          >
            <Image
              source={
                groupDetails?.image
                  ? { uri: groupDetails.image }
                  : require('app/assets/lucosa-thumbnai.png')
              }
              style={styles.groupAvatar}
            />
            <Text style={styles.groupTitle} numberOfLines={1}>
              {groupDetails?.name || ''}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.headerRight}>
        <TouchableOpacity onPress={toggleOpenSearch} style={styles.iconWrapper}>
          <FontAwesome
            name={openSearch ? 'times' : 'search'}
            size={20}
            color='black'
          />
        </TouchableOpacity>
        <Menu
          visible={showMenu}
          onDismiss={closeMenu}
          anchor={
            <TouchableOpacity onPress={openMenu} style={styles.iconWrapper}>
              <MenuSVG width={15} height={15} />
            </TouchableOpacity>
          }
        >
          <Menu.Item
            onPress={() => {
              if (groupDetails) {
                navigation.navigate('group-info', { groupDetails });
              }
            }}
            title={t('Groups_opt_groupInfo')}
            titleStyle={styles.menuItemTitle}
          />
          {showJoinGroup ? (
            <Menu.Item
              onPress={joinGroup}
              title={t('Groups_joinGroups')}
              titleStyle={styles.menuItemTitle}
            />
          ) : (
            <Menu.Item
              onPress={leaveGroup}
              title={t('Groups_opt_exitGroup')}
              titleStyle={styles.menuItemTitle}
            />
          )}
        </Menu>
      </View>
    </View>
  );

  // ---------- MENU TOGGLE ----------
  const [showMenu, setShowMenu] = useState(false);
  const openMenu = () => setShowMenu(true);
  const closeMenu = () => setShowMenu(false);

  // ---------- LONG PRESS (ACTION SHEET) ----------
  const handleLongPress = (message: any) => {
    const isMyMessage = message?.user?._id === myId;
    if (!message) return;

    let options: string[] = isMyMessage
      ? ['Reply', 'Delete Message', 'Copy Text', 'Cancel']
      : ['Reply', 'Copy Text', 'Cancel'];

    const cancelIndex = options.length - 1;
    setActionSheetOptions(options);
    setCancelButtonIndex(cancelIndex);
    setActionSheetVisible(true);
    setOnActionSheetSelect(() => (index: number) => {
      if (isMyMessage) {
        switch (index) {
          case 0:
            setQuotedData({
              message: message.text,
              name: 'You',
            });
            break;
          case 1:
            // Delete
            deleteMessage(message._id);
            // Optionally show a toast or modal
            showModal({
              modalVisible: true,
              title: 'Alert',
              message: 'Message deleted',
              setModalVisible: hideModal,
              type: 'success-alert',
              handleConfirm: hideModal,
              handleAlert: hideModal,
            });
            break;
          case 2:
            // Copy (uncomment if implementing actual copy)
            Clipboard.setStringAsync(message.text).then(() => {
              console.log('copied');
            });
            break;
          default:
            break;
        }
      } else {
        // Not my message
        switch (index) {
          case 0:
            setQuotedData({
              message: message.text,
              name: message.user?.name || 'Unknown',
            });
            break;
          case 1:
            // Copy text
            Clipboard.setStringAsync(message.text).then(() => {
              console.log('copied');
            });
            // showToast('Copied to clipboard');
            break;
          default:
            break;
        }
      }
    });
  };

  // ---------- GROUP MESSAGES BY DATE ----------
  const groupedMessages = useMemo(() => {
    if (!messages || messages.length === 0) return [];

    // Sort ascending by date
    const sorted = [...messages].sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    let lastDate = '';
    const grouped: any[] = [];
    sorted.forEach((msg) => {
      const msgDate = new Date(msg.createdAt).toDateString();
      if (msgDate !== lastDate) {
        // Insert "day separator"
        grouped.push({
          _id: `day-${msg._id}`,
          type: 'day',
          date: msgDate,
        });
        lastDate = msgDate;
      }
      grouped.push(msg);
    });

    // Reverse for "inverted" in FlatList
    return grouped.reverse();
  }, [messages]);

  // ---------- RENDER MESSAGE ITEM ----------
  const renderItem = useCallback(({ item }) => {
    if (item.type === 'day') {
      // Day separator
      return (
        <View style={styles.daySeparatorContainer}>
          <Text style={styles.daySeparatorText}>{item.date}</Text>
        </View>
      );
    }
    // Otherwise, normal message
    const position = item.user?._id === myId ? 'right' : 'left';
    return (
      <TouchableOpacity
        onLongPress={() => handleLongPress(item)}
        activeOpacity={0.7}
        style={{ marginHorizontal: 8, marginVertical: 4 }}
      >
        {renderBubble({ currentMessage: item, position })}
      </TouchableOpacity>
    );
  }, []);

  // ---------- RENDER BUBBLE (similar to new private chat) ----------
  const renderBubble = ({ currentMessage, position }: any) => {
    const isMyMessage = position === 'right';
    const bubbleStyles = isMyMessage
      ? {
          backgroundColor: '#E5E5E5',
          borderTopLeftRadius: 20,
          borderBottomLeftRadius: 20,
          borderTopRightRadius: 20,
          borderBottomRightRadius: 0,
        }
      : {
          backgroundColor: '#4F4F4F',
          borderTopLeftRadius: 0,
          borderBottomLeftRadius: 20,
          borderTopRightRadius: 20,
          borderBottomRightRadius: 20,
        };

    const isAttachment =
      currentMessage.type === 'image' ||
      currentMessage.type === 'video' ||
      currentMessage.type === 'document' ||
      currentMessage.type === 'audio' ||
      currentMessage.type === 'contact';

    // 1) If it’s an attachment, show a preview
    if (isAttachment) {
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => {
            if (currentMessage.type === 'image') {
              navigation.push('Common', {
                screen: 'fullscreen-image',
                params: { image: currentMessage.attachment },
              });
            } else {
              navigation.push('Common', {
                screen: 'preview-attachment',
                params: { ...currentMessage },
              });
            }
          }}
          style={[
            styles.bubbleContainer,
            isMyMessage
              ? styles.myBubbleContainer
              : styles.otherBubbleContainer,
            { padding: 5 },
          ]}
        >
          {currentMessage.type === 'image' && (
            <View style={{ width: wp('45%'), height: hp('30%') }}>
              <Image
                source={{ uri: currentMessage.attachment }}
                style={{ width: '100%', height: '100%' }}
                resizeMode='cover'
              />
            </View>
          )}
          {currentMessage.type === 'video' && (
            <View
              style={{
                width: wp('45%'),
                height: hp('30%'),
                // Removed paddingTop if not needed
              }}
            >
              {/* Use absolute positioning for the overlay */}
              <View
                style={{
                  position: 'absolute',
                  top: '40%', // Adjust as needed
                  left: 0,
                  right: 0,
                  justifyContent: 'center',
                  alignItems: 'center',
                  zIndex: 999,
                }}
              >
                <FontAwesome name='play-circle' size={35} color='white' />
                <Text
                  style={{ color: 'white', marginTop: 4, fontWeight: 'bold' }}
                >
                  {FileHelper.byteToFileSize(
                    currentMessage.attachmentSize || 0
                  )}
                </Text>
              </View>
              <View
                style={{
                  borderRadius: 20,
                  overflow: 'hidden',
                  backgroundColor: 'black',
                }}
              >
                <Image
                  source={{ uri: currentMessage.attachment }}
                  style={{ width: wp('45%'), height: hp('29%') }}
                  resizeMode='cover'
                />
              </View>
            </View>
          )}
          {currentMessage.type === 'contact' && (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                padding: 15,
                paddingHorizontal: 20,
                borderRadius: 5,
                backgroundColor: 'white',
              }}
            >
              <FontAwesome
                name='address-card'
                size={23}
                style={{ marginRight: 5 }}
                color='#FFCB05'
              />
              <Text
                style={{ fontSize: rv(13), fontWeight: 'bold', marginLeft: 4 }}
              >
                {currentMessage?.contact?.displayName || ''}
              </Text>
            </View>
          )}
          {currentMessage.type === 'document' && (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: 15,
                borderRadius: 5,
                backgroundColor: 'white',
              }}
            >
              <FontAwesome
                name='file'
                size={20}
                color='#FFCB05'
                style={{ paddingRight: 5 }}
              />
              <Text
                style={{
                  fontSize: rv(13),
                  fontWeight: 'bold',
                  paddingRight: 15,
                }}
              >
                {currentMessage.fileName || 'Document File'}
              </Text>
              <Text style={{ fontSize: rv(13) }}>
                {FileHelper.byteToFileSize(currentMessage.attachmentSize || 0)}
              </Text>
            </View>
          )}
          {currentMessage.type === 'audio' && (
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: 15,
                borderRadius: 5,
                backgroundColor: 'white',
              }}
            >
              <FontAwesome
                name='play-circle'
                size={21}
                style={{ marginRight: 5 }}
                color='#FFCB05'
              />
              <Text
                style={{
                  fontSize: rv(13),
                  fontWeight: 'bold',
                  paddingRight: 15,
                }}
              >
                Audio File
              </Text>
              <Text style={{ fontSize: rv(13) }}>
                {FileHelper.byteToFileSize(currentMessage.attachmentSize || 0)}
              </Text>
            </View>
          )}

          {/* Time stamp under the attachment */}
          <Text style={{ fontSize: rv(9), color: '#000', marginTop: 5 }}>
            {new Date(currentMessage.createdAt).toLocaleTimeString()}
          </Text>
        </TouchableOpacity>
      );
    }

    // 2) Normal text bubble (including quoting logic)
    return (
      <View
        style={[
          styles.bubbleContainer,
          isMyMessage ? styles.myBubbleContainer : styles.otherBubbleContainer,
        ]}
      >
        {/* QUOTED MESSAGE */}
        {currentMessage.quotedReply && (
          <View style={styles.quotedContainer}>
            <Text style={styles.quotedHeader} numberOfLines={1}>
              {currentMessage.quotedReply?.name || ''}
            </Text>
            <Text style={styles.quotedText} numberOfLines={2}>
              {currentMessage.quotedReply?.message || ''}
            </Text>
          </View>
        )}
        {/* SENDER NAME (for group) => If not your message */}
        {!isMyMessage && !!currentMessage?.user?.name && (
          <Text style={styles.senderName}>{currentMessage.user.name}</Text>
        )}
        <View style={[styles.textBubble, bubbleStyles]}>
          <Text
            style={[
              styles.messageText,
              isMyMessage ? styles.myMessageText : styles.otherMessageText,
            ]}
          >
            {currentMessage.text}
          </Text>
        </View>
        <Text style={styles.timeText}>
          {new Date(currentMessage.createdAt).toLocaleTimeString()}
        </Text>
      </View>
    );
  };

  // ---------- RENDER FOOTER SPINNER ----------
  const renderFooter = () => {
    if (isLoadingOlderMessages && pageNumber > 1) {
      return (
        <View style={styles.loadingOlderContainer}>
          <ActivityIndicator size='small' />
        </View>
      );
    }
    return null;
  };

  // ---------- RENDER "JOIN GROUP" BUTTON OR INPUT BAR ----------
  const renderInputBar = () => {
    // If user not in group:
    if (showJoinGroup) {
      return (
        <TouchableOpacity onPress={joinGroup} style={styles.joinContainer}>
          <Text style={styles.joinText}>{t('Groups_TaptoJoin')}</Text>
        </TouchableOpacity>
      );
    }

    // Otherwise, show the composer + send, and QUOTED block if present
    return (
      <View style={{ backgroundColor: '#000' /* or whatever color*/ }}>
        {/* QUOTED DATA BLOCK */}
        {quotedData ? (
          <View
            style={{
              backgroundColor: 'black',
              borderTopLeftRadius: 10,
              borderTopRightRadius: 10,
              paddingVertical: 10,
              paddingLeft: 13,
              borderLeftWidth: 10,
              borderLeftColor: '#9FD0D0',
              marginBottom: 5,
              maxHeight: 100,
            }}
          >
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}
            >
              <Text
                style={{ color: '#9FD0D0', fontSize: rv(13), marginBottom: 10 }}
              >
                Replying to {quotedData?.name || ''}
              </Text>
              <TouchableOpacity onPress={() => setQuotedData(null)}>
                <FontAwesome
                  name='times'
                  color='grey'
                  size={20}
                  style={{ paddingHorizontal: 17 }}
                />
              </TouchableOpacity>
            </View>
            {/* The text of the quoted message */}
            <Text
              style={{ color: 'white', fontSize: rv(13) }}
              numberOfLines={2}
              ellipsizeMode='tail'
            >
              {quotedData?.message}
            </Text>
          </View>
        ) : null}

        {/* ACTUAL INPUT ROW */}
        <View style={styles.inputContainer}>
          {/* SMILEY TOGGLE */}
          <TouchableOpacity
            style={styles.smileyButton}
            onPress={() => setShowEmojiPicker(!showEmojiPicker)}
          >
            <SmileySVG width={rv(25)} height={rv(25)} />
          </TouchableOpacity>

          {/* TEXT INPUT */}
          <View style={styles.textInputWrapper}>
            <TextInput
              style={styles.textInput}
              value={text}
              onChangeText={(val) => setTextInputValue(val)}
              placeholder={t('profile_message')}
              placeholderTextColor='#999'
              multiline
              onBlur={() => setShowEmojiPicker(false)}
            />
          </View>

          {/* ATTACHMENT ICON */}
          <TouchableOpacity
            style={styles.attachmentButton}
            onPress={() => setShowAttachmentDialog((prev) => !prev)}
          >
            <AttachmentSVG width={rv(20)} height={rv(20)} />
          </TouchableOpacity>

          {/* SEND ICON */}
          <TouchableOpacity
            style={styles.sendButton}
            onPress={() => onSend(text)}
            disabled={!text.trim() || isSending}
          >
            <ChatSendSVG
              width={rv(30)}
              height={rv(30)}
              fill={!text.trim() ? '#9E9E9E' : '#FFCB05'}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // ---------- MAIN RENDER ----------
  return (
    <Provider>
      <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
        {/* SELECT CONTACT (for "contact" sharing) */}
        <SelectContact
          show={showSelectContact}
          setShow={setShowSelectContact}
          setContact={setContactDetails}
        />
        {/* HEADER */}
        {renderHeader()}
        {/* MAIN CONTENT */}
        {isLoading && pageNumber === 1 ? (
          // INITIAL LOAD
          <View style={styles.initialLoadingContainer}>
            <ActivityIndicator size='large' color='#0000ff' />
          </View>
        ) : (
          <TouchableWithoutFeedback
            onPress={() => {
              Keyboard.dismiss();
              setShowAttachmentDialog(false);
            }}
          >
            <KeyboardAvoidingView
              style={{ flex: 1, backgroundColor: 'white' }}
              behavior={Platform.OS === 'ios' ? 'padding' : undefined}
              keyboardVerticalOffset={
                Platform.OS === 'ios' ? 0 : 0
              }
            >
              <FlatList
                data={groupedMessages}
                inverted
                keyExtractor={(item) => item._id}
                renderItem={renderItem}
                ListFooterComponent={renderFooter}
                onEndReached={loadNewerMessages}
                onEndReachedThreshold={0.5}
                style={{ flex: 1, backgroundColor: 'white' }}
                contentContainerStyle={{ paddingBottom: 10 }}
              />
              {/* COMPOSER / JOIN GROUP */}
              {renderInputBar()}

              {/* ATTACHMENT BOTTOM SHEET */}
              {showAttachmentDialog && (
                <SelectAttachment
                  show={showAttachmentDialog}
                  openShareAttachment={openShareAttachment}
                  setShowSelectContact={setShowSelectContact}
                  source='group'
                />
              )}
            </KeyboardAvoidingView>
          </TouchableWithoutFeedback>
        )}

        {/* EMOJI KEYBOARD */}
        {showEmojiPicker && (
          <EmojiPicker
            onEmojiSelected={(emojiObj) => {
              setTextInputValue((prev) => `${prev} ${emojiObj.emoji}`);
            }}
            open={showEmojiPicker}
            onClose={() => setShowEmojiPicker(false)}
          />
        )}

        {/* CUSTOM ACTION SHEET (MODAL) FOR LONG PRESS */}
        {actionSheetVisible && (
          <Modal transparent animationType='slide' visible={actionSheetVisible}>
            <TouchableWithoutFeedback
              onPress={() => setActionSheetVisible(false)}
            >
              <View style={styles.actionSheetBackground}>
                <View style={styles.actionSheetContainer}>
                  {actionSheetOptions.map((option, index) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => {
                        setActionSheetVisible(false);
                        onActionSheetSelect(index);
                      }}
                    >
                      <Text
                        style={[
                          styles.actionSheetItem,
                          index === cancelButtonIndex && { color: 'red' },
                        ]}
                      >
                        {option}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </TouchableWithoutFeedback>
          </Modal>
        )}
      </SafeAreaView>
    </Provider>
  );
}

// ------------- STYLES -------------
const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '85%',
    paddingLeft: 20,
  },
  headerSearchInput: {
    marginTop: 5,
    paddingHorizontal: 10,
    fontSize: rv(13),
    color: 'black',
    width: '75%',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  groupAvatar: {
    width: 30,
    height: 30,
    borderRadius: 25,
    borderColor: 'white',
    marginHorizontal: 7,
  },
  groupTitle: {
    color: 'black',
    fontSize: rv(13),
    fontWeight: 'bold',
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    width: '15%',
    justifyContent: 'flex-end',
  },
  iconWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: rv(3),
    marginHorizontal: 5,
  },
  menuItemTitle: {
    fontSize: rv(13),
  },

  initialLoadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  daySeparatorContainer: {
    alignItems: 'center',
    marginVertical: 10,
  },
  daySeparatorText: {
    fontWeight: 'bold',
    color: 'gray',
  },

  // Bubble styles
  bubbleContainer: {
    marginVertical: 5,
    padding: 3,
  },
  myBubbleContainer: {
    alignItems: 'flex-end',
  },
  otherBubbleContainer: {
    alignItems: 'flex-start',
  },
  textBubble: {
    padding: 10,
    marginHorizontal: 5,
    maxWidth: wp('70%'),
  },
  messageText: {
    fontSize: rv(13),
  },
  myMessageText: {
    color: '#000',
  },
  otherMessageText: {
    color: '#fff',
  },
  timeText: {
    fontSize: rv(9),
    color: '#000',
    marginTop: 3,
  },
  senderName: {
    fontSize: rv(11),
    color: '#FC8C78', // or any contrasting color
    marginLeft: 5,
    marginBottom: 2,
    fontWeight: 'bold',
  },
  // Quoted
  quotedContainer: {
    backgroundColor: '#696969',
    padding: 8,
    borderRadius: 8,
    maxHeight: 60,
    marginBottom: 4,
  },
  quotedHeader: {
    color: '#9FD0D0',
    fontSize: rv(13),
    fontWeight: 'bold',
  },
  quotedText: {
    color: 'white',
    fontSize: rv(12),
  },

  // Loading older
  loadingOlderContainer: {
    padding: 10,
    alignItems: 'center',
  },

  // Input bar
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#000', // black background
    padding: 5,
  },
  smileyButton: {
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textInputWrapper: {
    flex: 1,
    marginHorizontal: 5,
  },
  textInput: {
    backgroundColor: '#3D3C39',
    borderRadius: 20,
    color: 'white',
    fontSize: rv(13),
    paddingHorizontal: 10,
    paddingVertical: 8,
    maxHeight: 120,
    textAlignVertical: 'top',
  },
  attachmentButton: {
    marginRight: 10,
  },
  sendButton: {},
  joinContainer: {
    padding: 10,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  joinText: {
    fontSize: rv(13),
  },

  // Action Sheet
  actionSheetBackground: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  actionSheetContainer: {
    backgroundColor: 'white',
    padding: 20,
  },
  actionSheetItem: {
    fontSize: 18,
    padding: 10,
    textAlign: 'left',
    color: 'black',
  },
});

// -------------- WRAPPER --------------
export default function GroupChat({ navigation, route }: any) {
  return (
    
      <GroupChatContent navigation={navigation} route={route} />
   
  );
}

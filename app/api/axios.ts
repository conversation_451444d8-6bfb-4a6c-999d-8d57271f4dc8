import axios from 'axios';
import { store } from '../redux';
import { ShowAlert } from 'app/providers/toast';
import { Alert } from 'react-native';
 // Adjust the path as necessary

export const MAIN_URL: string = 'https://pennytot-backend-connectify.up.railway.app';
  

export const Axios = axios.create({
  baseURL: MAIN_URL,
  timeout: 40000, // 40s API call before ending
  headers: {},
});

const AxiosLogger = axios.create({
  baseURL: MAIN_URL,
  timeout: 40000,
  headers: {},
});

export const logUserError = async (payload: any) => {
  const { data } = await AxiosLogger.post('/log/error-log/', payload);
  return data;
};

Axios.interceptors.request.use(function (config: any) {
  const token = store.getState().user?.token;
  config.headers.Authorization = token ? `Bearer ${token}` : '';
  return config;
});

Axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.log(error, 'error from axios');

    let errorMessage = 'Network Error or Server Not Responding';

    if (error.data) {
      errorMessage = error.data.message;
      console.log({ type: 'error', message: errorMessage });
    } else {
      console.log({ type: 'error', message: errorMessage });
    }

    console.log(errorMessage,"ibk1")
    console.log(error.stack,"ibk2")
    // Prepare the payload for logUserError
    const payload = {
      message: errorMessage,
      stack: 'No stack trace',
    };
    console.log(payload,"ibk")

    // Send error log to backend
    try {
      console.log(payload,"logerror")
      await logUserError(payload);
    } catch (logError) {
      console.error('Failed to send error log:', logError);
    }

    // Return a more generic error if error.response is undefined
    return Promise.reject(error?.response ? error.response : 'Network Error');
  },
);

const { get, post, put, delete: destroy } = Axios;
export { get, post, put, destroy };

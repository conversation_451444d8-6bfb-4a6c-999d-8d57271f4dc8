import React, { useState } from 'react';
import { StyleSheet, View, Modal, Text, TouchableOpacity, Image } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { CameraHelper, FileSelectHelper } from 'app/helpers';
import { CustomText } from 'app/components/elements';
import { responsiveValue as rv } from 'app/providers/responsive-value';
const Loading = require('../../assets/loading.gif');
import { ActivityIndicator } from 'react-native';

type SelectImageProps = {
  setImage: any;
  setShow: React.Dispatch<React.SetStateAction<boolean>>;
  show: boolean;
};

export default function SelectImageDialog(props: SelectImageProps) {
  const [loading, setLoading] = useState(false)
  async function selectCamera() {
    let file: string = await CameraHelper.openCamera();
    if (file) {
      props.setImage(file);
      props.setShow(false);
    }
  }

  const selectFromGallery = async()=> {
  
   try{
    let file = await CameraHelper.selectImageFromGallery()
  
    if (file)  {
      setLoading(true)
      console.log('setloading', true)
      props.setImage(file);
      props.setShow(false);
      
    }
   } catch(error){
    console.error('Error selecting image from gallery:', error);

   } finally {
    setLoading(false)
   }
    
  }

  async function selectFromFiles() {
    let file = await FileSelectHelper()
    if (file) {
      props.setImage(file);
      props.setShow(false);
    }
  }

  return (
    <Modal
      transparent={true}
      animationType={'none'}
      visible={props.show}
      onRequestClose={() => {
        props.setShow(false);
      }}
    >
      
      {loading && (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignContent: 'center',
          }}
        >
     
            <Image source={Loading} style={{ width: rv(50), height: rv(50) }} />
       
          {/* Your content here */}
        </View>
      ) }
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          <CustomText
            style={{
              fontSize: rv(13),
            }}
            textType='bold'
          >
            Select Image
          </CustomText>
          <TouchableOpacity
            onPress={selectCamera}
            style={{
              marginTop: rv(15),
            }}
          >
            <CustomText
              style={{
                fontSize: rv(13),
              }}
            >
              Take Photo...
            </CustomText>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              marginTop: rv(13),
            }}
            onPress={selectFromGallery}
          >
            <CustomText
              style={{
                fontSize: rv(13),
              }}
            >
              Choose from Library
            </CustomText>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              marginTop: rv(15),
            }}
            onPress={selectFromFiles}
          >
            <CustomText
              style={{
                fontSize: rv(13),
              }}
            >
              Choose from Files
            </CustomText>
          </TouchableOpacity>
         
          <TouchableOpacity onPress={() => props.setShow(false)}>
            <CustomText
              style={{
                fontSize: rv(15),
                marginTop: rv(15),
              }}
              textType='bold'
            >
              CANCEL
            </CustomText>
          </TouchableOpacity>
    
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#00000040',
  },
  activityIndicatorWrapper: {
    backgroundColor: '#FFFFFF',
    height: wp('55%'),
    width: '80%',
    borderRadius: 10,
    display: 'flex',
    padding: '7%',
    flexDirection: 'column',
  },
});

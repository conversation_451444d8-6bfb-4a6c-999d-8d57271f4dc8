import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import TrackedTouchableOpacity from './TrackTouchable';

type ButtonProps = {
  onPress: () => void;
  label: string;
};
export const Button = (p: ButtonProps) => {
  return (
    <TrackedTouchableOpacity onPress={p.onPress} style={styles.container}>
      <Text style={styles.text}>{p.label}</Text>
    </TrackedTouchableOpacity>
  );
};

export const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1c1c1c',
    paddingVertical: rv(16),
    paddingHorizontal: 32,
    alignSelf: 'center',
    borderRadius: 32,
  },
  text: {
    textAlign: 'center',
    fontSize: rv(13),
    color: '#fff',
    fontFamily: 'bold',
    letterSpacing: 1,
  },
});

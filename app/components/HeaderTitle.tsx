import React, { FunctionComponent } from 'react';
import { TouchableOpacity, View } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { CustomText } from '../components/elements';

type HeaderTitleProps = {
  title: string;
  navigation: any;
  targetScreen?: any;
  convoPage?: string;
  profileScreen?: any;
  userDetails?: {
 
  };
};

const HeaderTitle: FunctionComponent<HeaderTitleProps> = ({
  navigation,
  title,
  targetScreen,
  convoPage,
  profileScreen,
  userDetails
}) => {
  return (
    <View
      style={{ flexDirection: 'row', alignItems: 'center', marginVertical: rv(10), backgroundColor: 'white'}}
    >
      <TouchableOpacity
        onPress={() => {
          console.log('Target Screen:', targetScreen);
          console.log('Convo Page:', convoPage);

          // if(targetScreen){
          //   navigation.navigate(targetScreen)
          // } else 
          if(convoPage){
            console.log('Navigating to convoPage:', convoPage);

            navigation.navigate(convoPage)
          } else if(targetScreen){
            navigation.navigate(targetScreen)
          }
          else if(profileScreen){
            navigation.navigate(profileScreen, {userDetails})
          }
           else{
            navigation.goBack();
          }
         
        }}
        style={{
          marginLeft: 15,
          backgroundColor: 'white',
          padding: 10, // Optional: Adds visual padding around the icon
        }}
        hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }} // Expands the touchable area
      >
        <Ionicons
          name='chevron-back'
          size={22}
          color='black'
          style={{
            marginRight: 10,
          }}
        />
      </TouchableOpacity>
      <CustomText
        style={{
          fontSize: rv(12),
          alignItems: 'center',
          marginTop: rv(3),
          fontFamily: 'medium'

        }}
      >
        {title}
      </CustomText>
    </View>
  );
};

export default HeaderTitle;

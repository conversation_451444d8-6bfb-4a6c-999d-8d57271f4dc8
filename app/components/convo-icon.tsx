import { View } from 'react-native';
import React, { FunctionComponent } from 'react';
import { CustomText } from './elements';
import SVG from "app/providers/svg";
import { useGetUnreadNotifications } from 'app/redux/main/hooks';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import GhanaConvoNotification from 'app/assets/svg/polygon.svg'
import GhanaConvoBuildSVG from '@/app/assets/svg/lucosa-convo-icon.svg'

type ConvoNotificationIconProps = {
  count: number;
};

const ConvoNotificationIcon: FunctionComponent<ConvoNotificationIconProps> = ({
  count,
}) => {
  const { t } = useTranslation();


  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
     <View style={{position: 'relative'}}>
     <GhanaConvoBuildSVG />
     </View>

      {/* <CustomText
        style={{
          fontSize: rv(13),
          marginRight: 3,
          paddingTop: 3,
        }}
      >
        {t("Convos")}
      </CustomText> */}


      {count ? (
        <View
          style={{
           position: 'absolute',
           top: 0,
           bottom: 5
           
          }}
        >
          <GhanaConvoNotification style={{position: 'relative'}} />
          <CustomText
            style={{
              fontSize: rv(11),
              color: 'white',
             top: -17,
              position: 'absolute',
             left: 4,
             
            }}
          >
            {count}

          </CustomText>
        </View>
      ) : null}
    </View>
  );
};

export default ConvoNotificationIcon;

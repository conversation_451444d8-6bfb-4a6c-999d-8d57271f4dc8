import React, { FunctionComponent, useMemo } from 'react';
import { Text, StyleSheet, TextStyle, TextProps } from 'react-native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Hyperlink from 'react-native-hyperlink';
import { Linking } from 'react-native';

interface CustomTextProps extends TextProps {
  style?: TextStyle | TextStyle[];
  textType?: 'light' | 'regular' | 'bold' | 'semi-bold' | 'extra-bold' | 'medium';
  children: any;
  onPress?: () => void
}

export const CustomText: FunctionComponent<CustomTextProps> = ({
  children,
  textType,
  style,
  ...rest
}) => {

  const textStyle = useMemo(() => {
    switch (textType) {
      case 'medium':
        return styles.medium;
      case 'regular':
        return styles.regular;
      case 'bold':
        return styles.bold;
      case 'extra-bold':
        return styles.extraBold;
      case 'semi-bold':
        return styles.semiBold;
      default:
        return styles.regular;
    }
  }, [textType]);

  const passedStyles = useMemo(() => StyleSheet.flatten(style || {}), [style]);

  return (
    <Hyperlink
      linkStyle={{ color: '#0D99FF', textDecorationLine: 'underline' }} // Customize the link style
      onPress={(url) => Linking.openURL(url)} // Handle link clicks
    >
      <Text style={[textStyle, passedStyles]} {...rest}>
        {children}
      </Text>
    </Hyperlink>
  );
}

const styles = StyleSheet.create({
  regular: {
    fontFamily: 'regular'
  },
  medium: {
    fontFamily: 'medium'
  },
  bold: {
    fontFamily: 'bold',
    fontSize: rv(13)
  },
  extraBold: {
    fontFamily: 'italic'
  },
  semiBold: {
    fontFamily: 'semiBold'
  },
  black:{
    fontFamily: 'black'
  },
  italic:{
    fontFamily: 'italic'
  }
});

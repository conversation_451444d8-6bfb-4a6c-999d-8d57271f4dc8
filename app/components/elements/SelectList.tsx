import { useState } from "react";
import { TouchableOpacity, Text, View, Modal, FlatList,  } from "react-native";
import { responsiveValue as rv } from "app/providers/responsive-value";
import Arrow from '@/app/assets/svg/lucosa-arrow.svg'
import countries from "world-countries";
import { Vi } from "react-flags-select";
interface CountryItem {
    label: string;
    value: string;
    flag: string;
  }
  
  interface SelectProps {
    data: any;
    value: any;
    onSelect: (selected: CountryItem) => void;
  }
const  SelectCountry: React.FC<SelectProps> = ({ data, value, onSelect }) => {
    const [modalVisible, setModalVisible] = useState(false);

 
    return (
      <View>
        {/* Display the selected value */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            paddingHorizontal: 20,
            backgroundColor: 'transparent',
            marginBottom: 15,
            borderStyle: 'solid',
            marginTop: 10,
           
            
            borderWidth: 2,
            borderColor: '#F5F5F5',
          
            height: rv(42),
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
          onPress={() => setModalVisible(true)}
        >
         <View style={{display: 'flex', flexDirection: 'row', alignItems: 'center'}}>
         <Text style={{ fontSize: 15, marginRight: 10,  }}>
            {value?.flag}
          </Text>
          <Text style={{ fontSize: 15,  fontFamily: 'regular', color: '#696969', textAlign: 'center' }}>
            {value?.label}
          </Text>
         </View>
          <View ><Arrow width={13}/></View>


        </TouchableOpacity>
  
        {/* Modal for country selection */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <View style={{ width: '80%', backgroundColor: '#fff', borderRadius: 10, padding: 10 }}>
              <Text style={{ fontSize: 15, fontWeight: 'bold', marginBottom: 10 }}>
                Select a Country
              </Text>
  
              <FlatList
                data={data}
                keyExtractor={(item) => item.value}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={{
                      flexDirection: 'row',
                      padding: 10,
                      borderBottomWidth: 1,
                      borderBottomColor: '#ddd',
                      alignItems: 'center',
                    }}
                    onPress={() => {
                      onSelect(item);
                      setModalVisible(false);
                    }}
                  >
                    <Text style={{ fontSize: 15, marginRight: 10 }}>{item.flag}</Text>
                    <Text style={{ fontSize: 14, color: '#696969', }}>{item.label}</Text>
                  </TouchableOpacity>
                )}
                style={{ maxHeight: rv(200) }}
              />
  
              <TouchableOpacity onPress={() => setModalVisible(false)} style={{ marginTop: 20 }}>
                <Text style={{ color: 'blue', textAlign: 'center' }}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </View>
    );
  };
export default SelectCountry;
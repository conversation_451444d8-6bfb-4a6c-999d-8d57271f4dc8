import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import {
  useIsFollowingUser,
  useFollowUser,
  useUnfollowUser,
} from 'app/redux/follow/hooks';
import { CustomText } from './Text';
import FollowSVG from 'app/assets/svg/followUser.svg'
import { responsiveValue as rv } from 'app/providers/responsive-value';
import UnFollowSVG from 'app/assets/svg/unfollowUser.svg'

interface FollowButtonProps {
  userId?: string;
}

const FollowButton = ({ userId }: FollowButtonProps) => {
  const { data, isLoading } = useIsFollowingUser(userId);
  const { mutateAsync: followUser, isLoading: loadings } = useFollowUser();
  const { mutateAsync: unfollowUser, isLoading: loading } = useUnfollowUser();
  const [isFollowing, setIsFollowing] = useState(false);
 

  const { t } = useTranslation();

  const handleToggle =()=>{
    setIsFollowing(prevFollow => !prevFollow)

  }
  const handleFollowToggle = async () => {
    try {
      if (isFollowing) {
        await unfollowUser(userId);
        setIsFollowing(false);
      } else {
        await followUser(userId);
        setIsFollowing(true);
      }
    } catch (error) {
      console.error('Error updating follow status', error);
    }
  };

  useEffect(() => {
    setIsFollowing(data?.data?.isFollowing || false);
  }, [data]);

  // TODO: Fetch initial follow status from API

  return (
    <TouchableOpacity onPress={handleFollowToggle}>
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          // marginLeft: rv(4),
          alignItems: 'center',

          gap: rv(5),
          marginBottom: rv(10)
        }}
      >
        <View
          style={{

            // marginRight: rv(5),

          }}
        >
          {isFollowing ? (
           <UnFollowSVG width={20} /> 
          ) : (
           <FollowSVG width={20} />
          )}
        </View>
       
      
        <CustomText style={styles.buttonText}>
          {isFollowing ? 'Unfollow' : 'Follow'}
        </CustomText>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    width: 85,
    borderRadius: 12,
    borderWidth: 1,
    padding: 4,
    paddingHorizontal: 10,
    borderColor: '#FED830',
    backgroundColor: 'white', // Adjust as needed
    justifyContent: 'center',
    alignItems: 'center',
  },

  buttonText: {
    fontFamily: 'regular',
    fontSize: rv(13),
  },
});

export default FollowButton;

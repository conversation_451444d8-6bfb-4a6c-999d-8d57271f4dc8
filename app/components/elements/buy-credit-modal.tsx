import {
  Platform,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Button,
  ScrollView,
} from 'react-native';
import React, { useEffect, useState, useMemo } from 'react';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import BottomModal from '../bottom-modal';
import { CustomText } from './Text';
import { CustomButton } from './Button';
import { useBuySubscription, useRestoreSubscription } from '../../redux/credit/hook';
import { useTranslation } from 'react-i18next';
import CustomModal from './Modals';
import { useStakeUserPennytots } from 'app/redux/quiz/hook';
import Purchases from 'react-native-purchases';
import { queryClient } from 'app/redux/user/hooks';
import { useNavigation } from '@react-navigation/native';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { showModal, hideModal } from 'app/providers/modals';
import { PurchasesOffering, PurchasesPackage } from 'react-native-purchases';

interface CustomCheckboxProps {
  isChecked: boolean;
  onPress: () => void;
}

// RevenueCat products mapping (if needed in the future)
const revenueCatProducts: any = {
  annual: 'rc_ghana_biannual:rc-ghanaglobal-annual',
  monthly: 'rc_ghana_biannual:rc-ghanaglobal-monthly',
  quarterly: 'rc_ghana_biannual:rc-ghanaglobal-quaterly',
  biannual: 'rc_ghana_biannual:rc-ghanaglobal-biannual',
};

// Define the mapping between our plans and RevenueCat offerings
const PLAN_TO_OFFERING_MAP = {
  diamond: 'threeMonth',
  gold: 'twoMonth',
  silver: 'monthly',
  bronze: 'weekly'
} as const;

type OfferingKey = keyof typeof PLAN_TO_OFFERING_MAP;
type OfferingValue = typeof PLAN_TO_OFFERING_MAP[OfferingKey];

// Define your Android plan mapping – note that for each plan we store a planId
// (which is used as the subscriptionType sent to the backend) and the intended duration.
const androidPlanMap: any = {
  1: { planId: 'diamond', daysToAdd: 30 },
  2: { planId: 'gold', daysToAdd: 30 },
  3: { planId: 'silver', daysToAdd: 30 },
  4: { planId: 'bronze', daysToAdd: 30 },
};

interface BuyCreditModalProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  currentPlan?: string; // Add this prop to receive current plan
}

const BuyCreditModal = ({ isOpen, setIsOpen, currentPlan }: BuyCreditModalProps) => {
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [amount, setAmount] = useState('');
  const { mutateAsync: buy, isLoading: isBuyLoading } = useBuySubscription();
  const { t } = useTranslation();
  const [selectedButton, setSelectedButton] = useState<any>(null);
  const { mutate: stakeUserPennytots } = useStakeUserPennytots();
  const [loading, setLoading] = useState(false);
  const [purchaseInProgress, setPurchaseInProgress] = useState(false);
  const [prices, setPrices] = useState<any>({});
  const [loadingPrices, setLoadingPrices] = useState(true);

  const { mutateAsync: restoreSubscription } = useRestoreSubscription({
    onSuccess: (response) => {
      showModal({
        modalVisible: true,
        title: 'Restore Successful',
        message: response.message || 'Your previous subscription has been restored!',
        setModalVisible: hideModal,
        type: 'success-alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
        navigation
      });
    },
    onError: (error: any) => {
      showModal({
        modalVisible: true,
        title: 'Restore Failed',
        message: error?.message || 'Error restoring purchases. Please try again.',
        setModalVisible: hideModal,
        type: 'error-alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
        navigation
      });
    }
  });

  useEffect(() => {
    async function loadOfferings() {
      try {
        setLoadingPrices(true);
        const offerings = await Purchases.getOfferings();
        if (offerings.current) {
          const newPrices: any = {};
          // Map the offerings to our plan types
          if (offerings.current.threeMonth) {
            newPrices.diamond = offerings.current.threeMonth.product.priceString;
          }
          if (offerings.current.twoMonth) {
            newPrices.gold = offerings.current.twoMonth.product.priceString;
          }
          if (offerings.current.monthly) {
            newPrices.silver = offerings.current.monthly.product.priceString;
          }
          if (offerings.current.weekly) {
            newPrices.bronze = offerings.current.weekly.product.priceString;
          }
          setPrices(newPrices);
        }
      } catch (error) {
        console.log('Error loading prices:', error);
      } finally {
        setLoadingPrices(false);
      }
    }
    
    loadOfferings();
  }, []);

  const getPriceForPlan = (plan: string) => {
    return prices[plan] || '';
  };

  // Add function to check if a plan is disabled
  const isPlanDisabled = (planId: string) => {
    return currentPlan === planId;
  };

  // Add function to get button opacity based on disabled state
  const getButtonOpacity = (planId: string) => {
    return isPlanDisabled(planId) ? 0.5 : 1;
  };

  // Update handlePress to check for disabled state
  const handlePress = (button: any) => {
    const plan = androidPlanMap[button];
    if (isPlanDisabled(plan.planId)) {
      showModal({
        modalVisible: true,
        title: 'Current Plan',
        message: 'You are already subscribed to this plan. Please select a different plan to upgrade or downgrade.',
        setModalVisible: hideModal,
        type: 'error-alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
        navigation
      });
      return;
    }
    setSelectedButton(button);
    setAmount(plan.planId);
  };

  const credits: string = useMemo(() => {
    if (!amount) {
      return '0';
    }
    return (parseInt(amount) * 1).toString();
  }, [amount]);

  // Restore Purchases Function
  async function restorePurchases() {
    try {
      setLoading(true);
      
      // 1. First restore purchases through RevenueCat
      const restoredInfo = await Purchases.restorePurchases();
      console.log('Restored Customer Info:', restoredInfo);
      
      if (restoredInfo.entitlements.active && Object.keys(restoredInfo.entitlements.active).length > 0) {
        // 2. If RevenueCat restore successful, sync with our backend
        await restoreSubscription(restoredInfo);
      } else {
        showModal({
          modalVisible: true,
          title: 'No Purchases Found',
          message: 'No active subscriptions were found to restore.',
          setModalVisible: hideModal,
          type: 'error-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation
        });
      }
    } catch (error: any) {
      console.log('Restore error:', error);
      showModal({
        modalVisible: true,
        title: 'Restore Failed',
        message: error?.message || 'Error restoring purchases. Please try again.',
        setModalVisible: hideModal,
        type: 'error-alert',
        handleConfirm: hideModal,
        handleAlert: hideModal,
        navigation
      });
    } finally {
      setLoading(false);
    }
  }

  // Add confirmation for plan changes
  const handlePlanChange = async () => {
    if (!amount) return;

    // Show warning when downgrading
    const currentPlanRank = getPlanRank(currentPlan);
    const newPlanRank = getPlanRank(amount);
    
    console.log('Plan change - Current rank:', currentPlanRank, 'New rank:', newPlanRank);
    
    if (currentPlanRank > newPlanRank) {
      console.log('Showing downgrade confirmation modal');
      // Close the buy credit modal first to prevent modal stacking
      setIsOpen(false);
      
      // Small delay before showing the confirmation modal
      setTimeout(() => {
        showModal({
          modalVisible: true,
          title: 'Confirm Downgrade',
          message: 'You are about to downgrade your subscription. This may reduce your benefits immediately. Do you want to continue?',
          setModalVisible: (visible) => {
            console.log('Setting modal visible:', visible);
            if (!visible) {
              // Reopen the buy credit modal if user cancels
              setIsOpen(true);
            }
            hideModal();
          },
          type: 'error-alert',
          handleConfirm: () => {
            console.log('Confirming downgrade');
            hideModal();
            setTimeout(() => {
              proceed();
            }, 300);
          },
          handleAlert: () => {
            console.log('Canceling downgrade');
            hideModal();
            // Reopen the buy credit modal
            setIsOpen(true);
          },
          navigation
        });
      }, 300);
      return;
    }

    proceed();
  };

  // Helper function to get plan rank for comparison
  const getPlanRank = (plan?: string) => {
    const ranks = {
      'diamond': 4,
      'gold': 3,
      'silver': 2,
      'bronze': 1
    };
    return plan ? ranks[plan as keyof typeof ranks] || 0 : 0;
  };

  async function proceed() {
    try {
      setLoading(true);
      setPurchaseInProgress(true);
      console.log('Starting purchase process with amount:', amount);

      if (!amount) {
        setLoading(false);
        setPurchaseInProgress(false);
        showModal({
          modalVisible: true,
          title: 'Error',
          message: 'Please select a sponsorship plan.',
          setModalVisible: hideModal,
          type: 'error-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation
        });
        return;
      }

      console.log('Fetching offerings from RevenueCat...');
      const offerings = await Purchases.getOfferings();
      console.log('Received offerings:', offerings);

      if (!offerings.current) {
        console.log('No current offerings found');
        setLoading(false);
        setPurchaseInProgress(false);
        showModal({
          modalVisible: true,
          title: 'Error',
          message: 'No subscription offerings available at this time.',
          setModalVisible: hideModal,
          type: 'error-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation
        });
        return;
      }

      let packageToPurchase;
      console.log('Selecting package for plan:', amount);
      
      switch (amount) {
        case 'diamond':
          packageToPurchase = offerings.current.threeMonth;
          break;
        case 'gold':
          packageToPurchase = offerings.current.twoMonth;
          break;
        case 'silver':
          packageToPurchase = offerings.current.monthly;
          break;
        case 'bronze':
          packageToPurchase = offerings.current.weekly || offerings.current.monthly;
          break;
        default:
          console.log('Invalid plan selected:', amount);
          setLoading(false);
          setPurchaseInProgress(false);
          showModal({
            modalVisible: true,
            title: 'Error',
            message: 'Invalid sponsorship plan selected.',
            setModalVisible: hideModal,
            type: 'error-alert',
            handleConfirm: hideModal,
            handleAlert: hideModal,
            navigation
          });
          return;
      }

      console.log('Selected package:', packageToPurchase);

      if (!packageToPurchase) {
        console.log('Package not available for plan:', amount);
        setLoading(false);
        setPurchaseInProgress(false);
        showModal({
          modalVisible: true,
          title: 'Error',
          message: 'Selected package is not available.',
          setModalVisible: hideModal,
          type: 'error-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation
        });
        return;
      }

      // Purchase via RevenueCat
      console.log('Initiating purchase with RevenueCat...');
      const purchaseResult = await Purchases.purchasePackage(packageToPurchase);
      console.log('Purchase Result:', purchaseResult);
      
      // Only proceed with backend update if purchase was successful
      console.log('Updating backend with subscription type:', amount);
      const payload = { subscriptionType: amount };
      await buy(payload);
      await queryClient.invalidateQueries(['subscription']);
      
      setIsOpen(false);
      setTimeout(() => {
        showModal({
          modalVisible: true,
          title: 'Success',
          message: 'Your subscription has been activated successfully!',
          setModalVisible: hideModal,
          type: 'success-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation
        });
      }, 300);
    } catch (error: any) {
      console.log('Purchase error:', error);
      setTimeout(() => {
        showModal({
          modalVisible: true,
          title: 'Purchase Failed',
          message: error?.message || 'There was an error processing your subscription. Please try again.',
          setModalVisible: hideModal,
          type: 'error-alert',
          handleConfirm: hideModal,
          handleAlert: hideModal,
          navigation
        });
      }, 300);
    } finally {
      setLoading(false);
      setPurchaseInProgress(false);
      setSelectedButton(null);
      setAmount('');
    }
  }

  const handleConfirm = async () => {
    setModalVisible(false);
  };

  const CustomCheckbox: React.FC<CustomCheckboxProps> = ({ isChecked, onPress }) => (
    <TouchableOpacity onPress={onPress} style={styles.checkboxContainer}>
      <View style={[styles.checkbox, isChecked && styles.checkedCheckbox]}>
        {isChecked && <View style={styles.innerCircle} />}
      </View>
    </TouchableOpacity>
  );

  if (loading || purchaseInProgress || isBuyLoading) {
    return (
      <BottomModal isOpen={isOpen} setIsOpen={setIsOpen} height={hp(90)}>
        <View style={{ flex: 1, padding: rv(10), paddingHorizontal: rv(20), flexDirection: 'column', backgroundColor: 'white', justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color="#163E23" />
          <CustomText style={{ marginTop: 10, color: '#4F4F4F', fontSize: rv(14), fontFamily: 'medium', textAlign: 'center' }}>
            {purchaseInProgress ? 'Processing purchase...' : 'Loading...'}
          </CustomText>
        </View>
      </BottomModal>
    );
  }

  return (
    <BottomModal 
      isOpen={isOpen} 
      setIsOpen={(value: boolean) => {
        console.log('Setting BuyCreditModal visibility:', value);
        setIsOpen(value);
        // If we're closing the modal, ensure we clean up any state
        if (!value) {
          setSelectedButton(null);
          setAmount('');
          setPurchaseInProgress(false);
          setLoading(false);
        }
      }} 
      height={hp(90)}
    >
      <CustomModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        type="minimum"
        handleConfirm={handleConfirm}
        navigation={navigation}
      />
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <View style={{ padding: rv(10), paddingHorizontal: rv(20), flexDirection: 'column', backgroundColor: 'white' }}>
          <CustomText style={{ color: '#797978', fontSize: 18, marginBottom: 10 }}>
            Sponsorship Plans
          </CustomText>
          {currentPlan && (
            <CustomText style={[styles.text, { color: '#749B6C', marginBottom: 10 }]}>
              Current Plan: {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)}
            </CustomText>
          )}
          <CustomText style={[styles.text, { flexWrap: 'wrap', marginBottom: rv(10) }]}>
            Sponsors receive recognition for the duration of each plan
          </CustomText>

          {loadingPrices ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#163E23" />
              <CustomText style={styles.loadingText}>Loading plans...</CustomText>
            </View>
          ) : (
            <View style={{ flexDirection: 'column', marginVertical: rv(5) }}>
              {/* Diamond Sponsorship */}
              <TouchableOpacity
                style={[
                  styles.button,
                  { 
                    borderColor: selectedButton === 1 ? '#749B6C' : '#F0F2F5',
                    opacity: getButtonOpacity('diamond')
                  }
                ]}
                onPress={() => handlePress(1)}
                disabled={isPlanDisabled('diamond')}
              >
                <View style={styles.row}>
                  <CustomCheckbox
                    isChecked={selectedButton === 1}
                    onPress={() => handlePress(1)}
                  />
                  <View style={styles.innerContainer}>
                    <Text style={styles.text}>Diamond Sponsorship</Text>
                    <Text numberOfLines={3} style={styles.planDescription}>
                      Diamond - 30-day public recognition and 30-day uninterrupted access
                    </Text>
                    {isPlanDisabled('diamond') && (
                      <Text style={styles.currentPlanText}>Current Plan</Text>
                    )}
                  </View>
                  <View style={styles.priceContainer}>
                    <Text style={styles.price}>{getPriceForPlan('diamond')}</Text>
                  </View>
                </View>
              </TouchableOpacity>

              {/* Gold Sponsorship */}
              <TouchableOpacity
                style={[
                  styles.button,
                  { 
                    borderColor: selectedButton === 2 ? '#749B6C' : '#F0F2F5',
                    opacity: getButtonOpacity('gold')
                  }
                ]}
                onPress={() => handlePress(2)}
                disabled={isPlanDisabled('gold')}
              >
                <View style={styles.row}>
                  <CustomCheckbox
                    isChecked={selectedButton === 2}
                    onPress={() => handlePress(2)}
                  />
                  <View style={styles.innerContainer}>
                    <Text style={styles.text}>Gold Sponsorship</Text>
                    <Text numberOfLines={3} style={styles.planDescription}>
                      Gold - 20-day public recognition and 30-day uninterrupted access
                    </Text>
                    {isPlanDisabled('gold') && (
                      <Text style={styles.currentPlanText}>Current Plan</Text>
                    )}
                  </View>
                  <View style={styles.priceContainer}>
                    <Text style={styles.price}>{getPriceForPlan('gold')}</Text>
                  </View>
                </View>
              </TouchableOpacity>

              {/* Silver Sponsorship */}
              <TouchableOpacity
                style={[
                  styles.button,
                  { 
                    borderColor: selectedButton === 3 ? '#749B6C' : '#F0F2F5',
                    opacity: getButtonOpacity('silver')
                  }
                ]}
                onPress={() => handlePress(3)}
                disabled={isPlanDisabled('silver')}
              >
                <View style={styles.row}>
                  <CustomCheckbox
                    isChecked={selectedButton === 3}
                    onPress={() => handlePress(3)}
                  />
                  <View style={styles.innerContainer}>
                    <Text style={styles.text}>Silver Sponsorship</Text>
                    <Text numberOfLines={3} style={styles.planDescription}>
                      Silver - 10-day public recognition and 30-day uninterrupted access
                    </Text>
                    {isPlanDisabled('silver') && (
                      <Text style={styles.currentPlanText}>Current Plan</Text>
                    )}
                  </View>
                  <View style={styles.priceContainer}>
                    <Text style={styles.price}>{getPriceForPlan('silver')}</Text>
                  </View>
                </View>
              </TouchableOpacity>

              {/* Bronze Sponsorship */}
              <TouchableOpacity
                style={[
                  styles.button,
                  { 
                    borderColor: selectedButton === 4 ? '#749B6C' : '#F0F2F5',
                    opacity: getButtonOpacity('bronze')
                  }
                ]}
                onPress={() => handlePress(4)}
                disabled={isPlanDisabled('bronze')}
              >
                <View style={styles.row}>
                  <CustomCheckbox
                    isChecked={selectedButton === 4}
                    onPress={() => handlePress(4)}
                  />
                  <View style={styles.innerContainer}>
                    <Text style={styles.text}>Bronze Sponsorship</Text>
                    <Text numberOfLines={3} style={styles.planDescription}>
                      Bronze - 5-day public recognition and 30-day uninterrupted access
                    </Text>
                    {isPlanDisabled('bronze') && (
                      <Text style={styles.currentPlanText}>Current Plan</Text>
                    )}
                  </View>
                  <View style={styles.priceContainer}>
                    <Text style={styles.price}>{getPriceForPlan('bronze')}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          )}

          {selectedButton && (
            <CustomButton
              disabled={!selectedButton || loadingPrices}
              label={loadingPrices ? 'Loading...' : t('Credits_proceedBtn')}
              onPress={handlePlanChange}
              buttonTheme="primary"
              style={{ marginTop: rv(10), backgroundColor: (selectedButton && !loadingPrices) ? '#163E23' : '#FCFCFC' }}
              loading={loading || isBuyLoading}
            />
          )}

          <TouchableOpacity 
            onPress={restorePurchases}
            style={styles.restoreContainer}
          >
            <CustomText style={styles.restoreText}>
              Restore previous purchases
            </CustomText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </BottomModal>
  );
};

const styles = StyleSheet.create({
  container: {},
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    flexWrap: 'nowrap',
  },
  button: {
    borderWidth: rv(2),
    borderRadius: rv(8),
    paddingVertical: rv(12),
    paddingHorizontal: rv(12),
    marginVertical: rv(4),
    flexDirection: 'column',
  },
  innerContainer: {
    flexDirection: 'column',
    width: '55%',
  },
  text: {
    fontSize: rv(12),
    fontFamily: 'medium',
  },
  price: {
    fontSize: rv(11),
    fontFamily: 'bold',
    color: '#696969',
    textAlign: 'right',
    minWidth: rv(65),
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginLeft: 'auto',
    paddingRight: rv(2),
    width: '30%',
  },
  currencySymbol: {
    fontSize: rv(9),
    color: '#98A2B3',
    marginRight: 1,
  },
  amountText: {
    fontFamily: 'regular',
    fontSize: rv(10),
    textAlign: 'center',
  },
  checkboxContainer: {
    marginRight: rv(6),
    width: '7%',
  },
  checkbox: {
    width: rv(18),
    height: rv(18),
    borderWidth: 2,
    borderColor: '#F0F2F5',
    borderRadius: rv(9),
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedCheckbox: {
    borderColor: '#749B6C',
  },
  innerCircle: {
    width: 10,
    height: 10,
    backgroundColor: '#749B6C',
    borderRadius: 5,
  },
  restoreContainer: {
    alignItems: 'center',
    marginTop: rv(5),
    marginBottom: rv(1),
  },
  restoreText: {
    fontSize: rv(11),
    color: '#98A2B3',
    textDecorationLine: 'underline',
    fontFamily: 'regular',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: rv(20),
  },
  loadingText: {
    marginTop: rv(10),
    color: '#98A2B3',
    fontSize: rv(14),
    fontFamily: 'regular',
  },
  planDescription: {
    fontSize: rv(9),
    color: '#98A2B3',
    marginTop: rv(2),
    width: '100%',
    fontFamily: 'medium',
    flexWrap: 'wrap',
    lineHeight: rv(12),
    paddingRight: rv(4),
  },
  currentPlanText: {
    fontSize: rv(10),
    color: '#749B6C',
    marginTop: rv(2),
    fontFamily: 'medium',
  },
});

export default BuyCreditModal;

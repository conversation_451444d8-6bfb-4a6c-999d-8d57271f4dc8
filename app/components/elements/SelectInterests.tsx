import React, { FunctionComponent, useEffect, useState } from 'react';
import { TouchableOpacity, View, Text, FlatList } from 'react-native';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { Axios } from 'app/api/axios';
import CustomCheckbox from './CustomCheckBox';
import Ionicons from '@expo/vector-icons/Ionicons';
import { CustomText } from './Text';

type SelectInterestsProps =
  | {
      multiple: boolean;
      setInterests: (interests: string[]) => void;
      interests: string[];
      interest?: string;
      setInterest?: any;
    }
  | {
      multiple?: never;
      setInterests?: any;
      interests?: string[];
      interest: string;
      setInterest: (interest: string) => void;
    };

interface IInterestButtonProps {
  item: IInterest;
  onDelete: () => void;
}

const InterestButton: FunctionComponent<IInterestButtonProps> = ({
  item,
  onDelete,
}) => {
  return (
    <View style={{ flexDirection: 'row', alignItems: 'center', margin: rv(3) }}>
      <TouchableOpacity
        onPress={onDelete}
        style={{
          backgroundColor: 'transparent',
          borderRadius: rv(32),
          padding: rv(10),
          paddingHorizontal: rv(20),
          flexDirection: 'row',
          alignItems: 'center',
          borderWidth: 1,
          borderColor: 'grey',
        }}
      >
        <Text
          style={{
            color: 'black',
            fontSize: rv(10),
            marginRight: rv(10),
            fontFamily: 'medium',
          }}
        >
          {item.name}
        </Text>
        <Text style={{ color: 'black', fontSize: rv(10) }}>X</Text>
      </TouchableOpacity>
    </View>
  );
};

const SelectInterests: FunctionComponent<SelectInterestsProps> = ({
  setInterests,
  interests = [],
  setInterest,
  interest,
  multiple,
}) => {
  const [allInterests, setAllInterests] = useState<IInterest[]>([]);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);

  useEffect(() => {
    Axios({
      method: 'get',
      url: '/tags/list',
    }).then((response: any) => {
      let interestData = JSON.parse(JSON.stringify(response.data.data));
      interestData.sort((a: IInterest, b: IInterest) => {
        if (a.name.toLowerCase() === 'business') return -1;
        if (b.name.toLowerCase() === 'business') return 1;
        return 0;
      });
      setAllInterests(interestData);
    });
  }, []);

  const handleSelectInterest = (value: string) => {
    if (multiple) {
      const newSelectedItems = interests.includes(value)
        ? interests.filter((item) => item !== value)
        : [...interests, value];
      setInterests(newSelectedItems);
    } else {
      setInterest(value);
      setDropdownVisible(false);
    }
  };

  const handleRemoveInterest = (id: string) => {
    if (multiple) {
      setInterests(interests.filter((item) => item !== id));
    } else {
      setInterest('');
    }
  };

  return (
    <View style={{ padding: rv(10) }}>
      {/* Render Selected Interests */}
      <View
      // style={{
      //   borderWidth: multiple || interest ? 1 : 0,
      //   borderColor: 'grey',
      //   borderRadius: rv(6),
      //   padding: rv(5),
      //   marginVertical: rv(10),
      // }}
      >
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: rv(5),
          }}
        >
          {multiple
            ? interests.map((id) => {
                const item = allInterests.find(
                  (interest) => interest._id === id
                );
                return item ? (
                  <InterestButton
                    key={item._id}
                    item={item}
                    onDelete={() => handleRemoveInterest(item._id)}
                  />
                ) : null;
              })
            : interest &&
              allInterests.find((item) => item._id === interest) && (
                <InterestButton
                  key={interest}
                  item={allInterests.find((item) => item._id === interest)!}
                  onDelete={() => handleRemoveInterest(interest)}
                />
              )}
        </View>
      </View>

      {/* Dropdown button for selecting an interest */}
      <TouchableOpacity
        onPress={() => setDropdownVisible(!dropdownVisible)}
        style={{
          borderColor: '#D0D5DD',
          borderRadius: 6,
          borderWidth: 1,
          padding: rv(10),

          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <Text
          style={{
            fontSize: rv(12),
            flex: 1,
            fontFamily: 'medium',
            color: '#110F05',
          }}
        >
          {interest
            ? allInterests.find((item) => item._id === interest)?.name
            : 'Click to select your interests'}
        </Text>
        <Ionicons name={'chevron-down'} size={rv(14)} />
      </TouchableOpacity>

      {/* Dropdown List */}
      {dropdownVisible && (
        <View
          style={{
            borderRadius: rv(6),
            borderColor: '#D0D5DD',
            borderWidth: 1,
            marginTop: rv(10),
            maxHeight: rv(200),
          }}
        >
          <FlatList
            data={allInterests}
            keyExtractor={(item) => item._id}
            nestedScrollEnabled
            renderItem={({ item }) => (
              <TouchableOpacity
                onPress={() => handleSelectInterest(item._id)}
                style={{
                  padding: rv(10),
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <CustomCheckbox
                  value={
                    multiple
                      ? interests.includes(item._id)
                      : interest === item._id
                  }
                  onValueChange={() => handleSelectInterest(item._id)}
                  color='green'
                  size={24}
                />
                <CustomText
                  style={{
                    fontSize: rv(13),
                    color: '#808080',
                    fontFamily: 'medium',
                    marginLeft: rv(10),
                  }}
                  textType='medium'
                >
                  {item.name}
                </CustomText>
              </TouchableOpacity>
            )}
            style={{ maxHeight: rv(200) }}
          />
        </View>
      )}
    </View>
  );
};

export default SelectInterests;

import { View, Text, Image, TouchableOpacity, KeyboardAvoidingView } from 'react-native';
import React from 'react';
import GallerySVG from 'app/assets/svg/media/gallery.svg';
import GallerySVG2 from 'app/assets/svg/media/gallery3.svg';
import CameraSVG from 'app/assets/svg/media/camera.svg';
import DocumentSVG from 'app/assets/svg/media/document.svg';
import ContactSVG from 'app/assets/svg/media/contact.svg';
import AudioSVG from 'app/assets/svg/media/audio.svg';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';

export default function SelectAttachment(props:any) {
  const { t } = useTranslation();

  if (!props.show) {
    return null;
  }

  // Helper function to handle option press
  const handleOptionPress = (type:any) => {
    props.openShareAttachment(type);
  };

  return (
    <View
      style={{
        backgroundColor: 'white',
        justifyContent: 'space-between',
        paddingHorizontal: rv(14),
        alignItems: 'center',
        flexDirection: 'row',
        // Removed the hardcoded height
        // The container will now size based on its children.
        paddingVertical: rv(14),
      }}
    >
      <TouchableOpacity
        onPress={() => handleOptionPress('camera')}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <CameraSVG width={47} />
        <Text
          style={{
            paddingTop: 4,
            color: 'rgba(0, 0, 0, 0.31)',
            fontFamily: 'medium',
            fontSize: rv(12),
          }}
        >
          Camera {props.text}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => handleOptionPress('image')}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <GallerySVG width={47} />
        <Text
          style={{
            paddingTop: 4,
            color: 'rgba(0, 0, 0, 0.31)',
            fontFamily: 'medium',
            fontSize: rv(12),
          }}
        >
          {t('Chats_att_images')}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => handleOptionPress('video')}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <GallerySVG2 width={47} />
        <Text
          style={{
            paddingTop: 4,
            color: 'rgba(0, 0, 0, 0.31)',
            fontFamily: 'medium',
            fontSize: rv(12),
          }}
        >
          Video
        </Text>
      </TouchableOpacity>

      {props.source === 'group' ? (
        <TouchableOpacity
          onPress={() => {
            props.setShowSelectContact(true);
          }}
          style={{
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <ContactSVG width={47} />
          <Text
            style={{
              fontSize: rv(12),
              paddingTop: 4,
              color: 'rgba(0, 0, 0, 0.31)',
              fontFamily: 'medium'

              
            }}
          >
            {t('Chats_att_contact')}
          </Text>
        </TouchableOpacity>
      ) : null}

      <TouchableOpacity
        onPress={() => handleOptionPress('document')}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <DocumentSVG width={47} />
        <Text
          style={{
            paddingTop: 4,
            color: 'rgba(0, 0, 0, 0.31)',
            fontFamily: 'medium',
            fontSize: rv(12),

          }}
        >
          Document
        </Text>
      </TouchableOpacity>
    </View>
  );
}

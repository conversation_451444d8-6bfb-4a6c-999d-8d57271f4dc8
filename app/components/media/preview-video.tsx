import React, { useRef, useState } from 'react';
import {
  View,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  Alert,
  useWindowDimensions,
} from 'react-native';
import { Video } from 'expo-av';
import Ionicons from 'react-native-vector-icons/Ionicons';

export default function PreviewVideo({ videoLink, width, height, style }:any) {
  const video = useRef(null);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [aspectRatio, setAspectRatio] = useState(16 / 9); // Default aspect ratio
  const windowDimensions = useWindowDimensions();

  const handleReadyForDisplay = (event) => {
    const { naturalSize } = event;
    if (naturalSize && naturalSize.width > 0 && naturalSize.height > 0) {
      const calculatedAspectRatio = naturalSize.width / naturalSize.height;
      setAspectRatio(calculatedAspectRatio);
    }
    setIsVideoReady(true);
  };

  const handlePlaybackStatusUpdate = (status) => {
    if (!status.isLoaded) {
      if (status.error) {
        console.error(`Encountered a fatal error during playback: ${status.error}`);
      }
    } else {
      if (status.didJustFinish) {
        // Video finished playing
        setIsPlaying(false);
      } else {
        setIsPlaying(status.isPlaying);
      }
    }
  };

  const handleVideoError = (error) => {
    console.error('Video error:', error);
    Alert.alert('Error', 'Failed to load the video.');
  };

  const handlePlayPress = async () => {
    if (video.current) {
      const status = await video.current.getStatusAsync();
      if (status.positionMillis === status.durationMillis) {
        await video.current.replayAsync();
      } else {
        await video.current.playAsync();
      }
    }
  };

  // Determine the width and height
  const videoWidth = width || windowDimensions.width;
  const videoHeight = height || videoWidth / aspectRatio;

  return (
    <View style={[styles.container, style]}>
      {!isVideoReady && (
        <ActivityIndicator
          size="large"
          color="#ffffff"
          style={styles.loadingIndicator}
        />
      )}
      {videoLink && (
        <View>
          <Video
            ref={video}
            style={[
              styles.video,
              {
                width: videoWidth,
                height: videoHeight,
                opacity: isVideoReady ? 1 : 0, // Hide video until ready
              },
            ]}
            source={{ uri: videoLink }}
            resizeMode="contain"
            onReadyForDisplay={handleReadyForDisplay}
            onError={handleVideoError}
            onPlaybackStatusUpdate={handlePlaybackStatusUpdate}
            useNativeControls
          />

          {!isPlaying && isVideoReady && (
            <TouchableOpacity
              style={styles.playButtonContainer}
              onPress={handlePlayPress}
            >
              <Ionicons name="play-circle" size={70} color="white" />
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'black', // Ensure background is set appropriately
    alignItems: 'center',
    justifyContent: 'center',
  },
  video: {
    backgroundColor: 'black',
  },
  loadingIndicator: {
    position: 'absolute',
    alignSelf: 'center',
  },
  playButtonContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -35 }, { translateY: -35 }], // Center the play button
    width: 70,
    height: 70,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from 'react-native-popup-menu';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import { Image } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import ReportDialog from 'app/components/dialogs/report';
import Document from 'app/components/attachments/Document';
import Audio from 'app/components/attachments/Audio';
import Video from 'app/components/attachments/Video';
import { CustomText } from '../elements/Text';
import { useSelector } from 'react-redux';
import { userId, userToken } from 'app/redux/user/reducer';
import Avatar from 'app/components/elements/Avater';
import { main, topic } from 'app/api';
import { ShowAlert } from 'app/providers/toast';
import { triggerStyles } from 'app/assets/styles/MenuStyles';
import CommentSVG from '../svgReactComponent/CommentSVG';
import { timeAgo } from 'app/helpers/time-ago';
import MenuSVG from 'app/assets/svg/menu.svg';
import MuteGroupSVG from 'app/assets/svg/muteUser.svg';
// import FlagSVG from 'app/assets/svg/flagasInapp.svg';
import EditTopicSVG from 'app/assets/svg/edit-3.svg';
import UnMuteSVG from 'app/assets/svg/unmuteUser.svg';
import { showModal, hideModal } from 'app/providers/modals';
import ThumbSVG from '../svgReactComponent/ThumSVG';
import FlagSVG from '@/app/assets/svg/flagAsInUser.svg';

import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import React, {
  useContext,
  useState,
  useEffect,
  useRef,
  FunctionComponent,
} from 'react';
import FollowButton from '../elements/FollowButton';
import { IsLoggedIn, userAuthInfo } from 'app/redux/user/reducer';
import { logout } from 'app/redux/user/hooks';
import { useTranslation } from 'react-i18next';
import { Axios } from 'app/api/axios';
import { DeleteSVG } from 'app/providers/svg/loader';
import { useSingleTopic } from 'app/redux/topic/hooks';
export type TopicProps = {
  navigation: any;
  item?: any;
  convo?: boolean;
  onToggleMuted?: () => void; // Function that handles the mute/unmute logic
  muted?: boolean;
  refreshFunction?: () => void;
  route?: any;
};

export const TopicCard: FunctionComponent<TopicProps> = ({
  item,
  onToggleMuted,
  navigation,
  convo = false,
  refreshFunction,
}) => {
  console.log(item, 'item');
  const isLoggedIn = useSelector(IsLoggedIn);
  const Id = useSelector(userId);
  const token = useSelector(userToken);
  const userAuth = useSelector(userAuthInfo);
  const { t } = useTranslation();

  const myId = useSelector(userId);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});
  const { SlideInMenu } = renderers;
  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);
  const [isMuted, setIsMuted] = useState<boolean>(item.muted);
  const blurhash =
    '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

  const handleToggleMuted = () => {
    setIsMuted((prevMuted) => !prevMuted);
    if (onToggleMuted) {
      onToggleMuted();
    }
  };

  useEffect(() => {
    if (item.isLiked && item.likes) {
      setIsLiked(item.isLiked);
      setNumLikes(item.likes);
    }

    console.log(JSON.stringify(item.image), '==see topic content');
  }, [item]);

  function handleLike() {
    setIsLiked(!isLiked);
    setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);

    topic
      .likeTopic(item._id)
      .then((data) => {
        refreshFunction!();
      })
      .catch((err) => {
        setIsLiked(!isLiked);
        setNumLikes(isLiked ? numLikes + 1 : numLikes - 1);
      });
  }

  async function removeFromConversation() {
    topic.removeFromConvos(item._id).then((data) => {
      console.log(data);
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'This topic has now been removed from your convos',
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }

  async function deleteFromTopic() {
    topic.deleteTopic(item._id).then((data) => {
      console.log(data);
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Topic deleted',
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }

  function muteNotification() {
    console.log('got here');
    let payload = {
      contentId: item._id,
      type: 'topic',
      action: item.muted ? 'unmute' : 'mute',
    };
    console.log(payload);
    console.log(item, 'itemss');
    main.muteNotifications(payload).then((data) => {
      console.log(data);

      const message = item.muted
        ? 'Topic Unmuted!'
        : 'Topic muted. You will no longer receive notifications from this topic';
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Muted Successfully',
      // });
      refreshFunction!();
    });
  }

  function openReportDetails(item: any, type: string) {
    setReportDetails({ data: { _id: item._id }, type });
    setShowReportDialog(true);
  }

  return (
    <TouchableOpacity
      onPress={() =>
        navigation.navigate('Common', {
          screen: 'view-topic',
          params: {
            postid: item._id,
          },
        })
      }
      style={{
        width: '100%',
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        backgroundColor: 'white',
        paddingVertical: rv(8),
        // borderBottomColor: '#EBEBEB',
        // borderBottomWidth: 1,
      }}
    >
      <ReportDialog
        show={showReportDialog}
        setShow={setShowReportDialog}
        reportDetails={reportDetails}
      />

      <View style={{ width: '20%', justifyContent: 'flex-end' }}>
        <TouchableOpacity
          style={{
            alignItems: 'flex-end',
          }}
          onPress={() => {
            if (item?.userId?._id != myId) {
              navigation.navigate('Common', {
                screen: 'private-chat',
                params: {
                  userDetails: item?.userId,
                },
              });
            }
          }}
        >
          <View style={{ marginBottom: 10, marginRight: 15 }}>
            <Avatar source={item?.userId?.profile_picture} size={47} />
          </View>
        </TouchableOpacity>
      </View>
      <View style={{ width: '80%', flexDirection: 'column' }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginRight: wp('5%'),
          }}
        >
          <View
            style={{
              flexDirection: 'row',
            }}
          >
            <TouchableOpacity
              style={{
                width: '85%',

                justifyContent: 'center',
              }}
              onPress={() => {
                if (item?.userId?._id != myId) {
                  navigation.navigate('Common', {
                    screen: 'private-chat',
                    params: {
                      userDetails: item?.userId,
                    },
                  });
                }
              }}
            >
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={1}
                ellipsizeMode='tail'
                style={{
                  color: '#000000',
                  fontSize: rv(14),
                  flexWrap: 'nowrap',
                  fontFamily: 'bold',
                }}
                // textType='bold'
              >
                {`${item?.userId?.first_name} ${item?.userId?.last_name}`.slice(
                  0,
                  35
                ) +
                  (item?.userId?.first_name?.length +
                    item?.userId?.last_name?.length >
                  35
                    ? '...'
                    : '')}
              </CustomText>
            </TouchableOpacity>

            <TouchableOpacity style={{}}></TouchableOpacity>
          </View>

          <View
            style={{
              alignItems: 'center',
              justifyContent: 'flex-end',
              flexWrap: 'nowrap',
              flexDirection: 'row',
            }}
          >
            <Menu renderer={SlideInMenu}>
              <MenuTrigger>
                <View
                  style={{
                    alignItems: 'flex-end',
                    flexDirection: 'row',
                    padding: rv(3),
                  }}
                >
                  <MenuSVG width={15} height={15} />
                </View>
              </MenuTrigger>

              <MenuOptions
                customStyles={{
                  optionText: [styles.text],
                  optionsContainer: [
                    {
                      borderTopLeftRadius: 15,
                      borderTopRightRadius: 15,
                    },
                  ],
                }}
              >
                <View
                  style={{
                    margin: 5,
                    flexDirection: 'column',
                    marginVertical: 10,
                    padding: 15,
                  }}
                >
                  {myId == item?.userId?._id ? (
                    <MenuOption
                      onSelect={() => {
                        navigation.navigate('Common', {
                          screen: 'edit-topic',
                          params: {
                            topicData: item,
                          },
                        });
                      }}
                    >
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center', // Ensures vertical alignment
                          marginBottom: rv(10),
                        }}
                      >
                        <EditTopicSVG width={20} />
                        <CustomText
                          style={{
                            color: 'black',
                            fontSize: rv(12),
                            marginLeft: rv(5),
                            fontFamily: 'medium',
                            // Consistent margin between icon and text
                          }}
                        >
                          Edit post
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}

                  {myId !== item?.userId?._id && (
                    <MenuOption>
                      <FollowButton userId={item.userId?._id} />
                    </MenuOption>
                  )}

                  <MenuOption onSelect={() => muteNotification()}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginBottom: rv(10),
                      }}
                    >
                      {item.muted ? (
                        <UnMuteSVG width={20} />
                      ) : (
                        <MuteGroupSVG width={20} />
                      )}
                      <CustomText
                        style={{
                          color: 'black',
                          fontSize: rv(12),
                          marginLeft: rv(5),
                          fontFamily: 'medium',
                          // Consistent margin
                        }}
                      >
                        {item.muted ? 'Unmute post' : 'Mute post'}
                      </CustomText>
                    </View>
                  </MenuOption>
                  {myId == item?.userId?._id ? (
                    <MenuOption onSelect={() => deleteFromTopic()}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginBottom: rv(10),
                        }}
                      >
                        <DeleteSVG width={20} height={20} />
                        <CustomText
                          style={{
                            color: 'red',
                            fontSize: rv(12),
                            marginLeft: rv(5),
                            fontFamily: 'medium',
                            // Consistent margin
                          }}
                        >
                          Delete post
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}

                  {convo ? (
                    <MenuOption onSelect={() => removeFromConversation()}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginBottom: rv(10),
                        }}
                      >
                        {/* <Ionicons name='flag' size={20} /> */}
                        <FlagSVG width={20} />
                        <CustomText
                          style={{
                            color: 'black',
                            fontSize: rv(12),
                            marginLeft: rv(5), // Consistent margin
                            fontFamily: 'medium',
                          }}
                        >
                          {t('convos_SubMenu')}
                        </CustomText>
                      </View>
                    </MenuOption>
                  ) : null}
                  {myId !== item?.userId?._id && (
                    <MenuOption
                      onSelect={() => openReportDetails(item, 'topic')}
                    >
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}
                      >
                        <FlagSVG width={20} />
                        <CustomText
                          style={{
                            color: 'black',
                            fontSize: rv(12),
                            marginLeft: rv(5),
                            fontFamily: 'medium',
                            // Consistent margin
                          }}
                        >
                          {t('Topics_flag')}
                        </CustomText>
                      </View>
                    </MenuOption>
                  )}
                </View>
              </MenuOptions>
            </Menu>
          </View>
        </View>

        <View style={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={2}
            style={{
              flexDirection: 'row',
              width: '80%',
              fontSize: rv(12),
              color: '#9D9D9D',
              fontFamily: 'medium',
            }}
          >
            {item.userId?.company_position && item.userId.company
              ? `${
                  item.userId.company_position?.replace(/(\r\n|\n|\r)/gm, '') ||
                  'position'
                } at ${item.userId.company}`
              : 'Position at Company'}
          </CustomText>
         
        </View>
        <View style={{ width: '100%' }}>
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={2}
            style={{
              flexDirection: 'row',
              width: '80%',
              fontSize: rv(12),
              color: '#9D9D9D',
              fontFamily: 'medium',
            }}
          >
            {item.userId?.subscriptionType
              ? `${
                  item.userId.subscriptionType
                    .charAt(0)
                    .toUpperCase() +
                  item.userId.subscriptionType.slice(1)
                } Member`
              : 'Free Member'}
          </CustomText>
        </View>

        <View
          style={{
            alignItems: 'flex-start',
          }}
        >
          <CustomText
            adjustsFontSizeToFit={false}
            numberOfLines={1}
            style={{
              fontSize: rv(12),
              color: '#9D9D9D',
              flexWrap: 'nowrap',
              fontFamily: 'medium',
            }}
          >
            {timeAgo(item.createdAt)}
          </CustomText>
        </View>
        <View
          style={{
            marginRight: wp('5%'),
          }}
        >
          <View
            style={{
              flexDirection: 'column',
            }}
          >
            <CustomText
              style={{
                fontSize: rv(13),
                color: '#636363',
                lineHeight: 20,
                fontFamily: 'medium',
              }}
            >
              {item.content}
            </CustomText>

            {item.image ? (
              <TouchableOpacity
                style={{
                  flex: 1,
                  width: '100%',
                }}
                onPress={() =>
                  navigation.push('Common', {
                    screen: 'fullscreen-image',
                    params: {
                      image: item.image,
                    },
                  })
                }
              >
                <Image
                  style={{
                    width: '100%',
                    height: hp('30%'),
                    borderRadius: wp('2%'),
                    borderColor: 'white',
                    borderWidth: wp('0.6%'),
                    marginTop: rv(4),
                  }}
                  source={{ uri: item.image }}
                />
              </TouchableOpacity>
            ) : null}
            {item.audio ? (
              <View style={styles.attachment}>
                <Audio
                  attachmentSize={item.attachmentSize}
                  link={item.audio}
                  navigation={navigation}
                />
              </View>
            ) : null}

            {item.video ? (
              <View style={styles.attachment}>
                <Video
                  attachmentSize={item.attachmentSize}
                  link={item.video}
                  navigation={navigation}
                />
              </View>
            ) : null}

            {item.document ? (
              <View style={styles.attachment}>
                <Document
                  attachmentSize={item.attachmentSize}
                  link={item.document}
                  navigation={navigation}
                />
              </View>
            ) : null}
          </View>
        </View>
        <View
          style={{
            flexDirection: 'row',
            marginTop: 10,
            marginRight: wp('5%'),
            justifyContent: 'space-between',
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <CommentSVG
              color={'#5A5E5C'}
              style={{
                marginRight: 5,
              }}
            />

            <CustomText
              style={{
                color: '#5A5E5C',
                marginLeft: 3,
                fontSize: rv(11),
                paddingTop: 6,
                fontFamily: 'regular',
              }}
            >
              {item.noOfComments} {t('Topic_Comments')}
              {item.noOfComments > 1 ? 's' : ''}
            </CustomText>
          </View>

          <TouchableOpacity
            onPress={() => handleLike()}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <ThumbSVG style={{}} color={isLiked ? '#FE3233' : '#5A5E5C'} />

            <CustomText
              style={{
                fontSize: rv(11),
                marginLeft: 5,
                marginRight: 40,
                marginTop: 4,
                color: isLiked ? '#FE3233' : '#5A5E5C',
                fontFamily: 'regular',
              }}
            >
              {numLikes} {t('comments_likes')}
              {numLikes > 1 ? 's' : ''}
            </CustomText>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  title: {
    textAlign: 'center',
    marginVertical: 8,
  },
  fixToText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  attachment: {
    marginVertical: rv(8),
    width: wp('68%'),
  },
  separator: {
    marginVertical: 8,
    borderBottomColor: '#737373',
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  signup: {
    height: 60,
    borderColor: '#D0CFD0',
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: '#ffffff',
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
  },
  replycomment: {
    flex: 1,
    flexDirection: 'row',
    height: 45,
    borderColor: '#D0CFD0',
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: '#D1D1D1',
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
    position: 'relative',
  },
  reply: {
    flexDirection: 'row',
    width: '90%',
    justifyContent: 'space-around',
    borderColor: '#D0CFD0',
    borderWidth: 1,
    borderRadius: 10,
    paddingLeft: 20,
    backgroundColor: '#ffffff',
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
  },
  medicalinput: {
    height: 50,
    borderColor: '#f8f8fa',
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: '#f0f0f0',
  },
  medicalinputarea: {
    height: 120,
    borderColor: '#f8f8fa',
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'flex-start',
  },
  icontext: {
    paddingTop: 30,
    color: 'gray',
    fontSize: rv(13),
    fontWeight: '200',
    textAlign: 'center',
    fontFamily: 'medium',
    // fontFamily: 'Segoe UI',
  },
  profilename: {
    fontFamily: 'medium',
    // fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: '600',
    fontSize: rv(13),
    lineHeight: 27,
  },
  touchbtn: {
    backgroundColor: 'purple',
    height: 50,
    width: 150,
    borderColor: 'purple',
    borderWidth: 2,
    borderRadius: 33,
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 10,
  },
  consultview: {
    flex: 1,
    width: 500,
    flexDirection: 'row',
    alignItems: 'stretch',
    borderStyle: 'solid',
    borderBottomColor: '#F8F8F8',
    borderBottomWidth: 2,
    paddingTop: 10,
    marginLeft: 16,
    paddingBottom: 10,
  },
  medicalview: {
    flex: 1,
    flexDirection: 'column',
    borderStyle: 'solid',
    paddingTop: 10,
    marginLeft: 16,
    marginRight: 17,
  },
  scheduleview: {
    flex: 1,
    flexDirection: 'column',
    alignContent: 'stretch',
    marginLeft: 16,
    marginRight: 17,
  },
  slide: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'blue',
  },
  image: {
    width: 300,
    height: 481,
    // marginVertical: 12,
  },
  text: {
    color: '#707070',
    // textAlign: 'center',
    fontSize: wp('4%'),
    width: '87%',
    // fontFamily: 'Helvetica',
    marginLeft: wp('4%'),
    fontFamily: 'medium',
  },
  wrapperText: {
    color: '#707070',
    // textAlign: 'center',
    fontSize: wp('4%'),
    width: '87%',
    // fontFamily: 'Helvetica',
    // marginVertical: hp('1%')
    // marginLeft: wp('1%')
    fontFamily: 'medium',
  },
  splashbtn: {
    alignContent: 'center',
    alignSelf: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    height: 60,
    width: 300,
    borderColor: 'black',
    borderWidth: 2,
    marginTop: 1,
    marginBottom: 20,
    borderRadius: 30,
    paddingTop: 25,
    fontStyle: 'normal',
    fontFamily: 'bold',
    fontSize: rv(13),
    lineHeight: 14,
    textAlign: 'center',
    color: '#163E23',
    display: 'flex',
    marginLeft: 20,
    marginRight: 20,
  },
  titleAccount: {
    fontSize: rv(13),
    color: 'black',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'medium',
  },
  errorTextStyle: {
    color: 'red',
    textAlign: 'center',
    fontSize: rv(13),
  },
  tabStyle: {},
  scrollStyle: {
    backgroundColor: 'white',
    paddingLeft: 65,
    paddingRight: 65,
    // justifyContent: 'center',
  },
  tabBarTextStyle: {
    fontSize: rv(13),
    fontFamily: 'medium',
    fontWeight: 'normal',
  },
  underlineStyle: {
    height: 3,
    backgroundColor: 'red',
    borderRadius: 3,
    width: 15,
  },
  topbar: {
    flexDirection: 'row',
    backgroundColor: 'dimgray',
    paddingTop: 15,
  },
  trigger: {
    padding: 5,
    margin: 5,
  },
  triggerText: {
    color: 'white',
  },
  disabled: {
    color: '#ccc',
  },
  divider: {
    marginVertical: 5,
    marginHorizontal: 2,
    borderBottomWidth: 1,
    borderColor: '#ccc',
  },
  logView: {
    flex: 1,
    flexDirection: 'column',
  },
  logItem: {
    flexDirection: 'row',
    padding: 8,
  },
  slideInOption: {
    padding: 5,
  },
  bottom: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  touchableOpacityStyle: {
    position: 'absolute',
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    right: 30,
    bottom: 30,
  },
  floatingButtonStyle: {
    resizeMode: 'contain',
    width: '125%',
    height: '125%',
    //backgroundColor:'black'
  },
  actionButtonIcon: {
    fontSize: 20,
    height: 22,
    color: 'white',
  },
  profile: {
    height: 60,
    borderColor: '#C2C2C2',
    borderWidth: 0.5,
    borderRadius: 5,
    paddingLeft: 20,
    backgroundColor: '#FBFBFB',
    marginBottom: 10,
  },
  topicStyle: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: '#f6f6f6',
    paddingTop: 20,
    paddingLeft: 10,
    borderBottomColor: '#EBEBEB',
    borderBottomWidth: 1,
    marginTop: 10,
    marginRight: 10,
    marginLeft: 10,
    borderRadius: 15,
  },
});

export default TopicCard;

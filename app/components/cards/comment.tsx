import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import React, { useEffect, useState } from 'react';
import Avatar from '../elements/Avater';
import { CustomText } from '../elements';
import { timeAgo } from 'app/helpers/time-ago';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from 'react-native-popup-menu';
import Ionicons from '@expo/vector-icons/Ionicons';
import ReportDialog from 'app/components/dialogs/report';
import { Axios } from 'app/api/axios';
import ThumbSVG from '../svgReactComponent/ThumSVG';
import CommentSVG from '../svgReactComponent/CommentSVG';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { default as themeFont } from 'app/assets/themes/fonts.json';
import { Image } from 'expo-image';
import Document from '../attachments/Document';
import Audio from '../attachments/Audio';
import Video from '../attachments/Video';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import MenuSVG from 'app/assets/svg/menu.svg';
import { useSelector } from 'react-redux';
import { userId } from 'app/redux/user/reducer';
import { useTopicSubComments } from 'app/redux/topic/hooks';
import FlagSVG from 'app/assets/svg/flagasInapp.svg';
import { showModal, hideModal } from 'app/providers/modals';
import { DeleteSVG } from 'app/providers/svg/loader';
import { topic } from 'app/api';
import ReplySVG from '@/app/assets/svg/Reply.svg'
const Loading = require('../../assets/loading.gif');

interface ICommentCard {
  item: any;
  refreshFunction?: () => void;
  navigation: any;
  setSubCommentData: any;
}

const CommentCard = ({
  item,
  refreshFunction = () => {},
  navigation,
  setSubCommentData,
}: ICommentCard) => {
  const { SlideInMenu } = renderers;

  const [isLiked, setIsLiked] = useState(false);
  const [numLikes, setNumLikes] = useState(0);
  const myId = useSelector(userId);
  const [commentId, setCommentId] = useState<any>(null);
  const { data: subComments, isLoading: loadingSubComments,  } =
    useTopicSubComments(commentId);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});

  useEffect(() => {
    if (item.isLiked && item.likes) {
      setIsLiked(item.isLiked);
      setNumLikes(item.likes);
    }
  }, [item]);

  function openReportDetails(item: any, type: any) {
    setReportDetails({
      data: { _id: item },
      type,
    });
    setShowReportDialog(true);
  }

  function handleCommentLike({ commentId, commentType }: any) {
    setIsLiked(!isLiked);
    setNumLikes(isLiked ? numLikes - 1 : numLikes + 1);

    Axios({
      method: 'post',
      url: '/topics/like-topic-comment/' + commentId + '?type=' + commentType,
    })
      .then((response) => {
        refreshFunction();
        // refetchComment();
      })
      .catch(() => {
        setIsLiked(!isLiked);
        setNumLikes(isLiked ? numLikes + 1 : numLikes - 1);
      });
  }

  async function deleteComments() {
    topic.deleteComment(item._id).then((data) => {
     
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Comment deleted',
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
         
          hideModal();
        },
        handleAlert: () => {
        ;
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }
  async function deleteSubComments(subCommentId:any) {
    topic.deleteSubComment(subCommentId).then((data) => {
    
      showModal({
        modalVisible: true,
        title: 'Alert',
        message: 'Delete successfully',
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
         
          hideModal();
        },
        handleAlert: () => {
       
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'info',
      //   className: 'Success',
      //   message: 'Removed Successfully',
      // });
      refreshFunction!();
    });
  }

  return (
    <View
      style={{
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        paddingTop: rv(2),
        borderBottomColor: '#EBEBEB',
        // borderBottomWidth: 1,
        paddingHorizontal: 20,
      }}
    >
      <View style={{ width: '20%' }}>

        <TouchableOpacity
          onPress={() => {
            if (item?.userId?._id != myId) {
              navigation.push('Common', {
                screen: 'private-chat',
                params: {
                  postid: item.userId.first_name + ' ' + item.userId.last_name,
                  userDetails: item.userId,
                },
              });
            }
          }}
        >
          
          <ReportDialog
            show={showReportDialog}
            setShow={setShowReportDialog}
            reportDetails={reportDetails}
          />

          <View
            style={{
              marginBottom: 10,
              marginLeft: 10,
              marginRight: 10,
            }}
          >
            <Avatar source={item.userId.profile_picture} size={rv(40)} />
          </View>
        </TouchableOpacity>
      </View>
      <View
        style={{
          width: '80%',
        }}
      >
        <View
          style={{
            width: '100%',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            // marginBottom: 10,
            flex: 1,
          }}
        >
          <View style={{ width: '80%' }}>
            <TouchableOpacity
              onPress={() =>
                navigation.push('Common', {
                  screen: 'private-chat',
                  params: {
                    userDetails: item.userId,
                  },
                })
              }
              style={{
                justifyContent: 'center',
              }}
            >
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={1}
                style={{
                  color: '#000000',
                  fontSize: rv(13),
                  fontFamily: 'bold'

                }}
                // textType='bold'
              >
                {item.userId.first_name + ' ' + item.userId.last_name}
              </CustomText>
            </TouchableOpacity>

            <View style={{ width: '100%' }}>
              <CustomText
                adjustsFontSizeToFit={false}
                numberOfLines={2}
                ellipsizeMode='tail'
                style={{
                  // flexDirection: 'row',

                  // width: '80%',
                  fontSize: rv(12),
                  color: '#9D9D9D',
                  fontFamily: 'medium'

                }}
              >
                {item.userId.company_position && item.userId.company
                  ? `${
                      item.userId.company_position?.replace(
                        /(\r\n|\n|\r)/gm,
                        ''
                      ) || 'company'
                    } at ${item.userId.company}`
                  : 'Position at company'}
              </CustomText>
            </View>
            <View
              style={{
                width: '80%',
                alignItems: 'flex-start',
              }}
            >
              <CustomText
                adjustsFontSizeToFit={true}
                numberOfLines={1}
                style={{
                  fontSize: rv(11),
                  color: '#9D9D9D',
                  flexWrap: 'nowrap',
                }}
              >
                {timeAgo(item.createdAt)}
              </CustomText>
            </View>
          </View>
          <View
            style={{
              alignItems: 'flex-end',
              flexDirection: 'row',
              // paddingLeft: 20,
            }}
          >
            <Menu
              renderer={SlideInMenu}
              // onSelect={(value) => {
              //     //onMenuClicked(value)
              // }}
            >
              <MenuTrigger>
                <View
                  style={{
                    alignItems: 'flex-end',
                    flexDirection: 'row',
                    padding: rv(3),
                  }}
                >
                  <MenuSVG width={15} height={15} />
                </View>
              </MenuTrigger>

              <MenuOptions
                customStyles={{
                  optionText: [styles.text],
                  optionsContainer: [
                    {
                      borderRadius: 15,
                    },
                  ],
                }}
              >
                <View
                  style={{
                    margin: 5,
                    flexDirection: 'column',
                    marginVertical: 10,
                    padding: 15,
                  }}
                >
                 { item.userId._id === myId ? 
                  <MenuOption onSelect={() => deleteComments()}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginBottom: rv(10),
                          marginHorizontal: 10,
                        }}
                      >
                        <DeleteSVG width={20} height={20}/>
                        <CustomText
                          style={{
                            fontFamily: 'medium',

                            color: 'black',
                            fontSize: rv(13),
                            marginLeft: rv(5), // Consistent margin
                          }} 
                        >
                         Delete comment
                        </CustomText>
                      </View>
                    </MenuOption> : null} 
                    {item.userId._id === myId ?    
                  <MenuOption
                    onSelect={() => {
                      openReportDetails(item._id, 'comment');
                    }}
                  >
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '100%',
                        alignItems: 'center',
                        marginHorizontal: 10,
                      }}
                    >
                      <FlagSVG width={20} />
                      <CustomText
                        style={{
                          color: 'black',
                          fontSize: rv(12),
                          paddingLeft: rv(8),
                          fontFamily: 'medium'

                        }}
                      >
                        Flag comment as inappropriate
                      </CustomText>
                    </View>
                  </MenuOption> : null}
             
                </View>
              </MenuOptions>
            </Menu>
          </View>
        </View>
        {/* {item.updatedAt} */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: '95%' }}>
            <CustomText
              style={{
                fontSize: rv(12),
                color: '#636363',
                lineHeight: 20,
                fontFamily: 'medium'

              }}
            >
              {item.comment}
            </CustomText>
          </View>
        </View>

        {/* Comment Medias */}

        {item.image ? (
          <TouchableOpacity
            style={styles.attachment}
            onPress={() =>
              navigation.push('Common', {
                screen: 'fullscreen-image',
                params: {
                  image: item.image,
                },
              })
            }
          >
            <Image
              source={{
                uri: item.image,
              }}
              style={{
                height: hp('30%'),
                borderRadius: wp('2%'),
                borderColor: 'white',
                borderWidth: wp('0.6%'),
                flex: 1,
                flexDirection: 'column',
              }}
              resizeMode='cover'
            />
          </TouchableOpacity>
        ) : null}
        {item.document ? (
          <View style={styles.attachment}>
            <Document
              attachmentSize={item.attachmentSize}
              link={item.document}
              navigation={navigation}
            />
          </View>
        ) : null}

        {item.audio ? (
          <View style={styles.attachment}>
            <Audio
              attachmentSize={item.attachmentSize}
              link={item.audio}
              navigation={navigation}
            />
          </View>
        ) : null}

        {item.video ? (
          <View style={styles.attachment}>
            <Video
              attachmentSize={item.attachmentSize}
              link={item.video}
              navigation={navigation}
            />
          </View>
        ) : null}

        <View
          style={{
            width: '90%',
            flexDirection: 'column',
            marginTop: rv(5),
            marginBottom: rv(5),
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: rv(5)
              }}
              hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
              onPress={() => {
                setSubCommentData({
                  id: item._id,
                  comment: item.comment,
                });
              }}
            >
            <ReplySVG width={30}/>

               <CustomText
                                style={{
                                    color: '#9D9D9D',
                                    marginLeft: 5,
                                    fontSize: 13,
                                    paddingTop: 6,
                                    fontFamily: 'medium'

                                }}
                            >
                                Reply
                            </CustomText> 
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setCommentId(item._id);
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <CommentSVG
                color={'#5A5E5C'}
                style={{
                  marginRight: 5,
                }}
              />

              <CustomText
                style={{
                  color: '#5A5E5C',
                  marginLeft: 3,
                  fontSize: rv(11),
                  paddingTop: 6,
                  fontFamily: 'regular'
                }}
              >
                {item.subCommentsCount} Comment
                {item.subCommentsCount > 1 ? 's' : ''}
              </CustomText>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                handleCommentLike({
                  commentId: item._id,
                  commentType: 'comment',
                });
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <ThumbSVG
               
                color={isLiked ? '#E64028' : '#5A5E5C'}
                style={{
                  marginRight: 5,
                }}
              />

              <CustomText
                style={{
                  fontSize: rv(11),
                  marginTop: 4,
                  color: isLiked ? '#E64028' : '#5A5E5C',
                  marginLeft: 3,
                  fontFamily: 'regular'
                }}
              >
                {numLikes}
                {/* Like{numLikes > 1 ? 's' : ''} */}
              </CustomText>
            </TouchableOpacity>
          </View>
        </View>

        {/* SubComment Section */}

        <View
          style={{
            flex: 1,
            marginVertical: rv(3),
            width: '100%',
          }}
        >
        {loadingSubComments ? (
  <View
    style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <Image
      source={Loading}
      style={{
        width: rv(50),
        height: rv(50),
      }}
    />
  </View>
) : (
  subComments && subComments.length > 0 ? (
    subComments.map((subComment: any, index: number) => (
      <View
        key={index}
        style={{
          flex: 1,
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          paddingTop: 5,
        }}
      >
        <View style={{ width: '15%' , marginRight: rv(10)}}>
          <TouchableOpacity
            style={{
              paddingRight: 3,
            }}
            onPress={() =>
              navigation.push('Common', {
                screen: 'private-chat',
                params: {
                  postid:
                    subComment.userId.first_name +
                    ' ' +
                    subComment.userId.last_name,
                  userDetails: subComment.userId,
                },
              })
            }
          >
            <Avatar
              source={subComment.userId.profile_picture}
              size={30}
            />
          </TouchableOpacity>
        </View>

        <View style={{ width: '85%' }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'center',
              marginBottom: rv(3),
            }}
          >
            <TouchableOpacity
              style={{
                width: '60%',
                paddingTop: rv(2),
              }}
              onPress={() =>
                navigation.push('Common', {
                  screen: 'private-chat',
                  params: {
                    postid:
                      subComment.userId.first_name +
                      ' ' +
                      subComment.userId.last_name,
                    userDetails: subComment.userId,
                  },
                })
              }
            >
              <CustomText
                adjustsFontSizeToFit={true}
                numberOfLines={1}
                style={{
                  color: '#000000',
                  fontSize: rv(13),
                  fontFamily: 'bold'

                }}
                // textType='bold'
              >
                {subComment.userId.first_name +
                  ' ' +
                  subComment.userId.last_name}
              </CustomText>
              <View style={{ width: '100%' }}>
                <CustomText
                  adjustsFontSizeToFit={false}
                  numberOfLines={2}
                  ellipsizeMode='tail'
                  style={{
                    // flexDirection: 'row',
                    // width: '80%',
                    fontSize: rv(12),
                    color: '#9D9D9D',
                    fontFamily: 'medium'

                  }}
                >
                  {subComment.userId.company_position &&
                  subComment.userId.company
                    ? `${
                        subComment.userId.company_position?.replace(
                          /(\r\n|\n|\r)/gm,
                          ''
                        ) || 'company'
                      } at ${subComment.userId.company}`
                    : 'Position at Company'}
                </CustomText>
              </View>
              <View
                style={{
                  alignItems: 'flex-start',
                }}
              >
                <CustomText
                  adjustsFontSizeToFit={true}
                  numberOfLines={1}
                  style={{
                    fontSize: rv(11),
                    color: '#9D9D9D',
                    marginTop: 2,
                  }}
                >
                  {timeAgo(subComment.createdAt)}
                </CustomText>
              </View>
            </TouchableOpacity>

            <View
              style={{
                width: '40%',
                alignItems: 'flex-end',
                flexDirection: 'row',
                paddingHorizontal: 5,
              }}
            >
              <View
                style={{
                  width: '80%',
                  alignItems: 'flex-end',
                }}
              ></View>

              <View
                style={{
                  width: '20%',
                }}
              >
                <Menu
                  renderer={SlideInMenu}
                >
                  <MenuTrigger>
                    <View
                      style={{
                        alignItems: 'flex-end',
                        flexDirection: 'row',
                        padding: rv(3),
                      }}
                    >
                      <MenuSVG width={15} height={15} />
                    </View>
                  </MenuTrigger>

                  <MenuOptions
                    customStyles={{
                      optionText: [styles.text],
                      optionsContainer: [
                        {
                          borderRadius: 15,
                        },
                      ],
                    }}
                  >
                    <View
                      style={{
                        margin: 5,
                        flexDirection: 'column',
                        marginVertical: 10,
                        padding: 15,
                      }}
                    >
                      {subComment.userId._id === myId ? <MenuOption onSelect={() => deleteSubComments(subComment._id)}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginBottom: rv(10),
                          marginHorizontal: 10,
                        }}
                      >
                        <DeleteSVG width={20} height={20}/>
                        <CustomText
                          style={{
                            color: 'black',
                            fontSize: rv(13),
                            marginLeft: rv(5),
                            fontFamily: 'medium'

                             // Consistent margin
                          }} 
                        >
                         Delete Sub Comment
                        </CustomText>
                      </View>
                    </MenuOption> : null }
                     {subComment.userId._id === myId ? <MenuOption
                        onSelect={() => {
                          openReportDetails(
                            subComment._id,
                            'sub-comment'
                          );
                        }}
                      >
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '100%',
                            alignItems: 'center',
                            marginHorizontal: 10,
                          }}
                        >
                          <FlagSVG width={20} />
                          <CustomText
                            style={{
                              color: 'black',
                              fontSize: rv(12),
                              marginLeft: rv(5),
                              fontFamily: 'medium'

                            }}
                          >
                            Flag comment as inappropriate
                          </CustomText>
                        </View>
                      </MenuOption>: null }
                      
                    </View>
                  </MenuOptions>
                </Menu>
              </View>
            </View>
          </View>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'flex-start',
            }}
          >
            <View style={{ width: '95%' }}>
              <CustomText
                style={{
                  color: '#636363',
                  fontSize: rv(12),
                  fontFamily: 'medium'

                }}
              >
                {subComment.comment}
              </CustomText>
            </View>
          </View>
        </View>
      </View>
    ))
  ) : null
)}

        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  attachment: {
    marginTop: 8,
    width: wp('68%'),
  },
  text: {
    color: '#707070',
    fontSize: rv(13),
    padding: 4,
    width: '100%',
    fontFamily: 'regular'
  },
  title: {
    fontSize: rv(13),
    color: '#333333',
    fontFamily: 'bold',
    textAlign: 'center',
  },
  titleAccount: {
    fontSize: rv(13),
    color: 'black',
    fontFamily: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  errorTextStyle: {
    color: 'red',
    textAlign: 'center',
    fontSize: rv(13),
  },
  disabled: {
    color: '#ccc',
  },
  divider: {
    marginVertical: 5,
    marginHorizontal: 2,
    // borderBottomWidth: 1,
    // borderColor: '#ccc',
  },
  logView: {
    flex: 1,
    flexDirection: 'column',
  },
  logItem: {
    flexDirection: 'row',
    padding: 8,
  },
  slideInOption: {
    padding: 5,
  },
  bottom: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  touchableOpacityStyle: {
    flex: 1,
    flexDirection: 'row',
    position: 'absolute',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    // right: 30,
    bottom: 0,
  },
  floatingButtonStyle: {
    resizeMode: 'contain',
    width: '125%',
    height: '125%',
    //backgroundColor:'black'
  },
  actionButtonIcon: {
    fontSize: rv(13),
    height: 22,
    color: 'white',
  },
  profile: {
    height: 60,
    borderColor: '#C2C2C2',
    borderWidth: 0.5,
    borderRadius: 5,
    paddingLeft: 20,
    backgroundColor: '#FBFBFB',
    marginBottom: 10,
  },
});

export default CommentCard;

import { View, TouchableOpacity, StyleSheet } from 'react-native';
import React, { FunctionComponent, useEffect, useState } from 'react';
import Avatar from 'app/components/elements/Avater';
import { CustomText } from 'app/components/elements/Text';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
  renderers,
} from 'react-native-popup-menu';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { useSelector } from 'react-redux';
import { userId } from 'app/redux/user/reducer';
import ReportDialog from 'app/components/dialogs/report';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Axios } from 'app/api/axios';
import { ShowAlert } from 'app/providers/toast';
import { timeAgo } from 'app/helpers/time-ago';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import MenuSVG from 'app/assets/svg/menu.svg';
import { useTranslation } from 'react-i18next';
import MuteSVG from 'app/assets/svg/muteUser.svg';
import ReportGroupSVG from 'app/assets/svg/reportUser.svg';
import UnMuteSVG from 'app/assets/svg/unmuteUser.svg';
import DarkBlockedChatSVG from 'app/assets/svg/Block_chats.svg';

import { showModal, hideModal } from 'app/providers/modals';
import FlagSVG from 'app/assets/svg/flagasInapp.svg';
// import { DarkBlockedChatSVG } from 'app/providers/svg/loader';
import { useGetUnreadNotifications } from 'app/redux/main/hooks';
import ConvoNotificationIcon from '../convo-icon';
import { main } from 'app/api/main';

interface ChatProps {
  item: any;
  navigation: any;
  onToggleMuted?: () => {};
  refreshFunction?: () => {};
  route: any;
  count: number;
}

export const ChatCard: FunctionComponent<ChatProps> = ({
  item,
  route,
  navigation,
  refreshFunction,
  onToggleMuted,
}) => {
  const { SlideInMenu } = renderers;

  const myId = useSelector(userId);

  const [showReportDialog, setShowReportDialog] = useState(false);
  const [reportDetails, setReportDetails] = useState({});
  const { t } = useTranslation();
  const [isMuted, setIsMuted] = useState(item.muted);

  function openReportDetails(item: any) {
    setReportDetails({ data: { _id: item }, type: 'user' });
    setShowReportDialog(true);
  }
  console.log(item, 'item');


  async function removeFromList(accountId: string) {
    await Axios({
      method: 'patch',
      url: '/chats/remove-from-chat/' + accountId,
      data: {},
    }).then((response: any) => {
      showModal({
        modalVisible: true,
        title: 'Success',
        message: response.data.message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });

      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: response.data.message,
      // });
      refreshFunction!();
    });
  }

  async function blockUser(accountId: string) {
    await Axios({
      method: 'patch',
      url: '/chats/block/' + accountId,
      data: {},
    }).then((response: any) => {
      const message = response.data.message === 'Blocked successfully' ? 'User blocked' : response.data.message

      showModal({
        modalVisible: true,
        title: 'Success',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: response.data.message,
      // });
      refreshFunction!();
    });
  }

  async function unBlockUser(accountId: string) {
    await Axios({
      method: 'patch',
      url: '/chats/unblock/' + accountId,
      data: {},
    }).then((response: any) => {
      const message = response.data.message === 'Chat has been unblocked successfully' ? 'User unblocked' : response.data.message

      showModal({
        modalVisible: true,
        title: 'Success',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: response.data.message,
      // });
      refreshFunction!();
    });
  }

  function muteNotification(item: {
    _id: string;
    muted?: boolean;
    accountId: AccountProfileProp;
  })

  {
       const message = item.muted ? 'Chat unmuted': 'This chat has now been muted'

    Axios({
      method: 'post',
      url: '/app/mute-notifications/',
      data: {
        contentId: item.accountId._id,
        type: 'user',
        action: item.muted ? 'unmute' : 'mute',
      },
    }).then((response: any) => {
      showModal({
        modalVisible: true,
        title: 'Success',
        message: message,
        setModalVisible: hideModal, // Function to close the modal
        type: 'success-alert',
        handleConfirm: () => {
          console.log('Confirmed!');
          hideModal();
        },
        handleAlert: () => {
          console.log('Alert handled!');
          hideModal();
        },
        navigation,
      });
      // ShowAlert({
      //   type: 'success',
      //   className: 'Success',
      //   message: 'User has been muted successfully',
      // });
      refreshFunction!();
    });
  }

  const handleToggleMuted = () => {
    setIsMuted((prevMuted: any) => !prevMuted);
    if (onToggleMuted) {
      onToggleMuted();
    }
  };

  useEffect(() => {
    console.log('item', item);
  }, [item]);

  return (
    <TouchableOpacity
      style={styles.topicStyle}
      onPress={() =>
        navigation.push('Common', {
          screen: 'private-chat',
          params: {
            userDetails: item.accountId,
          },
        })
      }
    >
      <ReportDialog
        show={showReportDialog}
        setShow={setShowReportDialog}
        reportDetails={reportDetails}
      />
      <View>
        <View style={{ marginBottom: 10, marginLeft: 5, marginRight: 15 }}>
          <Avatar source={item?.accountId?.profile_picture} size={rv(45)} />
        </View>
      </View>
      <View style={{ width: '80%' }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginRight: wp('5%'),
          }}
        >
          <View style={{ width: '60%' }}>
            <TouchableOpacity
              onPress={() =>
                navigation.navigate('Common', {
                  screen: 'private-chat',
                  params: {
                    userDetails: item.accountId,
                  },
                })
              }
            >
              {item &&
              item.accountId &&
              item.accountId.first_name + item.accountId.last_name.length >
                12 ? (
                <CustomText
                  style={{
                    color: 'black',
                    fontSize: rv(12),
                    fontFamily: 'bold'

                  }}
                  // textType='bold'
                >
                  {item.accountId.first_name +
                    ' ' +
                    item.accountId.last_name.slice(0, 12)}
                  ...
                </CustomText>
              ) : (
                <CustomText
                  style={{
                    color: 'black',
                    fontSize: rv(13),
                    fontFamily: 'bold'

                  }}
                  // textType='bold'
                >
                  {item.accountId
                    ? item.accountId.first_name + ' ' + item.accountId.last_name
                    : null}
                </CustomText>
              )}
            </TouchableOpacity>
          </View>
          <Menu
            renderer={SlideInMenu}
            // onSelect={(value) => {
            //   //onMenuClicked(value)
            // }}
          >
            <MenuTrigger
            // customStyles={{
            // triggerTouchable: {
            //   padding: 100, // Add padding to increase the clickable area
            //   alignItems: 'center', // Center the content
            //   justifyContent: 'center',
            // }}}
            >
              <View
                style={{
                  alignItems: 'flex-end',
                  flexDirection: 'row',
                  paddingRight: rv(7),
                }}
              >
                <MenuSVG width={15} height={15} />
              </View>
            </MenuTrigger>

            <MenuOptions
              customStyles={{
                optionText: [styles.text],
                optionsContainer: [
                  {
                    borderRadius: 15,
                  },
                ],
              }}
            >
              <View
                style={{
                  margin: 5,
                  flexDirection: 'column',
                  marginVertical: 10,
                  padding: 15,
                }}
              >
                {/* <MenuOption
                  onSelect={() => {
                    removeFromList(item.accountId._id);
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      marginBottom: 10,
                      alignItems: 'center',
                    }}
                  >
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '100%',

                        alignItems: 'center',
                      }}
                    >
                      <FlagSVG width={20} />
                      <CustomText
                        style={{
                         color: 'black',
                          fontSize: rv(12),
                          marginLeft: rv(3),
                          fontFamily: 'medium',
                        }}
                      >
                        {t('convos_SubMenu')}
                      </CustomText>
                    </View>
                  </View>
                </MenuOption> */}

                <MenuOption
                  onSelect={() => {
                    if (item.blocked && item.blocked.includes(myId)) {
                      unBlockUser(item.accountId._id);
                    } else {
                      blockUser(item.accountId._id);
                    }
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      alignItems: 'center',
                      marginBottom: 10,
                    }}
                  >
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}
                    >
                      {item.blocked ? (
                        <DarkBlockedChatSVG width={18} />
                      ) : (
                        <DarkBlockedChatSVG width={18} />
                      )}
                    </View>
                    <CustomText style={{ fontSize: rv(13), marginLeft: rv(5), fontFamily: 'medium'
 }}>
                      {item.blocked && item.blocked.includes(myId)
                        ? t('BlockedChats_opt1')
                        : t('Chats_blockUser')}
                    </CustomText>
                  </View>
                </MenuOption>

                <MenuOption
                  onSelect={() => {
                    muteNotification(item);
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      alignItems: 'center',
                      marginBottom: 10,
                    }}
                  >
                    <View
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}
                    >
                      {item.muted ? (
                        <MuteSVG width={20} />
                      ) : (
                        <UnMuteSVG width={20} />
                      )}
                    </View>
                    <CustomText style={{ marginLeft: rv(5), fontSize: rv(12),             fontFamily: 'medium'
 }}>
                      {item.muted ? 'Unmute User' : 'Mute User'}
                    </CustomText>
                  </View>
                </MenuOption>

                <MenuOption
                  onSelect={() => {
                    openReportDetails(item.accountId._id);
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      width: '100%',
                      alignItems: 'center',
                      // marginHorizontal: 10,
                      gap: rv(5),
                    }}
                  >
                    <ReportGroupSVG width={20} />
                    <CustomText style={{ fontSize: rv(12),             fontFamily: 'medium'
 }}>
                      {t('Chats_reportUser')}
                    </CustomText>
                  </View>
                </MenuOption>
              </View>
            </MenuOptions>
          </Menu>
        </View>
        <View style={{}}>
          {item.accountId ? (
            <CustomText
              numberOfLines={2}
              style={{
                fontSize: rv(12),
                color: '#9D9D9D',
                flexWrap: 'nowrap',
                width: '80%',
                fontFamily: 'medium'

              }}
            >
              {item.accountId?.company_position && item.accountId?.company
                ? `${
                    item.accountId?.company_position?.replace(
                      /(\r\n|\n|\r)/gm,
                      ''
                    ) || 'Position'
                  } at ${item.accountId?.company}`
                : 'Position at Company'}
            </CustomText>
          ) : null}
        </View>
        <View style={{flexDirection: 'row', gap: rv(5)}}>
          {item.lastMessageDate ? (
            <CustomText
              style={{
                fontSize: rv(11),
                color: '#9D9D9D',
                flexWrap: 'nowrap',
                fontFamily: 'medium'

              }}
            >
              {timeAgo(item.lastMessageDate)}
            </CustomText>
          ) : null}
          <View>
            {item.unreadMessages ? (
              <View
                style={{
                  width: 20,
                  height: 20,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#163E23',
                  marginRight: 10,
                  borderRadius: 20,
                }}
              >
                <CustomText
                  style={{
                    color: '#F2875D',
                    // fontWeight: 'bold',
                    fontSize: rv(11),
                    fontFamily: 'bold'

                  }}
                >
                  {item.unreadMessages}
                </CustomText>
              </View>
            ) : null}
          </View>
        </View>

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
          }}
        >
          <View
            style={{
              width: '80%',
              alignItems: 'center',
              flexDirection: 'row',
              
            }}
          >
            {item?.lastMessage ? (
              <CustomText
                numberOfLines={2}
                style={{
                  color: '#636363',
                  fontSize: rv(13),
                  fontFamily: 'medium'

                }}
              >
                {item.lastMessage}
              </CustomText>
            ) : item.type ? (
              <CustomText
                ellipsizeMode='tail'
                numberOfLines={2}
                style={{
                  color: '#636363',
                  fontSize: rv(13),
                  fontFamily: 'medium'

                }}
              >
                File shared with you
              </CustomText>
            ) : null}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: 16,
  },
  title: {
    textAlign: 'center',
    marginVertical: 8,
    fontSize: rv(13),
    color: '#333333',
    fontFamily: 'bold'
  },
  fixToText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  separator: {
    marginVertical: 8,
    borderBottomColor: '#737373',
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  signup: {
    height: 60,
    borderColor: '#D0CFD0',
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: '#ffffff',
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
  },
  replycomment: {
    flex: 1,
    flexDirection: 'row',
    height: 45,
    borderColor: '#D0CFD0',
    borderWidth: 1,
    borderRadius: 40,
    paddingLeft: 20,
    backgroundColor: '#D1D1D1',
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
    position: 'relative',
  },
  reply: {
    flexDirection: 'row',
    width: '90%',
    justifyContent: 'space-around',
    borderColor: '#D0CFD0',
    borderWidth: 1,
    borderRadius: 10,
    paddingLeft: 20,
    backgroundColor: '#ffffff',
    marginBottom: 15,
    borderStyle: 'solid',
    marginTop: 10,
  },
  medicalinput: {
    height: 50,
    borderColor: '#f8f8fa',
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: '#f0f0f0',
  },
  medicalinputarea: {
    height: 120,
    borderColor: '#f8f8fa',
    borderWidth: 2,
    marginBottom: 20,
    borderRadius: 33,
    paddingLeft: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'flex-start',
  },
  icontext: {
    paddingTop: 30,
    color: 'gray',
    fontSize: rv(13),
    fontWeight: '200',
    textAlign: 'center',
    fontFamily: 'medium'

    // fontFamily: 'Segoe UI',
  },
  profilename: {
    // fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontFamily: 'semiBold',
    fontSize: rv(13),
    lineHeight: 27,
  },
  touchbtn: {
    backgroundColor: 'purple',
    height: 50,
    width: 150,
    borderColor: 'purple',
    borderWidth: 2,
    borderRadius: 33,
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 10,
  },
  consultview: {
    flex: 1,
    width: 500,
    flexDirection: 'row',
    alignItems: 'stretch',
    borderStyle: 'solid',
    borderBottomColor: '#F8F8F8',
    borderBottomWidth: 2,
    paddingTop: 10,
    marginLeft: 16,
    paddingBottom: 10,
  },
  medicalview: {
    flex: 1,
    flexDirection: 'column',
    borderStyle: 'solid',
    paddingTop: 10,
    marginLeft: 16,
    marginRight: 17,
  },
  scheduleview: {
    flex: 1,
    flexDirection: 'column',
    alignContent: 'stretch',
    marginLeft: 16,
    marginRight: 17,
  },
  slide: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'blue',
  },
  image: {
    width: 400,
    height: 481,
    // marginVertical: 12,
  },
  text: {
    color: '#707070',
    textAlign: 'center',
    fontSize: rv(13),
    width: '87%',
    fontFamily: 'medium'

    // fontFamily: 'Helvetica',
  },
  splashbtn: {
    alignContent: 'center',
    alignSelf: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    height: 60,
    width: 300,
    borderColor: 'black',
    borderWidth: 2,
    marginTop: 1,
    marginBottom: 20,
    borderRadius: 30,
    paddingTop: 25,
    fontStyle: 'normal',
    // fontWeight: 'bold',
    fontSize: rv(13),
    lineHeight: 14,
    textAlign: 'center',
    color: '#163E23',
    display: 'flex',
    marginLeft: 20,
    marginRight: 20,
    fontFamily: 'bold'

  },
  titleAccount: {
    fontSize: rv(13),
    color: 'black',
    fontFamily: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  errorTextStyle: {
    color: 'red',
    textAlign: 'center',
    fontSize: rv(13),
  },
  tabStyle: {},
  scrollStyle: {
    backgroundColor: 'white',
    paddingLeft: 65,
    paddingRight: 65,
    // justifyContent: 'center',
  },
  tabBarTextStyle: {
    fontSize: rv(13),
    fontFamily: 'medium'

  },
  underlineStyle: {
    height: 3,
    backgroundColor: 'red',
    borderRadius: 3,
    width: 15,
  },
  topbar: {
    flexDirection: 'row',
    backgroundColor: 'dimgray',
    paddingTop: 15,
  },
  trigger: {
    padding: 5,
    margin: 5,
  },
  triggerText: {
    color: 'white',
  },
  disabled: {
    color: '#ccc',
  },
  divider: {
    marginVertical: 5,
    marginHorizontal: 2,
    borderBottomWidth: 1,
    borderColor: '#ccc',
  },
  logView: {
    flex: 1,
    flexDirection: 'column',
  },
  logItem: {
    flexDirection: 'row',
    padding: 8,
  },
  slideInOption: {
    padding: 5,
  },
  bottom: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  touchableOpacityStyle: {
    position: 'absolute',
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    right: 30,
    bottom: 30,
  },
  floatingButtonStyle: {
    resizeMode: 'contain',
    width: '125%',
    height: '125%',
    //backgroundColor:'black'
  },
  actionButtonIcon: {
    fontSize: 20,
    height: 22,
    color: 'white',
  },
  profile: {
    height: 60,
    borderColor: '#C2C2C2',
    borderWidth: 0.5,
    borderRadius: 5,
    paddingLeft: 20,
    backgroundColor: '#FBFBFB',
    marginBottom: 10,
  },
  topicStyle: {
    // flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: 'white',
    paddingVertical: rv(8),
    paddingLeft: rv(20),
  },
});

import React,{useEffect} from 'react';
import { TouchableOpacity, View, Image } from 'react-native';
import { createDrawerNavigator } from '@react-navigation/drawer';
// import Icon from 'react-native-vector-icons/FontAwesome';
// import { default as themeFont } from 'app/assets/themes/fonts.json';
import { IsLoggedIn } from 'app/redux/user/reducer';
import { useSelector } from 'react-redux';
import { BottomTabStack } from 'app/navigation/home/<USER>';
import Sidebar from './sidebar';
import Profile from 'app/screens/drawer/profile';
import PostTopic from 'app/screens/drawer/post-topic';
import CreateGroup from 'app/screens/drawer/create-group';
import SettingsStackNavigator from './settings';
import HelpDesk from 'app/screens/drawer/helpdesk';
import EditProfile from 'app/screens/common/edit-profile';
import HelpdeskStackNavigator from './helpdesk';
import CreditScreen from 'app/screens/drawer/credit';
import { useTranslation } from 'react-i18next';
import { responsiveValue as rv } from 'app/providers/responsive-value';
import Topic from 'app/screens/bottom/topic';


export default function DrawerNavigator() {
  const { t } = useTranslation();
  const Drawer = createDrawerNavigator();
  const isLoggedIn = useSelector(IsLoggedIn);
  
  return (
    <Drawer.Navigator
      initialRouteName={'Quiz'}
      defaultStatus='closed'
      screenOptions={{
        drawerStyle: {
          backgroundColor: 'white',
          width: 300,
        },
      }}
      drawerContent={(props) => {
        const filteredProps = {
          state: {
            ...props.state,
          },
          navigation: props.navigation, 
        };
        return <Sidebar props={filteredProps} />;
      }}
    >
      <Drawer.Screen
        name='Quiz'
        options={{
          title: 'Quiz',
          headerShown: false,
          drawerIcon: ({ focused, size }) => (
            <Image
              source={require('app/assets/person-outline.png')} // Use your desired icon
              style={{ width: rv(18), height: rv(20) }}
            />
          ),
        }}
        component={BottomTabStack} // Link to BottomTabStack
      />

      <Drawer.Screen
        name="Profile"
        options={{
          title: "Profile",
          headerShown: false,
        }}
        component={Profile}
      />

      

      <Drawer.Screen
        name='PostTopic'
        options={{
          title: 'PostTopic',
          headerShown: false,
        }}
        component={PostTopic}
      />
  
      <Drawer.Screen
        name='CreateGroup'
        options={{
          title: 'CreateGroup',
          headerShown: false,
        }}
        component={CreateGroup}
      />

      <Drawer.Screen
        name='Settings'
        options={{
          title: t('SideNav_settings'),
          headerShown: false,
        }}
        component={SettingsStackNavigator}
      />

      <Drawer.Screen
        name='Credits'
        options={{
          title: t('Credits_Title'),
          headerShown: false,
        }}
        component={CreditScreen}
      />
    </Drawer.Navigator>
  );
}


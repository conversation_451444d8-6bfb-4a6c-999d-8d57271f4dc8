import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AreaOfInterest from 'app/screens/drawer/settings/area-of-interests';
import Profile from 'app/screens/drawer/profile';
import Settings from 'app/screens/drawer/settings';
import ChangeLanguage from 'app/screens/drawer/settings/change-language';
import ChangePin from 'app/screens/drawer/settings/change-pin';
import BlockedChats from 'app/screens/drawer/settings/blocked-chats';
import UserTerms from 'app/screens/drawer/settings/user-terms';
import About from 'app/screens/drawer/settings/about';
import { useTranslation } from 'react-i18next';
import Country from 'app/screens/drawer/settings/country';
import HelpDesk from 'app/screens/drawer/helpdesk';
import CreditScreen from 'app/screens/drawer/credit';
import CodeOfConduct from 'app/screens/drawer/settings/code-of-conduct';
import PrivacyPolicy from 'app/screens/drawer/settings/privacy-policy';
import KYCPolicy from 'app/screens/drawer/settings/kyc';
import CopyrightInfringementPolicy from 'app/screens/drawer/settings/copyright';
import APITerms from 'app/screens/drawer/settings/api-terms';
import EULA from 'app/screens/drawer/settings/eula';

const SettingsStack = createNativeStackNavigator();

function SettingsStackNavigator() {
  const { t } = useTranslation();
  return (
    <SettingsStack.Navigator
      screenOptions={{ headerShown: false }}
      initialRouteName={t('SideNav_settings')}
    >
      <SettingsStack.Screen name='settings' component={Settings} />
      <SettingsStack.Screen
        name='area-of-interest'
        component={AreaOfInterest}
      />
      <SettingsStack.Screen name='country' component={Country} />
      <SettingsStack.Screen name='change-language' component={ChangeLanguage} />
      <SettingsStack.Screen name='change-pin' component={ChangePin} />
      <SettingsStack.Screen name='blocked-chats' component={BlockedChats} />
      <SettingsStack.Screen name='user-terms' component={UserTerms} />
      <SettingsStack.Screen name='about' component={About} />
      <SettingsStack.Screen name='HelpDesk' component={HelpDesk} />
      <SettingsStack.Screen name='Credit' component={CreditScreen} />
      <SettingsStack.Screen name='code-of-conduct' component={CodeOfConduct} />
      <SettingsStack.Screen name='privacy-policy' component={PrivacyPolicy} />
      <SettingsStack.Screen name='kyc-policy' component={KYCPolicy} />
      <SettingsStack.Screen name='copyright' component={CopyrightInfringementPolicy} />
      <SettingsStack.Screen name='api-terms' component={APITerms} />
      <SettingsStack.Screen name='eula' component={EULA} />

    </SettingsStack.Navigator>
  );
}

export default SettingsStackNavigator;

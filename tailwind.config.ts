import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    screens: {
      'xs': '475px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      spacing: {
        'xs': 'var(--spacing-xs)',
        'sm': 'var(--spacing-sm)',
        'md': 'var(--spacing-md)',
        'lg': 'var(--spacing-lg)',
        'xl': 'var(--spacing-xl)',
        '2xl': 'var(--spacing-2xl)',
        // Layout specific spacing
        'sidebar': 'var(--layout-sidebar-width)',
        'content': 'var(--layout-content-width)',
        'right-sidebar': 'var(--layout-right-sidebar-width)',
        'layout-max': 'var(--layout-max-width)',
        'content-max': 'var(--layout-content-max-width)',
      },
      fontSize: {
        'xs': ['var(--text-xs)', { lineHeight: '1.4' }],
        'sm': ['var(--text-sm)', { lineHeight: '1.4' }],
        'base': ['var(--text-base)', { lineHeight: '1.5' }],
        'lg': ['var(--text-lg)', { lineHeight: '1.5' }],
        'xl': ['var(--text-xl)', { lineHeight: '1.6' }],
      },
      maxWidth: {
        'layout': 'var(--layout-max-width)',
        'content': 'var(--layout-content-width)',
        'content-max': 'var(--layout-content-max-width)',
        'sidebar': 'var(--layout-sidebar-width)',
        'right-sidebar': 'var(--layout-right-sidebar-width)',
      },
      width: {
        'sidebar': 'var(--layout-sidebar-width)',
        'content': 'var(--layout-content-width)',
        'right-sidebar': 'var(--layout-right-sidebar-width)',
      },
      colors: {
        background: 'var(--color-background)',
        foreground: 'var(--color-foreground)',
        primary: {
          DEFAULT: '#163E23',
          foreground: '#ffffff',
        },
        secondary: {
          DEFAULT: '#F2875D',
          foreground: '#163E23',
        },
        accent: {
          DEFAULT: '#F58634',
          foreground: '#163E23',
        },
        muted: {
          DEFAULT: '#797C7B',
          foreground: '#5A5E5C',
        },
        border: '#D9D9D9',
        input: '#D0D5DD',
        ring: '#F2875D',
        destructive: {
          DEFAULT: '#FE3233',
          foreground: '#ffffff',
        },
      },
      borderRadius: {
        'xs': '0.125rem',  /* 2px */
        'sm': '0.25rem',   /* 4px */
        'md': '0.375rem',  /* 6px */
        'lg': '0.5rem',    /* 8px */
        'xl': '2rem',      /* 32px */
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}

export default config

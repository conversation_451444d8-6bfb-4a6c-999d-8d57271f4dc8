{"name": "pennytots", "version": "4.2.0", "versionCode": "29", "main": "app/App", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/config-plugins": "~8.0.0", "@expo/prebuild-config": "~7.0.0", "@expo/react-native-action-sheet": "^4.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.3.1", "@react-native-community/viewpager": "5.0.11", "@react-native-picker/picker": "2.7.5", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@reduxjs/toolkit": "^1.9.5", "@shopify/flash-list": "1.6.4", "@tanstack/query-async-storage-persister": "^4.29.19", "@tanstack/react-query": "^4.29.19", "@tanstack/react-query-persist-client": "^4.29.19", "@types/react": "~18.2.79", "axios": "^1.4.0", "d3-shape": "^3.2.0", "date-fns": "^4.1.0", "debug": "^4.4.0", "expo": "^51.0.38", "expo-av": "~14.0.3", "expo-camera": "~15.0.14", "expo-checkbox": "~3.0.0", "expo-clipboard": "~6.0.3", "expo-constants": "~16.0.1", "expo-contacts": "~13.0.3", "expo-dev-client": "~4.0.29", "expo-device": "~6.0.2", "expo-document-picker": "~12.0.1", "expo-file-system": "~17.0.1", "expo-font": "~12.0.4", "expo-image": "~1.13.0", "expo-image-picker": "~15.1.0", "expo-location": "~17.0.1", "expo-media-library": "~16.0.3", "expo-notifications": "~0.28.1", "expo-sharing": "^12.0.1", "expo-splash-screen": "~0.27.4", "expo-status-bar": "~1.12.1", "expo-updates": "~0.25.10", "i18next": "^23.6.0", "moment": "^2.29.4", "react": "18.2.0", "react-flags-select": "^2.2.3", "react-i18next": "^13.3.1", "react-native": "0.74.5", "react-native-animatable": "^1.4.0", "react-native-country-picker-modal": "^2.0.0", "react-native-dropdown-country-picker": "^1.0.11", "react-native-dropdown-picker": "^5.4.6", "react-native-dropdown-select-list": "^2.0.4", "react-native-emoji-input": "^1.1.10", "react-native-gesture-handler": "~2.16.1", "react-native-gifted-chat": "^2.7.1", "react-native-hyperlink": "^0.0.22", "react-native-keyboard-controller": "^1.16.4", "react-native-paper": "^5.10.1", "react-native-picker-select": "^8.0.4", "react-native-popup-menu": "^0.16.1", "react-native-purchases": "^7.27.2", "react-native-raw-bottom-sheet": "^2.2.0", "react-native-reanimated": "~3.10.1", "react-native-responsive-screen": "^1.4.2", "react-native-root-toast": "^3.4.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-segmented-control-tab": "^4.0.0", "react-native-select-dropdown": "^3.3.4", "react-native-svg": "15.2.0", "react-native-toast-message": "^2.1.6", "react-native-webview": "13.8.6", "react-native-wheel-of-fortune": "^1.5.4", "react-navigation": "^4.4.4", "react-navigation-drawer": "^2.7.2", "react-navigation-stack": "^2.10.4", "react-redux": "^8.1.1", "redux": "^4.2.1", "redux-persist": "^6.0.0", "rn-emoji-keyboard": "^1.5.1", "socket.io-client": "^4.7.2", "typescript": "~5.3.3", "uuid": "^11.0.5", "world-countries": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.24.0", "react-native-get-random-values": "^1.11.0", "react-native-svg-transformer": "^1.5.0"}, "private": true, "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}